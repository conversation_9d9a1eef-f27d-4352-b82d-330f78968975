# Agent-Based Email Categorization System - Design Document

## Executive Summary

This document presents a comprehensive redesign of the email categorization system, transitioning from the current waterfall classification approach to an agent-based, tool-orchestrated architecture. The new system leverages pydantic-ai agents with composable tools, enabling context-aware decisions, multiple workflow triggers per email, and enhanced observability.

## Table of Contents
1. [Current State Analysis](#current-state-analysis)
2. [Proposed Architecture](#proposed-architecture)
3. [Data Models and Schemas](#data-models-and-schemas)
4. [Tool Architecture](#tool-architecture)
5. [Agent Implementation](#agent-implementation)
6. [Workflow Integration](#workflow-integration)
7. [Migration Strategy](#migration-strategy)
8. [Observability and Telemetry](#observability-and-telemetry)
9. [Testing Strategy](#testing-strategy)
10. [Open Decisions](#open-decisions)
11. [Implementation Timeline](#implementation-timeline)

## Current State Analysis

### Existing System Overview

The current email categorization system (`didero/ai/email_categorization/categorizer.py`) uses a two-stage waterfall classification:

1. **Stage 1**: High-level domain classification (PO_RELATED, QUOTATION_RELATED, etc.) with 90% confidence threshold
2. **Stage 2**: Detailed categorization within domains
3. **Validation**: Specific validators for acknowledgments and shipments
4. **Workflow Triggering**: One-to-one mapping between category and workflow

### Limitations

- **No Context Awareness**: Categorization happens without database context
- **Limited Extraction**: Only PO numbers are extracted; invoice numbers are ignored
- **Single Trigger**: One workflow per email, no support for multiple triggers
- **Static Flow**: Fixed waterfall approach with no adaptive behavior
- **Thread Blindness**: Email thread context is logged but not utilized

## Proposed Architecture

### Core Design Principles

1. **Agent-Based Orchestration**: A pydantic-ai agent decides which tools to call based on email content
2. **Tool Composability**: Stateless, pure functions that can be tested independently
3. **Context Accumulation**: Rich dependency object that builds context throughout processing
4. **Multiple Workflow Support**: Enable multiple workflow triggers from a single email
5. **Type Safety**: Pydantic models throughout with comprehensive validation

### High-Level Architecture

```
Email → Agent Orchestrator → Tools → Context Building → Decision Making → Workflow Triggers
         ↓                     ↓           ↓                ↓                    ↓
    [Decides Tools]    [Extract/Validate] [Enrich]    [Analyze]           [Execute]
```

### Decision Log (finalized)

The following implementation choices are locked for the first iteration:

- Binary filter: Reuse Stage‑1 classifier only at 95% threshold (no rules prepass)
- Invoice identifier extraction: Reuse full invoice extraction and read `invoice_number`
- Prompt placement: Add agent system prompt under `didero/ai/email_categorization/prompts_config.yaml`
- Multi‑trigger policy: Allow unlimited triggers; dedupe by `(workflow_type, entity_id)`
- Thread context: Optional; proceed best‑effort; log when missing
- Trigger ordering: Dependency‑based (PO Creation → OA → Shipment → Invoice → Shipping Docs)
- Rollout: Shadow mode first (log v2 decisions; v1 triggers)
- Behavior configuration: No new per‑team behavior config initially; reuse existing toggles and constants
- Idempotency: In‑run dedupe and cross‑run idempotency keys per `(email_id, workflow_type, entity_id)`

## Data Models and Schemas

### Core Dependencies Model

```python
# didero/ai/email_categorization/schemas.py

from dataclasses import dataclass, field
from typing import Dict, List, Optional
from pydantic import BaseModel, Field

@dataclass
class EmailCategorizationDependencies:
    """Dependencies container for email categorization agent"""
    # Input data
    email: "Communication"
    team_id: int
    email_text: str
    attachment_names: List[str]
    
    # Accumulated context during processing
    extracted_identifiers: Dict[str, List[str]] = field(default_factory=dict)
    po_contexts: Dict[str, "PurchaseOrderContext"] = field(default_factory=dict)
    invoice_contexts: Dict[str, "InvoiceContext"] = field(default_factory=dict)
    thread_context: Optional["ThreadContext"] = None
    
    # Processing state
    is_procurement_related: Optional[bool] = None
    relevance_confidence: float = 0.0
    workflow_decisions: List["WorkflowDecision"] = field(default_factory=list)
    
    # Configuration
    tolerance_config: Optional[Dict[str, Any]] = None
    behavior_config: Optional["EmailCategorizationBehaviorConfig"] = None
```

### Context Models

```python
class PurchaseOrderContext(BaseModel):
    """Enriched PO context with status-aware relations"""
    po_id: str
    po_number: str
    supplier_id: Optional[int]
    order_status: str  # PurchaseOrderStatus value
    
    # Core data
    po_data: "PurchaseOrderDetails"
    
    # Status-dependent relations
    acknowledgments: List["OrderAcknowledgement"] = Field(default_factory=list)
    shipments: List["Shipment"] = Field(default_factory=list)
    invoices: List["Invoice"] = Field(default_factory=list)
    
    # Metadata
    is_new: bool = False  # True if PO doesn't exist yet
    last_modified: Optional[datetime] = None

class InvoiceContext(BaseModel):
    """Enriched invoice context"""
    invoice_number: str
    invoice_data: Optional["InvoiceSchema"] = None
    linked_po: Optional[PurchaseOrderContext] = None
    is_new: bool = False
    supplier_id: Optional[int] = None

class ThreadContext(BaseModel):
    """Email thread context"""
    thread_id: str
    email_count: int
    recent_emails: List["EmailSummary"] = Field(default_factory=list)
    linked_pos: List[str] = Field(default_factory=list)
    linked_invoices: List[str] = Field(default_factory=list)
    
class WorkflowDecision(BaseModel):
    """Decision to trigger a workflow"""
    workflow_type: str  # WorkflowType enum value
    trigger: str  # WorkflowTrigger enum value
    confidence: float = Field(ge=0.0, le=1.0)
    reason: str
    context_data: Dict[str, Any] = Field(default_factory=dict)
    validation_passed: bool = True
    po_number: Optional[str] = None
    invoice_number: Optional[str] = None
```

### Agent Output Schema

```python
class EmailCategorizationResult(BaseModel):
    """Final output from the email categorization agent"""
    
    # Classification results
    is_procurement_related: bool
    primary_category: Optional[str] = None  # EmailCategory value
    workflow_decisions: List[WorkflowDecision]
    
    # Extracted data
    extracted_po_numbers: List[str] = Field(default_factory=list)
    extracted_invoice_numbers: List[str] = Field(default_factory=list)
    
    # Processing metadata
    confidence_score: float = Field(ge=0.0, le=1.0)
    processing_notes: List[str] = Field(default_factory=list)
    requires_manual_review: bool = False
    manual_review_reason: Optional[str] = None
    
    # Telemetry
    tools_used: List[str] = Field(default_factory=list)
    processing_time_ms: Optional[int] = None
```

### Configuration and Flags

Initial rollout will use existing team toggles and module constants rather than a new behavior config. Specifically:

- Global AI processing enablement (existing)
- Email categorization version toggle (v1 legacy vs v2 agent; per team)
- Module-level constants for thresholds (e.g., Stage‑1 relevance 95%), ordering, and limits

We will introduce a dedicated behavior config only if shadow‑mode metrics show a need for per‑team tunables (thresholds, thread requirements, etc.).

## Tool Architecture

### Tool Categories

Tools are organized into functional categories following the single responsibility principle:

```
didero/ai/email_categorization/tools/
├── __init__.py
├── extraction_tools.py      # Identifier and data extraction
├── enrichment_tools.py      # Database context enrichment
├── validation_tools.py      # Business rule validation
├── decision_tools.py        # Workflow trigger decisions
└── types.py                 # Shared parameter types
```

### Extraction Tools

```python
# didero/ai/email_categorization/tools/extraction_tools.py

async def extract_identifiers(
    ctx: RunContext[EmailCategorizationDependencies],
    include_po: bool = True,
    include_invoice: bool = True,
) -> Dict[str, List[str]]:
    """
    Extract PO and invoice numbers from email text and attachments.
    
    Returns:
        Dictionary with 'po_numbers' and 'invoice_numbers' lists
    """
    identifiers = {}
    
    if include_po:
        # Reuse existing PO extraction logic
        from didero.ai.email_categorization.po_extraction import (
            extract_purchase_orders_from_email
        )
        extracted_pos = extract_purchase_orders_from_email(ctx.deps.email)
        
        # Filter by confidence threshold
        config = ctx.deps.behavior_config
        threshold = config.po_extraction_confidence_threshold if config else 0.70
        
        po_numbers = [
            po.po_number for po in extracted_pos 
            if po.confidence >= threshold
        ][:config.max_identifiers_to_extract if config else 10]
        
        identifiers['po_numbers'] = po_numbers
        
    if include_invoice:
        # Reuse full invoice extraction and read invoice_number
        from didero.invoices.schemas import Invoice as InvoiceSchema  # schema type
        from didero.ai.invoice.extraction import ai_extract_invoice_details

        invoice = ai_extract_invoice_details(ctx.deps.email)
        invoice_numbers: List[str] = []
        threshold = (config.invoice_extraction_confidence_threshold if config else 0.70)

        if invoice and getattr(invoice, 'invoice_number', None):
            # Treat presence as high-confidence identifier; if schema exposes confidence, respect it
            invoice_numbers = [invoice.invoice_number]

        identifiers['invoice_numbers'] = invoice_numbers[: (config.max_identifiers_to_extract if config else 10)]
    
    # Store in dependencies for other tools
    ctx.deps.extracted_identifiers = identifiers
    
    return identifiers

async def extract_thread_context(
    ctx: RunContext[EmailCategorizationDependencies],
    email_limit: int = 5,
) -> Optional[ThreadContext]:
    """
    Extract context from email thread history.
    
    Returns:
        ThreadContext with recent emails and linked documents
    """
    email = ctx.deps.email
    
    if not email.email_thread:
        return None
        
    thread = email.email_thread
    
    # Get recent emails in thread
    recent_emails = (
        Communication.objects
        .filter(email_thread=thread)
        .exclude(pk=email.pk)
        .order_by('-created_at')[:email_limit]
    )
    
    # Get linked documents
    linked_pos = list(
        thread.purchase_orders.values_list('po_number', flat=True)
    )
    linked_invoices = list(
        thread.invoices.values_list('invoice_number', flat=True)
    )
    
    thread_context = ThreadContext(
        thread_id=str(thread.id),
        email_count=thread.communications.count(),
        recent_emails=[
            EmailSummary(
                id=str(e.id),
                subject=e.subject,
                sender=e.sender,
                created_at=e.created_at,
                category=e.category,
            ) for e in recent_emails
        ],
        linked_pos=linked_pos,
        linked_invoices=linked_invoices,
    )
    
    # Store in dependencies
    ctx.deps.thread_context = thread_context
    
    return thread_context
```

### Enrichment Tools

```python
# didero/ai/email_categorization/tools/enrichment_tools.py

async def enrich_po_context(
    ctx: RunContext[EmailCategorizationDependencies],
    po_number: str,
) -> Optional[PurchaseOrderContext]:
    """
    Fetch PO with status-aware relation loading.
    
    Loads relations based on PO status:
    - Pre-shipment: PO only
    - Shipment phase: PO + OAs
    - Invoice phase: PO + OAs + Shipments + Invoices
    """
    try:
        # Get PO with optimized query
        po = (
            PurchaseOrder.objects
            .select_related('supplier', 'team')
            .prefetch_related(
                'order_acknowledgements',
                'shipments',
                'invoices',
            )
            .filter(
                po_number=po_number,
                team_id=ctx.deps.team_id,
                archived_at__isnull=True,
            )
            .first()
        )
        
        if not po:
            # Track as new PO for potential creation
            return PurchaseOrderContext(
                po_id="",
                po_number=po_number,
                order_status="NEW",
                po_data={},
                is_new=True,
            )
        
        # Build context based on status
        context = PurchaseOrderContext(
            po_id=str(po.id),
            po_number=po.po_number,
            supplier_id=po.supplier.id if po.supplier else None,
            order_status=po.order_status,
            po_data=serialize_purchase_order(po),
            last_modified=po.updated_at,
        )
        
        # Load relations based on status
        if po.order_status in ['ISSUED', 'OA_RECEIVED', 'OA_MISMATCH']:
            context.acknowledgments = [
                serialize_oa(oa) for oa in po.order_acknowledgements.all()
            ]
            
        if po.order_status in ['AWAITING_SHIPMENT', 'SHIPPED', 'DELIVERED', 'RECEIVED']:
            context.shipments = [
                serialize_shipment(s) for s in po.shipments.all()
            ]
            
        if po.order_status in ['INVOICE_MATCHED', 'INVOICE_MISMATCH']:
            context.invoices = [
                serialize_invoice(inv) for inv in po.invoices.all()
            ]
        
        # Store in dependencies
        ctx.deps.po_contexts[po_number] = context
        
        return context
        
    except Exception as e:
        logger.error(f"Failed to enrich PO context: {e}")
        return None

async def enrich_invoice_context(
    ctx: RunContext[EmailCategorizationDependencies],
    invoice_number: str,
) -> Optional[InvoiceContext]:
    """
    Fetch invoice and linked PO if exists.
    """
    try:
        invoice = (
            Invoice.objects
            .select_related('purchase_order', 'supplier')
            .filter(
                invoice_number=invoice_number,
                team_id=ctx.deps.team_id,
            )
            .first()
        )
        
        if not invoice:
            return InvoiceContext(
                invoice_number=invoice_number,
                is_new=True,
            )
        
        context = InvoiceContext(
            invoice_number=invoice.invoice_number,
            invoice_data=serialize_invoice(invoice),
            supplier_id=invoice.supplier.id if invoice.supplier else None,
        )
        
        # Link to PO context if exists
        if invoice.purchase_order:
            po_context = ctx.deps.po_contexts.get(invoice.purchase_order.po_number)
            if not po_context:
                po_context = await enrich_po_context(ctx, invoice.purchase_order.po_number)
            context.linked_po = po_context
        
        # Store in dependencies
        ctx.deps.invoice_contexts[invoice_number] = context
        
        return context
        
    except Exception as e:
        logger.error(f"Failed to enrich invoice context: {e}")
        return None
```

### Validation Tools

```python
# didero/ai/email_categorization/tools/validation_tools.py

async def validate_procurement_relevance(
    ctx: RunContext[EmailCategorizationDependencies],
) -> Dict[str, Any]:
    """
    Binary filter to determine if email is procurement-related.
    
    Returns:
        Dictionary with 'is_relevant', 'confidence', and 'method' keys
    """
    # Single-source binary filter via Stage‑1 classifier at 95% threshold
    from didero.ai.email_categorization.categorizer import (
        classify_email_high_level_domain,
        EMAIL_CATEGORIZATION_CONFIDENCE_THRESHOLD,
    )

    # Temporarily override threshold to 95% for relevance filtering
    domain, confidence = classify_email_high_level_domain(
        ctx=ctx, client=get_openai_client(), email_txt=ctx.deps.email_text
    )

    is_relevant = domain != HighLevelEmailCategory.OTHER.value and confidence >= 95.0
    ctx.deps.is_procurement_related = is_relevant
    ctx.deps.relevance_confidence = confidence

    return {
        'is_relevant': is_relevant,
        'confidence': confidence,
        'method': 'ai_stage1_95pct',
        'reason': f'Stage‑1={domain} at {confidence:.1f}% (threshold 95%)',
    }

async def validate_acknowledgment(
    ctx: RunContext[EmailCategorizationDependencies],
    po_context: PurchaseOrderContext,
) -> Dict[str, Any]:
    """
    Validate if email is a valid order acknowledgment for the given PO.
    
    Returns:
        Dictionary with 'is_valid', 'intent', and 'reason' keys
    """
    # Reuse existing validation logic
    from didero.ai.email_categorization.validation import (
        validate_order_acknowledgment_email
    )
    
    validation_result = validate_order_acknowledgment_email(ctx.deps.email)
    
    if not validation_result:
        return {
            'is_valid': False,
            'intent': None,
            'reason': 'Not an order acknowledgment'
        }
    
    # Additional validation against PO context
    if po_context.order_status not in ['ISSUED', 'OA_RECEIVED', 'OA_MISMATCH']:
        return {
            'is_valid': False,
            'intent': validation_result.acknowledgment_intent,
            'reason': f'PO status {po_context.order_status} not ready for acknowledgment'
        }
    
    return {
        'is_valid': validation_result.is_acknowledgment,
        'intent': validation_result.acknowledgment_intent,
        'reason': 'Valid acknowledgment' if validation_result.is_acknowledgment else 'Invalid intent'
    }

async def validate_shipment(
    ctx: RunContext[EmailCategorizationDependencies],
    po_context: PurchaseOrderContext,
) -> Dict[str, Any]:
    """
    Validate if email contains shipment or pickup notification.
    
    Returns:
        Dictionary with 'is_valid', 'notification_type', and 'tracking_info' keys
    """
    # Reuse existing validation
    from didero.ai.email_categorization.validation import validate_shipment_email
    
    is_valid, notification_type = validate_shipment_email(ctx.deps.email)
    
    if not is_valid:
        return {
            'is_valid': False,
            'notification_type': None,
            'tracking_info': None,
            'reason': 'No shipment information found'
        }
    
    # Validate against PO status
    if po_context.order_status not in ['AWAITING_SHIPMENT', 'SHIPPED']:
        return {
            'is_valid': False,
            'notification_type': notification_type,
            'tracking_info': None,
            'reason': f'PO status {po_context.order_status} not ready for shipment'
        }
    
    return {
        'is_valid': True,
        'notification_type': notification_type,
        'tracking_info': _extract_tracking_info(ctx.deps.email_text),
        'reason': f'Valid {notification_type} notification'
    }
```

### Decision Tools

```python
# didero/ai/email_categorization/tools/decision_tools.py

async def decide_workflow_triggers(
    ctx: RunContext[EmailCategorizationDependencies],
) -> List[WorkflowDecision]:
    """
    Analyze accumulated context and decide which workflows to trigger.
    
    This is the main decision-making tool that considers:
    - Extracted identifiers
    - Database context
    - Validation results
    - Business rules
    
    Returns:
        List of workflow decisions with reasons
    """
    decisions = []
    config = ctx.deps.behavior_config
    
    # Process each PO context
    for po_number, po_context in ctx.deps.po_contexts.items():
        
        # New PO creation
        if po_context.is_new:
            # Check if email contains PO document
            if await _contains_po_document(ctx):
                decisions.append(WorkflowDecision(
                    workflow_type=WorkflowType.PURCHASE_ORDER_CREATION.value,
                    trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
                    confidence=0.95,
                    reason=f"New PO {po_number} document detected",
                    po_number=po_number,
                    validation_passed=True,
                ))
                
        # Existing PO workflows
        else:
            # Check for acknowledgment
            ack_validation = await validate_acknowledgment(ctx, po_context)
            if ack_validation['is_valid'] and ack_validation['intent'] == 'accepting':
                decisions.append(WorkflowDecision(
                    workflow_type=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value,
                    trigger=WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED.value,
                    confidence=0.90,
                    reason=f"Valid acknowledgment for PO {po_number}",
                    po_number=po_number,
                    validation_passed=True,
                    context_data={'intent': ack_validation['intent']},
                ))
            
            # Check for shipment
            ship_validation = await validate_shipment(ctx, po_context)
            if ship_validation['is_valid']:
                decisions.append(WorkflowDecision(
                    workflow_type=WorkflowType.PURCHASE_ORDER_SHIPPED.value,
                    trigger=WorkflowTrigger.ON_ORDER_SHIPPED_EMAIL_RECEIVED.value,
                    confidence=0.85,
                    reason=f"{ship_validation['notification_type']} notification for PO {po_number}",
                    po_number=po_number,
                    validation_passed=True,
                    context_data={
                        'notification_type': ship_validation['notification_type'],
                        'tracking_info': ship_validation['tracking_info'],
                    },
                ))
    
    # Process invoices
    for invoice_number, invoice_context in ctx.deps.invoice_contexts.items():
        decisions.append(WorkflowDecision(
            workflow_type=WorkflowType.INVOICE_PROCESSING.value,
            trigger=WorkflowTrigger.ON_INVOICE_EMAIL_RECEIVED.value,
            confidence=0.90,
            reason=f"Invoice {invoice_number} detected",
            invoice_number=invoice_number,
            po_number=invoice_context.linked_po.po_number if invoice_context.linked_po else None,
            validation_passed=True,
        ))
    
    # Check for shipping documents (can trigger even without PO/Invoice)
    if await _contains_shipping_document(ctx):
        decisions.append(WorkflowDecision(
            workflow_type=WorkflowType.SHIPPING_DOCUMENT_PROCESSING.value,
            trigger=WorkflowTrigger.ON_SHIPPING_DOCUMENT_EMAIL_RECEIVED.value,
            confidence=0.80,
            reason="Shipping document detected",
            validation_passed=True,
        ))
    
    # Apply limits
    if config and config.max_workflows_per_email:
        decisions = decisions[:config.max_workflows_per_email]
    
    # Store in dependencies
    ctx.deps.workflow_decisions = decisions
    
    return decisions

async def categorize_email(
    ctx: RunContext[EmailCategorizationDependencies],
) -> str:
    """
    Determine primary email category for backward compatibility.
    
    Returns:
        EmailCategory value or 'OTHER'
    """
    # Prioritize based on workflow decisions
    if not ctx.deps.workflow_decisions:
        return EmailCategory.OTHER.value
    
    # Map workflow types to categories
    workflow_to_category = {
        WorkflowType.PURCHASE_ORDER_CREATION.value: EmailCategory.PURCHASE_ORDER,
        WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value: EmailCategory.PURCHASE_ORDER_ACKNOWLEDGEMENT,
        WorkflowType.PURCHASE_ORDER_SHIPPED.value: EmailCategory.ORDER_SHIPPED,
        WorkflowType.INVOICE_PROCESSING.value: EmailCategory.INVOICE,
        WorkflowType.SHIPPING_DOCUMENT_PROCESSING.value: EmailCategory.SHIPPING_DOCUMENT,
    }
    
    # Return category of highest confidence decision
    best_decision = max(ctx.deps.workflow_decisions, key=lambda d: d.confidence)
    return workflow_to_category.get(
        best_decision.workflow_type,
        EmailCategory.OTHER.value
    )
```

## Agent Implementation

### Agent Creation

```python
# didero/ai/email_categorization/agent.py

from didero.ai.agents import create_ai_agent
from didero.ai.schemas import AIModel, AIModelNames

def create_email_categorization_agent():
    """
    Create the email categorization agent with all tools.
    """
    return create_ai_agent(
        model=AIModel.OPENAI + ":" + AIModelNames.OPENAI_LARGE,
        deps_type=EmailCategorizationDependencies,
        output_type=EmailCategorizationResult,
        system_prompt=_get_categorization_system_prompt(),
        tools=[
            # Extraction tools
            extract_identifiers,
            extract_thread_context,
            
            # Enrichment tools
            enrich_po_context,
            enrich_invoice_context,
            
            # Validation tools
            validate_procurement_relevance,
            validate_acknowledgment,
            validate_shipment,
            
            # Decision tools
            decide_workflow_triggers,
            categorize_email,
        ],
        retries=2,
        instrument=True,  # Enable OpenTelemetry tracing
    )

def _get_categorization_system_prompt() -> str:
    """
    Load and compile the system prompt for email categorization.
    """
    prompts_path = Path(__file__).parent / "prompts_config.yaml"
    prompt = get_langfuse_prompt(
        prompt_key="email_categorization_agent",
        prompts_path=prompts_path
    )
    
    return prompt.compile(
        confidence_thresholds={
            'relevance': 0.85,
            'categorization': 0.90,
            'extraction': 0.70,
        }
    )
```

### System Prompt

```yaml
# didero/ai/email_categorization/prompts_config.yaml

email_categorization_agent:
  prompt: |
    You are an intelligent email categorization agent for a procurement system.
    Your goal is to analyze emails, extract relevant information, and determine 
    which workflows should be triggered.
    
    ## Your Workflow
    
    ### Step 1: Check Relevance
    First, determine if this email is procurement-related using Stage‑1 classifier at 95% threshold:
    ```python
    relevance = await validate_procurement_relevance()
    if not relevance['is_relevant']:
        return EmailCategorizationResult(
            is_procurement_related=False,
            workflow_decisions=[],
            confidence_score=relevance['confidence']
        )
    ```
    
    ### Step 2: Extract Identifiers
    Extract PO numbers using existing PO extractor and invoice numbers by reusing the full invoice extractor (read invoice_number only):
    ```python
    identifiers = await extract_identifiers(include_po=True, include_invoice=True)
    ```
    
    ### Step 3: Gather Context
    Get thread context if available:
    ```python
    thread_context = await extract_thread_context()
    ```
    
    ### Step 4: Enrich with Database Context
    For each extracted identifier, fetch database context:
    ```python
    for po_number in identifiers['po_numbers']:
        po_context = await enrich_po_context(po_number)
    
    for invoice_number in identifiers['invoice_numbers']:
        invoice_context = await enrich_invoice_context(invoice_number)
    ```
    
    ### Step 5: Make Decisions
    Analyze the accumulated context and decide on workflows:
    ```python
    decisions = await decide_workflow_triggers()
    ```
    
    ### Step 6: Determine Category
    For backward compatibility, determine primary category:
    ```python
    category = await categorize_email()
    ```
    
    ## Important Guidelines
    
    1. **Always validate before triggering workflows** - Use validation tools for 
       acknowledgments and shipments
    
    2. **Consider PO status** - Don't trigger workflows that don't match the PO's 
       current lifecycle stage
    
    3. **Handle multiple triggers carefully** - One email can trigger multiple 
       workflows if it contains multiple documents
    
    4. **Track confidence** - Each decision should have an associated confidence score
    
    5. **Provide clear reasons** - Each workflow decision must have a clear, 
       business-understandable reason
    
    ## Edge Cases
    
    - If no identifiers are found but the email appears procurement-related, 
      mark for manual review
    - If thread context contradicts current email, prioritize current email
    - If validation fails but confidence is high, include in notes for review
```

### Main Entry Point

```python
# didero/ai/email_categorization/categorizer_v2.py

def ai_categorize_email_agent(
    email: Communication,
) -> EmailCategorizationResult:
    """
    Agent-based email categorization with tool orchestration.
    
    This is the new entry point that will eventually replace ai_categorize_email.
    """
    with AIOperationContext(
        trace_name="ai_categorize_email_agent",
        team_id=email.team.id,
        masking_function=masking_function_email_and_phone_redactor,
        metadata={
            "email_id": email.pk,
            "email_message_id": email.email_message_id,
            "email_thread_id": email.email_thread.id if email.email_thread else None,
            "version": "v2_agent",
        },
        tags=["email_categorization", "agent"],
    ) as ctx:
        try:
            # Load behavior configuration
            behavior_config = _load_behavior_config(email.team.id)
            
            if not behavior_config.use_agent_categorization:
                # Fall back to legacy categorization
                return _convert_legacy_result(ai_categorize_email(email))
            
            # Prepare dependencies
            deps = EmailCategorizationDependencies(
                email=email,
                team_id=email.team.id,
                email_text=email.as_display_string(include_attachment_names=True),
                attachment_names=_get_attachment_names(email),
                behavior_config=behavior_config,
            )
            
            # Create and run agent
            agent = create_email_categorization_agent()
            result = agent.run_sync(
                "Categorize this email and determine which workflows to trigger.",
                deps=deps,
            )
            
            # Record telemetry
            _record_agent_categorization_metrics(result.output, email)
            
            return result.output
            
        except Exception as e:
            logger.error(
                "Agent categorization failed",
                error=str(e),
                email_id=email.pk,
                team_id=email.team.id,
            )
            
            if behavior_config.use_legacy_on_error:
                # Fall back to legacy on error
                return _convert_legacy_result(ai_categorize_email(email))
            
            # Return safe default
            return EmailCategorizationResult(
                is_procurement_related=False,
                workflow_decisions=[],
                confidence_score=0.0,
                requires_manual_review=True,
                manual_review_reason=f"Agent processing failed: {str(e)}",
            )
```

## Workflow Integration

### Triggering Multiple Workflows

```python
# didero/ai/email_categorization/workflow_triggers.py

def trigger_workflows_from_agent_result(
    email: Communication,
    result: EmailCategorizationResult,
) -> List[WorkflowTriggerResult]:
    """
    Trigger workflows based on agent categorization result.
    
    Supports multiple workflow triggers from a single email.
    """
    trigger_results = []
    
    # Enforce dependency-based ordering: PO Creation → OA → Shipment → Invoice → Shipping Docs
    ordered = _order_decisions_dependency_based(result.workflow_decisions)

    # In-run dedupe by (workflow_type, entity_id)
    seen: Set[Tuple[str, Optional[str]]] = set()

    for decision in ordered:
        key = (decision.workflow_type, decision.po_number or decision.invoice_number)
        if key in seen:
            continue
        seen.add(key)
        # Build context based on workflow type
        context = _build_workflow_context(email, decision)
        
        # Trigger workflow
        trigger_result = trigger_workflow_if_exists_with_status(
            workflow_type=decision.workflow_type,
            trigger=decision.trigger,
            team=email.team,
            context_object=context,
            idempotency_key=_make_idempotency_key(email.id, decision),
        )
        
        # Record telemetry
        if trigger_result.triggered:
            record_email_workflow_trigger(
                team_id=email.team.id,
                workflow_type=decision.workflow_type,
                trigger=decision.trigger,
                category=result.primary_category or "MULTIPLE",
                email_id=email.pk,
            )
        else:
            record_workflow_skip(
                team_id=email.team.id,
                workflow_type=decision.workflow_type,
                reason=trigger_result.error_message or "unknown",
                email_id=email.pk,
            )
        
        trigger_results.append(trigger_result)
    
    # Create manual review task if needed
    if result.requires_manual_review:
        _create_manual_review_task(
            email=email,
            reason=result.manual_review_reason,
            categorization_result=result,
        )
    
    return trigger_results

def _build_workflow_context(
    email: Communication,
    decision: WorkflowDecision,
) -> WorkflowContext:
    """
    Build appropriate context object for workflow type.
    """
    if decision.workflow_type == WorkflowType.PURCHASE_ORDER_SHIPPED.value:
        return ShipmentWorkflowContext(
            email=email,
            notification_type=decision.context_data.get('notification_type'),
        )
    
    # Default to email context
    return EmailWorkflowContext(email=email)

def _order_decisions_dependency_based(decisions: List[WorkflowDecision]) -> List[WorkflowDecision]:
    order = {
        WorkflowType.PURCHASE_ORDER_CREATION.value: 0,
        WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value: 1,
        WorkflowType.PURCHASE_ORDER_SHIPPED.value: 2,
        WorkflowType.INVOICE_PROCESSING.value: 3,
        WorkflowType.SHIPPING_DOCUMENT_PROCESSING.value: 4,
    }
    return sorted(decisions, key=lambda d: (order.get(d.workflow_type, 99), -d.confidence))

def _make_idempotency_key(email_id: str, decision: WorkflowDecision) -> str:
    entity = decision.po_number or decision.invoice_number or "none"
    material = f"{email_id}|{decision.workflow_type}|{entity}"
    return hashlib.sha256(material.encode("utf-8")).hexdigest()
```

### Temporal Activity Integration

```python
# didero/workflows/shared_activities/email_categorization.py

@activity.defn
def categorize_and_trigger_email_workflows_activity(
    email_id: str,
    team_id: int,
) -> Dict[str, Any]:
    """
    Temporal activity for agent-based email categorization and workflow triggering.
    """
    try:
        # Load email
        email = Communication.objects.get(id=email_id)
        
        # Run agent categorization
        result = ai_categorize_email_agent(email)
        
        # Trigger workflows
        trigger_results = trigger_workflows_from_agent_result(email, result)
        
        return {
            'success': True,
            'is_procurement_related': result.is_procurement_related,
            'workflows_triggered': len([r for r in trigger_results if r.triggered]),
            'workflows_attempted': len(trigger_results),
            'primary_category': result.primary_category,
            'extracted_pos': result.extracted_po_numbers,
            'extracted_invoices': result.extracted_invoice_numbers,
            'requires_manual_review': result.requires_manual_review,
        }
        
    except Exception as e:
        logger.error(f"Email categorization activity failed: {e}")
        raise ApplicationError(str(e), non_retryable=False)
```


## Observability and Telemetry

### Metrics

```python
# didero/telemetry/workflow_metrics.py

def record_agent_categorization(
    team_id: int,
    is_procurement_related: bool,
    workflow_count: int,
    extraction_count: int,
    confidence: float,
    processing_time_ms: int,
    version: str = "v2",
):
    """Record agent-based categorization metrics."""
    attributes = {
        "team.id": str(team_id),
        "categorization.version": version,
        "categorization.is_procurement": str(is_procurement_related),
        "categorization.workflow_count": str(workflow_count),
        "categorization.extraction_count": str(extraction_count),
        "categorization.confidence": str(int(confidence * 100)),
        "categorization.processing_time_ms": str(processing_time_ms),
    }
    
    agent_categorization_counter.add(1, attributes)
```

### Tracing

All agent operations are automatically traced via:
- **AIOperationContext**: Wraps all AI operations
- **Langfuse integration**: Detailed AI call tracing
- **OpenTelemetry**: Distributed tracing
- **Structlog**: Structured logging with trace correlation

### Debugging Support

```python
class EmailCategorizationDebugInfo(BaseModel):
    """Debug information for categorization decisions."""
    
    email_id: str
    agent_trace_id: str
    langfuse_trace_url: str
    
    # Tool execution log
    tool_executions: List[Dict[str, Any]]
    
    # Decision reasoning
    decision_tree: Dict[str, Any]
    
    # Confidence breakdown
    confidence_scores: Dict[str, float]
    
    # Validation results
    validation_results: Dict[str, Any]
```

## Testing Strategy

### Unit Tests

```python
# tests/ai/email_categorization/test_tools.py

class TestExtractionTools(TestCase):
    def test_extract_identifiers_with_confidence_filtering(self):
        """Test identifier extraction with confidence thresholds."""
        
    def test_extract_thread_context_with_no_thread(self):
        """Test thread extraction when email has no thread."""

class TestValidationTools(TestCase):
    def test_validate_procurement_relevance_rules(self):
        """Test rule-based relevance validation."""
    
    def test_validate_acknowledgment_with_wrong_status(self):
        """Test acknowledgment validation with incorrect PO status."""
```

### Integration Tests

```python
# tests/ai/email_categorization/test_agent_integration.py

class TestEmailCategorizationAgent(TransactionTestCase):
    def test_agent_full_flow_po_creation(self):
        """Test agent categorization for PO creation email."""
        
    def test_agent_multiple_workflow_triggers(self):
        """Test agent triggering multiple workflows from one email."""
    
    def test_agent_fallback_on_error(self):
        """Test fallback to legacy categorization on agent error."""
```

### Performance Tests

```python
# tests/ai/email_categorization/test_performance.py

class TestCategorizationPerformance(TestCase):
    def test_agent_categorization_under_3_seconds(self):
        """Ensure agent categorization completes within SLA."""
    
    def test_parallel_tool_execution(self):
        """Test that independent tools execute in parallel."""
```

## Open Decisions

### 1. Invoice Extraction Prompt Placement

**Options:**
 a) Add to existing `prompts_config.yaml` in email_categorization
 b) Create new `didero/ai/invoice/prompts_config.yaml`
 c) Centralize all prompts in `didero/ai/prompts/`

**Decision:** We will add the agent system prompt under `didero/ai/email_categorization/prompts_config.yaml` (A). For invoice extraction, we reuse the existing invoice prompts under `didero/ai/invoice/` and do not duplicate them.

### 2. Caching Strategy for Enrichment Tools

**Options:**
a. No caching - always fetch fresh data
b. Request-level caching - cache within single categorization
c. Time-based caching - cache for X minutes across requests

**Recommendation:** Option B to optimize multiple tool calls while ensuring freshness

### 3. Manual Review Task Creation

**Options:**
a. Create tasks for all low-confidence categorizations
b. Create tasks only when explicitly flagged by agent
c. Configurable threshold per team

**Recommendation:** Option C for flexibility

### 4. Thread Context Requirement

**Options:**
 a) Always require thread context for certain decisions
 b) Make thread context optional with degraded functionality
 c) Team-configurable requirement

**Decision:** Option A in our top-level decisions was to keep thread context optional; we proceed best‑effort and log when missing (maps to B here).

### 5. Workflow Trigger Ordering

When multiple workflows are triggered, in what order should they execute?

**Options:**
 a) Parallel execution (all at once)
 b) Priority-based (PO > OA > Shipment > Invoice)
 c) Confidence-based (highest confidence first)
 d) Dependency-based (PO creation before OA)

**Decision:** d) Dependency-based ordering: PO Creation → OA → Shipment → Invoice → Shipping Docs.

## Conclusion

This agent-based email categorization system represents a significant evolution from the current waterfall approach. By leveraging pydantic-ai agents with composable tools, we achieve:

1. **Flexibility**: Adaptive tool selection based on email content
2. **Context Awareness**: Rich database context informs decisions
3. **Scalability**: Support for multiple workflow triggers
4. **Observability**: Comprehensive tracing and debugging
5. **Maintainability**: Modular, testable tool architecture
6. **Type Safety**: Pydantic models throughout

The design follows established patterns in the codebase while introducing modern agent-based orchestration that aligns with the company's movement toward more intelligent, context-aware systems.