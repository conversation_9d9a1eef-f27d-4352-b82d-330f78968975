# Generated by Django 4.2.7 on 2024-08-05 15:42

from django.db import migrations, models
import django.db.models.deletion


def add_null_team(apps, schema_editor):
    Team = apps.get_model("users", "Team")
    if not Team.objects.filter(pk=0).exists():
        Team.objects.create(pk=0, name="Null Team")


def set_default_team(apps, schema_editor):
    SupplierDomain = apps.get_model("suppliers", "SupplierDomain")
    for domain in SupplierDomain.objects.all():
        domain.team = domain.supplier.team
        domain.save()


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0021_alter_teamsetting_name_alter_teamsetting_value"),
        ("suppliers", "0042_alter_communication_email_message_id_and_more"),
    ]

    operations = [
        migrations.RunPython(add_null_team),
        migrations.AddField(
            model_name="supplierdomain",
            name="team",
            field=models.ForeignKey(
                default=0,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="domains",
                to="users.team",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="suppliercontact",
            name="email",
            field=models.EmailField(blank=True, max_length=256),
        ),
        migrations.AlterField(
            model_name="supplierdomain",
            name="domain",
            field=models.CharField(db_index=True, max_length=128),
        ),
        migrations.AlterUniqueTogether(
            name="suppliercontact",
            unique_together={("email", "supplier")},
        ),
        migrations.AddIndex(
            model_name="supplierdomain",
            index=models.Index(
                fields=["domain", "team"], name="suppliers_s_domain_88fb5a_idx"
            ),
        ),
        migrations.RunPython(set_default_team),
    ]
