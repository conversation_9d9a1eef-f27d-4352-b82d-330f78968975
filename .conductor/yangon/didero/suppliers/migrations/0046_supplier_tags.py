# Generated by Django 4.2.7 on 2024-08-12 16:40

from django.db import migrations
import taggit.managers


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0005_alter_taggeditem_content_type_and_more"),
        ("suppliers", "0045_alter_suppliercontact_email_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="supplier",
            name="tags",
            field=taggit.managers.TaggableManager(
                help_text="A comma-separated list of tags.",
                through="common.TaggedItem",
                to="common.CustomTag",
                verbose_name="Tags",
            ),
        ),
    ]
