# Generated by Django 4.2.7 on 2024-08-20 14:36

import didero.suppliers.models
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0022_team_supplier_bulk_create_task_id"),
        ("suppliers", "0051_alter_communication_email_thread_and_more"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name="supplier",
            name="website_url",
            field=models.URLField(null=True),
        ),
        migrations.AlterField(
            model_name="supplierdomain",
            name="domain",
            field=models.CharField(
                db_index=True,
                max_length=128,
                validators=[didero.suppliers.models.validate_domain],
            ),
        ),
        migrations.AlterUniqueTogether(
            name="supplier",
            unique_together={("team", "website_url")},
        ),
    ]
