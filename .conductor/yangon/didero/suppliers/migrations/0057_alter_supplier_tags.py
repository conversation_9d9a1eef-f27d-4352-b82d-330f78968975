# Generated by Django 4.2.7 on 2024-08-23 02:50

from django.db import migrations
import taggit.managers


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0006_customtag_created_at_customtag_modified_at"),
        ("suppliers", "0056_communicationemailrecipient_communication_bcc_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="supplier",
            name="tags",
            field=taggit.managers.TaggableManager(
                blank=True,
                help_text="A comma-separated list of tags.",
                through="common.TaggedItem",
                to="common.CustomTag",
                verbose_name="Tags",
            ),
        ),
    ]
