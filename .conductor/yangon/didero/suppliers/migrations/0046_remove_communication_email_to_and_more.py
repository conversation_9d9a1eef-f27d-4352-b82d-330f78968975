# Generated by Django 4.2.7 on 2024-08-12 19:43

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_cryptography.fields


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0045_alter_suppliercontact_email_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="communication",
            name="email_to",
        ),
        migrations.CreateModel(
            name="CommunicationEmailRecipient",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "email_address",
                    django_cryptography.fields.encrypt(
                        models.EmailField(max_length=255)
                    ),
                ),
                (
                    "communication_to",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_to",
                        to="suppliers.communication",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
