# Generated by Django 4.2.7 on 2024-08-21 14:47

from django.db import transaction, IntegrityError, migrations

"""
This migration gave a lot of issues:
When the Communication model is imported as part of projectstate
(apps.get_model), this line `for comm in Communication.objects.all():`
fails with an encryption error (InvalidToken).
We therefore have to directly import Communication from the suppliers app.
However, if in the future we change the Communication model, the ORM
gets busted and the migration errors - so we could just come back here and
switch the import.
This migration should also ideally delete the comm.documents field. However,
although the projectstate Communication model still has .documents, the 
imported model wouldn't, and so `for doc in comm.documents.all()` fails with
"Communication has no attribute documents".

We therefore need two PRs/migrations:
One PR (this one) to create documentlinks for existing email attachments, and
introduce the logic to create them going forward.
And, once that's in prod, another to delete the comm.documents field.

Addendum:
When this made it to prod the first time, we got an issue on the websocket
call: creating a documentlink calls websocket triggering, which checks for the
team model. However, in prod, the team model project state wasn't the same as
the team model in the code. So it kept failing, and we had to change everything
to the mess of apps.get_models below.
"""


# we're removing documents from emails. that means we have to backfill:
# for existing emails, create document links from the document to the email
def create_document_links(apps, schema_editor):
    # Communication = apps.get_model("suppliers", "Communication")
    DocumentLink = apps.get_model("documents", "DocumentLink")
    Document = apps.get_model("documents", "Document")
    ContentType = apps.get_model("contenttypes", "ContentType")
    Communication = apps.get_model("suppliers", "Communication")
    communication_content_type = ContentType.objects.get_for_model(Communication)
    for comm in Communication.objects.all():
        for doc in comm.documents.all():
            document_instance = Document.objects.get(pk=doc.pk)
            try:
                with transaction.atomic():
                    DocumentLink.objects.create(
                        document=document_instance,
                        parent_object_type=communication_content_type,
                        parent_object_id=comm.pk,
                    )
            except IntegrityError:
                # If the document is already linked to the email
                pass


# do the reverse migration if we need. that means: delete the document link
# from the email, and set comm.documents back to the documents
def remove_document_links(apps, schema_editor):
    # Communication = apps.get_model("suppliers", "Communication")
    DocumentLink = apps.get_model("documents", "DocumentLink")
    ContentType = apps.get_model("contenttypes", "ContentType")
    communication_content_type = ContentType.objects.get_for_model(Communication)  # noqa: F821
    # go through all links whose parent is an email
    for link in DocumentLink.objects.filter(
        parent_object_type=communication_content_type
    ):
        parent_communication_id = link.parent_object_id
        parent_communication = Communication.objects.get(pk=parent_communication_id)  # noqa: F821
        current_documents = list(parent_communication.documents.all())
        current_document_ids = [doc.pk for doc in current_documents]
        parent_communication.documents.set(current_document_ids)


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0054_alter_supplier_website_url"),
    ]

    operations = [
        migrations.RunPython(create_document_links, reverse_code=remove_document_links),
    ]
