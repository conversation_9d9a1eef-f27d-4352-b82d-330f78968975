# Generated by Django 4.2.7 on 2024-08-12 20:47

from django.db import migrations, models


# This migration is part of the deletion of Supplier.Document (since we have Document.Document)
# GmailAttachments were still being saved as Supplier.Documents, so we need to
# migrate them - similar to how documents.0001_initial did it
def migrate_communication_documents(apps, schema_editor):
    Communication = apps.get_model("suppliers", "Communication")
    Document = apps.get_model("documents", "Document")
    DocumentLink = apps.get_model("documents", "DocumentLink")
    for communication in Communication.objects.all():
        for supplier_document in communication.documents.all():
            # Check if the document already exists in the new documents table
            document, created = Document.objects.get_or_create(
                uuid=supplier_document.uuid,
                defaults={
                    "name": supplier_document.name,
                    "doc_type": supplier_document.doc_type,
                    "text_content": supplier_document.text_content,
                    "description": supplier_document.description,
                    "description_meta": supplier_document.description_meta,
                    "csv_data": supplier_document.csv_data,
                    "csv_meta": supplier_document.csv_meta,
                    "document": supplier_document.document,
                    "content_type": supplier_document.content_type,
                    "content_hash": supplier_document.content_hash,
                    "filesize_bytes": supplier_document.filesize_bytes,
                    "team": supplier_document.team,
                },
            )
            # Add the new document to the new_documents field
            communication.new_documents.add(document)
            communication.save()

            # Link the document to the supplier
            DocumentLink.objects.create(
                document=document,
                parent_object=communication.supplier,
            )


def set_constraints_deferred(apps, schema_editor):
    schema_editor.execute("SET CONSTRAINTS ALL DEFERRED;")


def set_constraints_immediate(apps, schema_editor):
    schema_editor.execute("SET CONSTRAINTS ALL IMMEDIATE;")


"""
The pattern for this migration is:
Create a new field ("new_document"), that will link to the documents.Document model
Migrate the existing documents to the new field
Delete the old field (which linked to suppliers.Document)
Rename the new field (->normal "document)
"""


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0038_alter_delivery_receipt_document"),
        ("documents", "0001_initial"),
        ("emails", "0035_alter_gmailattachment_document"),
        ("suppliers", "0045_alter_suppliercontact_email_and_more"),
    ]

    operations = [
        migrations.RunPython(set_constraints_deferred),
        migrations.RemoveField(
            model_name="documentcomment",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="documentcomment",
            name="document",
        ),
        migrations.AddField(
            model_name="communication",
            name="new_documents",
            field=models.ManyToManyField(blank=True, to="documents.document"),
        ),
        migrations.RemoveField(
            model_name="communication",
            name="documents",
        ),
        migrations.RenameField(
            model_name="communication",
            old_name="new_documents",
            new_name="documents",
        ),
        migrations.DeleteModel(
            name="Document",
        ),
        migrations.DeleteModel(
            name="DocumentComment",
        ),
        migrations.RunPython(set_constraints_immediate),
    ]
