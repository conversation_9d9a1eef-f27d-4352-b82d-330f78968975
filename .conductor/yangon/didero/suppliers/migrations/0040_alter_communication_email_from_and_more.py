# Generated by Django 4.2.7 on 2024-08-01 20:55

from django.db import migrations, models
import django_cryptography.fields


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0039_alter_supplier_unique_together_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="communication",
            name="email_from",
            field=django_cryptography.fields.encrypt(
                models.EmailField(default="<EMAIL>", max_length=255)
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="communication",
            name="email_message_hash",
            field=models.CharField(
                blank=True, db_index=True, max_length=64, null=True, unique=True
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="communication",
            name="email_raw_headers",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name="communication",
            name="email_to",
            field=django_cryptography.fields.encrypt(
                models.EmailField(default="<EMAIL>", max_length=255)
            ),
            preserve_default=False,
        ),
    ]
