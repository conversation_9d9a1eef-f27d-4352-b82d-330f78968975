from datetime import <PERSON><PERSON><PERSON>
from typing import Any

import structlog
from celery import shared_task
from celery_singleton import Singleton
from django.contrib.admin.options import get_content_type_for_model
from django.utils import timezone

from didero.addresses.models import Address
from didero.ai.date_extraction.follow_up_dates import update_incomplete_oas_with_dates
from didero.ai.email_categorization import (
    ai_categorize_email,
    trigger_workflows_for_email_category,
)
from didero.ai.email_processing.email_processing import process_po_email
from didero.ai.supplier_description import generate_supplier_description_from_url
from didero.bulk_uploads.schemas import BulkUploadProcessResult
from didero.documents.models import Document, DocumentLink
from didero.documents.schemas import DocumentStatus
from didero.suppliers.models import (  # noqa: F401
    Communication,
    Supplier,
    SupplierContact,
    SupplierOnboardingRequirement,
)
from didero.suppliers.schemas import (
    OnboardingStatus,
    SupplierOnboardingRequirementStatus,
)
from didero.tasks.schemas import TaskStatus
from didero.tasks.utils import (
    create_document_expired_task,
    create_document_expiring_task,
)
from didero.users.models.team_models import Team
from didero.users.models.user_models import User
from didero.utils.utils import (
    domain_from_url,
    get_didero_ai_user,
)
from didero.utils.zapier_utils import trigger_zapier_workflow_alert_ops_team

logger = structlog.get_logger(__name__)

# todo change back to 5. however, for testing, need it larger to make sure can pick up emails, since nylas is missing some recent ones
# since the `alert_ops` flag is only set for one demo credential+supplier, this won't impact any other ones, and will only notify Joe and not ops
TIME_PERIOD_FOR_OPS_ALERTS_FOR_EMAILS = 50

REQUIRED_SUPPLIER_HEADERS = [
    "name",
    "website_url",
    "description",
    "notes",
    "address_line_1",
    "city",
    "state_or_province",
    "country",
    "postal_code",
    "payment_terms",
    "shipping_method",
]

REQUIRED_SUPPLIER_CONTACT_HEADERS = [
    "supplier_name",
    "name",
    "email",
    "phone",
    "default",
]


@shared_task(base=Singleton, lock_expiry=60 * 5, queue="ai_workflows")
def process_email_task(email_id: int, namespace: str) -> None:
    email = Communication.objects.get(id=email_id)

    """
    We still do not want to check for status updates on demo emails.
    We can eventually turn this on, but for now will just skip our other processing
    for demo emails.
    """
    if email.team.is_demo_team:
        logger.info(
            f"skipping all email processing as this is a demo email: {email.pk}",
            email_id=email_id,
            team_id=email.team.pk,
        )
        return

    email_category = ai_categorize_email(email)

    # Extract and update dates for follow-up scenarios
    if email_category:
        # Only attempt extraction for relevant categories
        # NOTE: PURCHASE_ORDER_ACKNOWLEDGEMENT intentionally excluded to prevent
        # revised OAs from triggering duplicate processing. This will be addressed
        # in a future ticket for handling revised OA scenarios.
        if email_category in [
            "GENERAL",
            "PURCHASE_ORDER_RELATED",
            "OTHER",
        ]:
            try:
                update_incomplete_oas_with_dates(email)
            except Exception as e:
                logger.error(
                    "Failed to extract dates for OA update",
                    error=str(e),
                    email_id=email_id,
                    category=email_category,
                )
                # Don't fail the whole task - continue to workflows

    if email_category:
        trigger_workflows_for_email_category(email, email_category)
    else:
        logger.info(
            "No email category found for email. Skipping trigger workflows.",
            email_id=email_id,
        )

    ### Check for status updates:
    process_po_email(email)
    ### Alert ops team. Only for new (live-pulled) emails - not historical backfills
    current_time = timezone.now()
    if (
        current_time - email.comm_time
        <= timedelta(days=TIME_PERIOD_FOR_OPS_ALERTS_FOR_EMAILS)
        and email.email_credential.alert_ops_team
        and email.supplier.alert_ops_team
    ):
        logger.info(
            "received recent email; triggering zapier workflow to alert ops team",
            supplier_id=email.supplier.pk,
            email_id=email_id,
            cred_id=email.email_credential.pk,
        )
        trigger_zapier_workflow_alert_ops_team(email)
    else:
        logger.info(
            "not triggering zapier workflow to alert ops team",
            supplier_id=email.supplier.pk,
            email_id=email_id,
            cred_id=email.email_credential.pk,
            email_credential_alert_ops_team=email.email_credential.alert_ops_team,
            supplier_alert_ops_team=email.supplier.alert_ops_team,
            current_time=current_time,
            email_comm_time=email.comm_time,
        )


EXPIRATION_TIME_DELTA_DAYS = 60


# Document Expiry Checking
# This only checks for documents that are currently attached to a SupplierOnboardingRequirement
@shared_task(base=Singleton, lock_expiry=60 * 5, queue="periodic_tasks")
def check_for_document_sor_expiry() -> None:
    # Attach related objects to the original queryset
    doc_links_with_sors = (
        DocumentLink.objects.filter(
            parent_object_type=get_content_type_for_model(SupplierOnboardingRequirement)
        )
        .select_related("parent_object_type")
        .iterator()
    )

    for link in doc_links_with_sors:
        doc: Document = link.document
        current_doc_status = doc.status
        expected_doc_status = get_doc_expiring_status(doc)
        sor: SupplierOnboardingRequirement = link.parent_object
        supplier: Supplier = sor.supplier

        # This shouldn't happen, but we need to handle it as expiration date is optional on documents.
        if current_doc_status is None:
            continue

        # Avoid a DB write if the status of the document hasn't changed.
        if current_doc_status == expected_doc_status:
            continue

        if expected_doc_status == DocumentStatus.EXPIRING:
            doc.set_document_status(expected_doc_status.value)
            sor.set_status(SupplierOnboardingRequirementStatus.EXPIRING.value)
            create_task_for_expiration_status(expected_doc_status, doc, sor)
        elif expected_doc_status == DocumentStatus.EXPIRED:
            doc.set_document_status(expected_doc_status)
            sor.set_status(SupplierOnboardingRequirementStatus.EXPIRED.value)
            supplier.set_status(
                OnboardingStatus.INACTIVE
            )  # If the document expires we should consider the Supplier in an inactive state.
            create_task_for_expiration_status(expected_doc_status, doc, sor)


@shared_task(base=Singleton, lock_expiry=60 * 5, queue="ai_workflows")
def generate_supplier_description_task(supplier_id: int):
    try:
        try:
            supplier = Supplier.objects.get(id=supplier_id)
        except Supplier.DoesNotExist:
            error_msg = f"Supplier with ID {supplier_id} does not exist"
            logger.error(error_msg, supplier_id=supplier_id)
            return

        if not supplier.website_url:
            error_msg = f"Supplier '{supplier.name}' has no website URL"
            logger.warning(
                error_msg,
                supplier_id=supplier_id,
                supplier_name=supplier.name,
            )
            return

        result = generate_supplier_description_from_url(
            url=supplier.website_url,
            team_id=str(supplier.team.id),
        )

        if result.success and result.business_description:
            supplier.description = result.business_description
            supplier.save(update_fields=["description", "modified_at"])

            return
        else:
            error_msg = "Failed to generate supplier description"
            logger.warning(
                error_msg, supplier_id=supplier_id, supplier_name=supplier.name
            )
            return

    except Exception as e:
        # Handle unexpected errors
        error_msg = f"Unexpected error in supplier description task: {str(e)}"
        logger.error(
            error_msg,
            supplier_id=supplier_id,
            error=str(e),
            exc_info=True,
        )
        return


# TODO: This is a overall product workaround for the fact that we don't support Group Tasks yet.
# The expectation that a task gets sent to a specific user sort of breaks when it comes to SORs
# As SORs should be able to be sent to a group of people (admins / or a group of users responsible for a category of supplier)
# We first default to the person who is the default requestor of POs as in our model they are generally a default user that tasks go to
# However that user is not guaranteed to exist on every team.
# We use the contractors+ (PH Teams account for a team) in that case.
def get_user_for_doc_expiry_task(team: Team) -> User | None:
    return (
        team.default_requestor
        if team.default_requestor
        else get_didero_ai_user(team=team)
    )


def get_doc_expiring_status(doc: Document) -> DocumentStatus | None:
    if not doc.expiration_date:
        logger.error(
            "ExpiryCheckerBeat: SOR is attached a document with no expiry. Silently ignoring but this is a problem"
        )
        return None

    days_till_expiry = (doc.expiration_date - timezone.now()).days
    if days_till_expiry > 0 and days_till_expiry <= EXPIRATION_TIME_DELTA_DAYS:
        return DocumentStatus.EXPIRING
    elif days_till_expiry <= 0:
        return DocumentStatus.EXPIRED


def create_task_for_expiration_status(
    doc_status: DocumentStatus, doc: Document, sor: SupplierOnboardingRequirement
) -> None:
    task_user = get_user_for_doc_expiry_task(sor.supplier.team)
    if not task_user:
        logger.error(
            f"SOR Expiry Checker: Could not find a user on team {sor.supplier.team.id} to send expiry task to for sor: {sor.id} and doc {doc.id}"
        )

    if doc_status == DocumentStatus.EXPIRING:
        create_document_expiring_task(
            user=get_user_for_doc_expiry_task(sor.supplier.team),
            status=TaskStatus.PENDING.value,
            supplier_id=sor.supplier.id,
            model_id=str(sor.supplier.id),
            model_type=get_content_type_for_model(Supplier),
            supplier_name=sor.supplier.name,
            doc_type=doc.doc_type,
        )
    elif doc_status == DocumentStatus.EXPIRED:
        create_document_expired_task(
            user=get_user_for_doc_expiry_task(sor.supplier.team),
            status=TaskStatus.PENDING.value,
            model_id=str(sor.supplier.id),
            model_type=get_content_type_for_model(Supplier),
            supplier_id=sor.supplier.id,
            supplier_name=sor.supplier.name,
            doc_type=doc.doc_type,
        )


class InvalidCSVHeaderError(Exception):
    """Exception raised for errors in the header of a file."""

    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(self.message)


def process_chunk_suppliers(job: Any, chunk: Any) -> BulkUploadProcessResult:
    """
    Process a chunk of suppliers for the new bulk upload system
    """
    team = job.team
    rows = chunk.data
    suppliers = []
    errors = {}

    logger.info(
        f"=== Starting suppliers chunk {chunk.chunk_number} for team {team.id} ==="
    )
    logger.info(f"Chunk contains {len(rows)} rows to process")
    logger.info(f"Required headers: {REQUIRED_SUPPLIER_HEADERS}")

    for idx, row in enumerate(rows):
        row_number = chunk.start_row + idx
        logger.info(f"\n--- Processing row {row_number} (chunk index {idx}) ---")
        logger.info(f"Raw row data: {row}")

        try:
            # Strip leading and trailing spaces from each cell value
            row = {
                key.strip(): (value.strip() if isinstance(value, str) else "")
                for key, value in row.items()
            }

            supplier_name = row.get("name", "MISSING_NAME")
            logger.info(f"Processing supplier: '{supplier_name}'")

            # Check row length
            if len(row) < len(REQUIRED_SUPPLIER_HEADERS):
                error_msg = f"Row is missing required fields: {row}"
                logger.error(f"Row {row_number} validation failed: {error_msg}")
                errors[row_number] = error_msg
                continue

            # Process website URL
            website = row.get("website_url", "")
            if website and not website.startswith(("http://", "https://")):
                website = f"http://{website}"

            if website:
                for tld in [".com", ".org", ".net", ".edu", ".gov", ".io", ".co"]:
                    if tld in website:
                        tld_pos = website.find(tld)
                        if tld_pos != -1:
                            website = website[: tld_pos + len(tld)]
                            break

            row["website_url"] = website

            # Create/update supplier
            logger.info(
                f"Creating/updating supplier '{supplier_name}' for team {team.id}"
            )
            supplier, created = Supplier.objects.update_or_create(
                name=row["name"],
                team_id=team.id,
                defaults={
                    "website_url": row.get("website_url"),
                    "description": row.get("description"),
                    "notes": row.get("notes", ""),
                    "payment_terms": row.get("payment_terms", ""),
                    "shipping_method": row.get("shipping_method", ""),
                },
            )

            logger.info(
                f"Supplier '{supplier_name}' - {'Created' if created else 'Updated'} (ID: {supplier.id})"
            )

            # Handle domains
            if row["website_url"]:
                domain_name = domain_from_url(row["website_url"])
                supplier.domains.get_or_create(domain=domain_name, team=team)

            suppliers.append(supplier)

            # Handle address creation
            address_fields = [
                row.get("address_line_1"),
                row.get("city"),
                row.get("state_or_province"),
                row.get("country"),
                row.get("postal_code"),
            ]
            logger.info(f"Address fields: {address_fields}")

            if all(address_fields):
                address, address_created = Address.objects.get_or_create(
                    supplier=supplier,
                    team=team,
                    defaults={
                        "line_1": row["address_line_1"],
                        "city": row["city"],
                        "state_or_province": row["state_or_province"],
                        "country": row["country"],
                        "postal_code": row["postal_code"],
                        "is_default": True,
                    },
                )
                logger.info(
                    f"Address - {'Created' if address_created else 'Updated'}: {address}"
                )
            else:
                logger.info("Skipping address creation - missing required fields")

        except Exception as e:
            error_msg = f"Failed to process row {row_number}: {str(e)}"
            logger.error(
                error_msg,
                row_number=row_number,
                row_data=row,
                error_type=type(e).__name__,
                exc_info=True,
            )
            errors[row_number] = error_msg
            continue

    logger.info(
        f"Processed chunk {chunk.chunk_number}: {len(suppliers)} suppliers, {len(errors)} errors"
    )

    return BulkUploadProcessResult(processed_count=len(suppliers), errors=errors)


def process_chunk_supplier_contacts(job: Any, chunk: Any) -> BulkUploadProcessResult:
    """
    Process a chunk of supplier contacts for the new bulk upload system
    """
    team = job.team
    rows = chunk.data
    contacts = []
    errors = {}

    logger.info(
        f"Processing supplier contacts chunk {chunk.chunk_number} for team {team.id}"
    )

    # Cache all suppliers for this team to avoid repeated database queries
    suppliers_cache = {
        supplier.name: supplier for supplier in Supplier.objects.filter(team=team)
    }
    logger.info(f"Cached {len(suppliers_cache)} suppliers for team {team.id}")

    for idx, row in enumerate(rows):
        row_number = chunk.start_row + idx
        try:
            row = {
                key.strip(): (value.strip() if isinstance(value, str) else "")
                for key, value in row.items()
            }

            if len(row) < len(REQUIRED_SUPPLIER_CONTACT_HEADERS):
                errors[row_number] = f"Row is missing something: {row}"
                continue

            try:
                supplier = suppliers_cache[row["supplier_name"]]
            except KeyError:
                errors[row_number] = f"Supplier not found: {row['supplier_name']}"
                continue

            contact, created = SupplierContact.objects.update_or_create(
                supplier=supplier,
                email=row["email"],
                defaults={
                    "name": row.get("name") or None,
                    "phone": row.get("phone") or None,
                },
            )

            contacts.append(contact)
            logger.info(f"Created contact {contact} for supplier {supplier}")

            default_value = row.get("default", "").lower().strip()
            if default_value in ["true", "1", "yes", "y"]:
                supplier.default_contact = contact
                supplier.save(update_fields=["default_contact"])
                logger.info(f"Set {contact} as default contact for supplier {supplier}")

        except Exception as e:
            error_msg = f"Failed to process row {row_number}: {str(e)}"
            logger.error(
                error_msg,
                row_number=row_number,
                row_data=row,
                error_type=type(e).__name__,
            )
            errors[row_number] = error_msg
            continue

    logger.info(
        f"Processed supplier contacts chunk {chunk.chunk_number}: {len(contacts)} contacts, {len(errors)} errors"
    )
    return BulkUploadProcessResult(processed_count=len(contacts), errors=errors)
