from typing import TYPE_CHECKING, Any, Dict

import structlog
from bs4 import BeautifulSoup
from django.contrib.admin.options import get_content_type_for_model
from django.core.exceptions import ValidationError
from rest_framework import serializers

from didero.addresses.models import Address
from didero.addresses.serializers import AddressSerializer
from didero.common.serializers import CustomTagListSerializerField
from didero.documents.models import Document, DocumentLink
from didero.documents.serializers import InnerDocumentSerializer
from didero.emails.models import EmailThread
from didero.emails.serializers import EmailCredentialNylasSerializer
from didero.serializers import PoppableModelSzlr
from didero.suppliers.models import (
    Communication,
    CommunicationEmailRecipient,
    ERPSupplier,
    Supplier,
    SupplierComment,
    SupplierContact,
    SupplierOnboardingRequirement,
    SupplierOnboardingRequirementType,
    SupplychainAlert,
)
from didero.users.serializers import BaseCommentSerializer

logger = structlog.get_logger(__name__)
if TYPE_CHECKING:
    from didero.suppliers.models import Communication


class SupplierContactSerializer(PoppableModelSzlr):
    last_contact = serializers.DateTimeField(read_only=True)

    class Meta:
        model = SupplierContact
        read_only_fields = (
            "id",
            "last_contact",
            "created_at",
        )
        fields = read_only_fields + (
            "name",
            "phone",
            "email",
            "additional_info",
            "archived_at",
        )


class BasicSupplierSerializer(PoppableModelSzlr):
    class Meta:
        model = Supplier
        fields = ("id", "name", "website_url", "archived_at", "onboarding_status")
        read_only_fields = fields


class ContactWithSupplierSerializer(PoppableModelSzlr):
    last_contact = serializers.DateTimeField(read_only=True)
    supplier = BasicSupplierSerializer(read_only=True)

    class Meta:
        model = SupplierContact
        read_only_fields = (
            "id",
            "last_contact",
            "created_at",
            "archived_at",
            "supplier",
        )
        fields = read_only_fields + (
            "name",
            "phone",
            "email",
            "additional_info",
        )


class ERPSupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = ERPSupplier
        fields = (
            "remote_supplier_id",
            "ipaas_supplier_id",
            "erp_provider",
            "erp_app",
        )


class SupplierSerializer(PoppableModelSzlr):
    contacts = serializers.SerializerMethodField()
    last_contact = serializers.SerializerMethodField()
    domains = serializers.SlugRelatedField(
        many=True,
        slug_field="domain",
        read_only=True,
    )
    blocked_email_addresses = serializers.SlugRelatedField(
        many=True,
        slug_field="email_address",
        read_only=True,
    )
    tags = CustomTagListSerializerField(required=False)
    erp_suppliers = ERPSupplierSerializer(many=True, required=False)
    supplier_address = serializers.SerializerMethodField()
    default_contact_detail = serializers.SerializerMethodField()
    default_email = serializers.ReadOnlyField()

    def get_last_contact(self, supplier: Supplier):
        # Return the last_contacted_at field from the supplier model
        return supplier.last_contacted_at

    def get_contacts(self, supplier: Supplier):
        # not ideal to fetch things here but fine for now
        contacts = supplier.contacts.filter(archived_at__isnull=True)
        return SupplierContactSerializer(contacts, many=True, read_only=True).data

    def get_supplier_address(self, supplier: Supplier):
        supplier_address = supplier.addresses.filter(is_default=True).first()
        return AddressSerializer(supplier_address).data if supplier_address else None

    def get_default_contact_detail(self, supplier: Supplier):
        if supplier.default_contact:
            return SupplierContactSerializer(supplier.default_contact).data
        return None

    class Meta:
        model = Supplier
        read_only_fields = (
            "id",
            "onboarding_status",
            "blocked_email_addresses",
            "domains",
            "team",
            "created_at",
            "modified_at",
            "contacts",
            "last_contact",
            "erp_suppliers",
            "supplier_address",
            "tags",
            "default_contact_detail",
            "default_email",
        )
        fields = (
            "name",
            "description",
            "website_url",
            "notes",
            "archived_at",
            "payment_terms",
            "shipping_method",
            "default_contact",
            "default_shipping_terms",
            "available_shipping_terms",
        ) + read_only_fields

    def create(self, validated_data: Dict[str, Any]):
        addresses = validated_data.pop("addresses", [])
        erp_suppliers = validated_data.pop("erp_suppliers", [])

        supplier = Supplier.objects.create(**validated_data)

        for address_data in addresses:
            Address.objects.create(supplier=supplier, **address_data)

        for erp_supplier_data in erp_suppliers:
            ERPSupplier.objects.create(supplier=supplier, **erp_supplier_data)

        return supplier


class SupplierOnboardingRequirementSerializer(serializers.ModelSerializer):
    supplier_name = serializers.SerializerMethodField()
    req_document_doc = serializers.SerializerMethodField()
    req_document_label = serializers.CharField(read_only=True)
    req_information_name = serializers.CharField(read_only=True)
    req_information_value = serializers.CharField(
        max_length=255, required=False, allow_blank=True
    )
    req_checkbox_name = serializers.CharField(max_length=255, read_only=True)
    req_checkbox_value = serializers.BooleanField(required=False)
    status = serializers.CharField(read_only=True)

    class Meta:
        model = SupplierOnboardingRequirement
        read_only_fields = (
            "id",
            "supplier_id",
            "supplier_name",
            "req_type",
            "req_document_type",
            "req_document_label",
            "req_document_doc",
            "req_information_name",
            "req_checkbox_name",
            "status",
        )
        fields = read_only_fields + (
            "req_information_value",
            "req_checkbox_value",
        )

    def validate(self, data: Dict[str, Any]):
        # We don't support POST on this view, so safe to grab the instance on validate.
        instance: SupplierOnboardingRequirement = self.instance
        try:
            instance.validate_field_changes(data)
        except ValidationError as e:
            raise serializers.ValidationError(e.messages)
        return super().validate(data)

    # No direct creation of SORs, only on Supplier creation.
    def create(self, validated_data: Dict[str, Any]):
        raise NotImplementedError(
            "Creation of SupplierOnboardingRequirement objects is not allowed."
        )

    def get_req_document_doc(self, obj: SupplierOnboardingRequirement):
        if obj.req_type != SupplierOnboardingRequirementType.DOCUMENT.value:
            return None

        document = Document.objects.filter(
            links__parent_object_type=get_content_type_for_model(
                SupplierOnboardingRequirement
            ),
            links__parent_object_id=obj.pk,
        ).first()

        if document is None:
            return None

        return InnerDocumentSerializer(document).data

    def get_supplier_name(self, obj: SupplierOnboardingRequirement):
        return obj.supplier.name


class CommunicationEmailRecipientSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommunicationEmailRecipient
        fields = ("email_address",)


class CommunicationSerializer(PoppableModelSzlr):
    contacts = SupplierContactSerializer(many=True, read_only=True)
    email_credential = EmailCredentialNylasSerializer(read_only=True)
    email_to = CommunicationEmailRecipientSerializer(many=True, required=False)
    email_cc = CommunicationEmailRecipientSerializer(many=True, required=False)
    email_bcc = CommunicationEmailRecipientSerializer(many=True, required=False)
    email_excerpt = serializers.SerializerMethodField()
    supplier_id = serializers.PrimaryKeyRelatedField(source="supplier", read_only=True)
    documents = serializers.SerializerMethodField()
    email_thread_thread_id = serializers.SerializerMethodField()

    def get_email_thread_thread_id(self, comm: Communication):
        return comm.email_thread.thread_id if comm.email_thread else None

    def get_email_excerpt(self, comm: Communication):
        if comm.email_content:
            try:
                soup = BeautifulSoup(comm.email_content, features="lxml")
                return soup.get_text(strip=True, separator="\n")
            except Exception as exc:
                print(exc)
        return None

    def get_documents(self, comm: Communication):
        docs = Document.objects.filter(
            links__parent_object_type=get_content_type_for_model(Communication),
            links__parent_object_id=comm.pk,
        )
        return InnerDocumentSerializer(docs, many=True).data

    class Meta:
        model = Communication
        list_fields = (
            "id",
            "team",
            "contacts",
            "comm_type",
            "comm_time",
            "direction",
            "documents",
            "email_credential",
            "email_message_id",
            "email_subject",
            "email_to",
            "email_from",
            "email_cc",
            "email_bcc",
            "email_thread",
            "response_to",
            "email_excerpt",
            "is_draft",
            "user",
        )
        fields = list_fields + (
            "supplier",
            "supplier_id",
            "email_content",
            "email_thread_thread_id",
        )

    def create(self, validated_data: Dict[str, Any]):
        email_to_data = validated_data.pop("email_to", [])
        email_cc_data = validated_data.pop("email_cc", [])
        email_bcc_data = validated_data.pop("email_bcc", [])

        email = Communication.objects.create(**validated_data)

        for address_data in email_to_data:
            CommunicationEmailRecipient(
                email_address=address_data["email_address"], communication_to=email
            ).save()

        for address_data in email_cc_data:
            CommunicationEmailRecipient(
                email_address=address_data["email_address"], communication_cc=email
            ).save()

        for address_data in email_bcc_data:
            CommunicationEmailRecipient(
                email_address=address_data["email_address"], communication_bcc=email
            ).save()

        return email

    def update(self, instance: Communication, validated_data: Dict[str, Any]):
        instance.email_to.all().delete()
        instance.email_cc.all().delete()
        instance.email_bcc.all().delete()

        email_to_data = validated_data.pop("email_to", [])
        email_cc_data = validated_data.pop("email_cc", [])
        email_bcc_data = validated_data.pop("email_bcc", [])

        for address_data in email_to_data:
            CommunicationEmailRecipient(
                email_address=address_data["email_address"], communication_to=instance
            ).save()

        for address_data in email_cc_data:
            CommunicationEmailRecipient(
                email_address=address_data["email_address"], communication_cc=instance
            ).save()

        for address_data in email_bcc_data:
            CommunicationEmailRecipient(
                email_address=address_data["email_address"], communication_bcc=instance
            ).save()

        instance.email_subject = validated_data.get("email_subject")
        instance.email_content = validated_data.get("email_content")
        instance.save()
        return instance


class EmailThreadSerializer(serializers.ModelSerializer):
    most_recent_email = serializers.SerializerMethodField()
    email_count = serializers.SerializerMethodField()
    has_attachment = serializers.SerializerMethodField()

    class Meta:
        model = EmailThread
        fields = (
            "id",
            "thread_id",
            "most_recent_email",
            "email_count",
            "has_attachment",
        )

    def get_most_recent_email(self, obj: EmailThread):
        # Get the most recent email in the thread
        recent_email = obj.emails.order_by("-comm_time").first()
        if recent_email:
            try:
                # need to serialize the email so we can get the excerpt
                serialized_email = CommunicationSerializer(recent_email).data
                if serialized_email:
                    return {
                        "email_subject": serialized_email.get("email_subject"),
                        # the FE just wants to show a preview of the email body (without HTML)
                        "email_preview": serialized_email.get("email_excerpt")[:200]
                        if serialized_email.get("email_excerpt") is not None
                        else "",
                        "email_from": serialized_email.get("email_from"),
                        "email_to": serialized_email.get("email_to"),
                        "email_cc": serialized_email.get("email_cc"),
                        "email_bcc": serialized_email.get("email_bcc"),
                        "time": serialized_email.get("comm_time"),
                    }
                else:
                    logger.info(
                        f"get_most_recent_email could not serialize email: {recent_email.pk}"
                    )
            except Exception as e:
                logger.error(
                    f"Error on get_most_recent_email: {e.with_traceback} [{recent_email.pk}]"
                )
        return None

    def get_email_count(self, obj: EmailThread):
        return obj.emails.count()

    def get_has_attachment(self, obj: EmailThread):
        return any(
            DocumentLink.objects.filter(
                parent_object_type=get_content_type_for_model(Communication),
                parent_object_id=email.id,
            ).exists()
            for email in obj.emails.all()
        )


class EmailThreadDetailSerializer(serializers.ModelSerializer):
    emails = CommunicationSerializer(many=True)

    class Meta:
        model = EmailThread
        fields = ("id", "thread_id", "emails")


# Repetitive to define each Comment's serializer when they're so similar, but
# couldn't find another solution, and this gives us more control.
class SupplierCommentSerializer(BaseCommentSerializer):
    class Meta:
        model = SupplierComment
        read_only_fields = ("id", "created_by", "created_at", "supplier_id")
        fields = read_only_fields + ("comment",)


class SupplychainAlertSerializer(PoppableModelSzlr):
    class Meta:
        model = SupplychainAlert
        read_only_fields = ("id", "topics", "thumbnail_w", "thumbnail_h", "created_at")
        fields = read_only_fields + ("headline", "link", "thumbnail")


class DomainModifySerializer(serializers.Serializer):
    add = serializers.ListField(
        child=serializers.CharField(required=False, max_length=64),
        allow_empty=True,
        required=False,
        max_length=5,
    )
    remove = serializers.ListField(
        child=serializers.CharField(required=False, max_length=64),
        allow_empty=True,
        required=False,
        max_length=20,
    )

    class Meta:
        fields = (
            "add",
            "remove",
        )


class ProductModifySerializer(DomainModifySerializer):
    class Meta:
        fields = (
            "add",
            "remove",
        )
