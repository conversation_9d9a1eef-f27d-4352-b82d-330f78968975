from django.urls import path
from rest_framework.routers import DefaultRouter

from didero.documents.views import DocumentsViewset
from didero.suppliers.views import (
    ContactsViewset,
    ModuleActivationView,
    SupplierCommentsViewset,
    SupplierOnboardingRequirementViewset,
    SupplierViewset,
)
from didero.suppliers.views.supplier_erp_view import (
    AlloySupplierCountView,
    AlloySupplierListView,
)

supplier_domains = SupplierViewset.as_view(
    {"post": "domains", "put": "domains", "patch": "domains"}
)
supplier_email_blocklist = SupplierViewset.as_view({"post": "email_blocklist"})
router = DefaultRouter(trailing_slash=False)
router.register(r"api/suppliers", SupplierViewset, basename="supplier")
router.register(r"api/sors", SupplierOnboardingRequirementViewset, basename="sors")

urlpatterns = [
    path(
        "api/suppliers/<int:supplier_id>/domains",
        supplier_domains,
        name="supplier-domains-modify",
    ),
    path(
        "api/suppliers/<int:supplier_id>/email_blocklist",
        supplier_email_blocklist,
        name="supplier-email-blocklist",
    ),
    path(
        "api/suppliers/<int:supplier_id>/contacts",
        ContactsViewset.as_view({"get": "list", "post": "create"}),
        name="supplier-contact-list",
    ),
    path(
        "api/suppliers/<int:supplier_id>/contacts/<int:pk>",
        ContactsViewset.as_view(
            {
                "get": "retrieve",
                "put": "update",
                "patch": "partial_update",
                "delete": "destroy",
            }
        ),
        name="supplier-contact-detail",
    ),
    path(
        "api/contacts",
        ContactsViewset.as_view(
            {
                "post": "create",
                "get": "list",
            }
        ),
        name="contacts-list",
    ),
    path(
        "api/suppliers/<int:supplier_id>/docs",
        DocumentsViewset.as_view({"get": "list", "post": "create"}),
        name="supplier-doc-list",
    ),
    path(
        "api/suppliers/<int:supplier_id>/docs/<str:uuid>",
        DocumentsViewset.as_view(
            {
                "get": "retrieve",
                "put": "update",
                "patch": "partial_update",
                "delete": "destroy",
            }
        ),
        name="supplier-doc-detail",
    ),
    path(
        "api/suppliers/<int:supplier_id>/comments",
        SupplierCommentsViewset.as_view({"get": "list", "post": "create"}),
        name="supplier-comment-list",
    ),
    path(
        "api/suppliers/<int:supplier_id>/comments/<int:pk>",
        SupplierCommentsViewset.as_view(
            {
                "get": "retrieve",
                "delete": "destroy",
                "put": "update",
                "patch": "partial_update",
            }
        ),
        name="supplier-comment-list",
    ),
    path(
        "api/suppliers/moduleactivate",
        ModuleActivationView.as_view(),
        name="supplier-module-activate",
    ),
    path(
        "api/suppliers/erp-suppliers",
        AlloySupplierListView.as_view(),
        name="alloy-vendors",
    ),
    path(
        "api/suppliers/erp-suppliers/count",
        AlloySupplierCountView.as_view(),
        name="alloy-vendors-count",
    ),
    path(
        "api/suppliers/export_data",
        SupplierViewset.as_view({"get": "export_data"}),
        name="supplier-export-data",
    ),
] + router.urls
