import structlog
from django.db.models import Max, Q
from django.db.utils import IntegrityError
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters import rest_framework as filters
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from rest_framework import status, viewsets
from rest_framework.exceptions import NotFound
from rest_framework.filters import Ordering<PERSON>ilter
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.emails.tasks.nylas_tasks import backfill_supplier_contacts
from didero.filters import (
    ArchiveStatus,
    BaseFilter,
    ChoiceInFilter,
    NumberInFilter,
    get_enum_choices,
)
from didero.suppliers.models import SupplierContact
from didero.suppliers.serializers import (
    ContactWithSupplierSerializer,
    SupplierContactSerializer,
)
from didero.views import APIView, PagesPagination

logger = structlog.get_logger(__name__)


class SupplierContactFilter(BaseFilter):
    name = filters.CharFilter(field_name="name", lookup_expr="exact")  # Case matters!
    name_contains = filters.CharFilter(field_name="name", lookup_expr="icontains")
    supplier_name_contains = filters.CharFilter(
        field_name="supplier__name", lookup_expr="icontains"
    )
    supplier_id = filters.CharFilter(field_name="supplier__id", lookup_expr="exact")
    email = filters.CharFilter(field_name="email", lookup_expr="exact")
    email_contains = filters.CharFilter(field_name="email", lookup_expr="icontains")
    phone = filters.CharFilter(field_name="phone", lookup_expr="exact")
    phone_contains = filters.CharFilter(field_name="phone", lookup_expr="icontains")
    last_contact_after = filters.DateFilter(
        field_name="last_contact", lookup_expr="gte"
    )
    last_contact_before = filters.DateFilter(
        field_name="last_contact", lookup_expr="lte"
    )
    ids = NumberInFilter(field_name="id")
    supplier_ids = NumberInFilter(field_name="supplier__id")
    archived = ChoiceInFilter(
        method="filter_archived", choices=get_enum_choices(ArchiveStatus)
    )

    class Meta:
        model = SupplierContact
        fields = {"supplier": ["exact"]}

    def multi_field_search(self, queryset, name, value):
        return queryset.filter(
            Q(supplier__name__icontains=value)
            | Q(name__icontains=value)
            | Q(email__icontains=value)
            | Q(phone__icontains=value)
            | Q(additional_info__icontains=value)
        ).distinct()

    def filter_queryset(self, queryset):
        # If request specifies archived, then we take that into account
        # Otherwise by default, we do not show contacts that are archived
        if "archived" not in self.data:
            queryset = queryset.filter(archived_at__isnull=True)
        return super().filter_queryset(queryset)


class ContactsViewset(APIView, viewsets.ModelViewSet):
    serializer_class = ContactWithSupplierSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_class = SupplierContactFilter
    # The fields we can sort by:
    ordering_fields = [
        "name",
        "supplier__name",
        "email",
        "last_contact",
    ]
    ordering = ["-last_contact"]  # default ordering is by most recent contact

    def get_supplier(self):
        team = self.request.team
        supplier = team.suppliers.filter(pk=self.kwargs["supplier_id"]).first()
        if not supplier:
            raise NotFound("Your team has no matching supplier")
        return supplier

    def retrieve(self, request, pk, supplier_id):
        # override the retrieve method to NOT filter out archived contacts
        contact = get_object_or_404(
            SupplierContact,
            pk=pk,
            supplier_id=supplier_id,
            supplier__team=self.request.team,
        )
        serializer = self.get_serializer(contact)
        return Response(serializer.data)

    def get_queryset(self):
        team = self.request.team
        # Only return contacts in {suppliers that aren't archived}
        queryset = SupplierContact.objects.filter(
            supplier__team=team,
            supplier__archived_at__isnull=True,
        )
        queryset = queryset.annotate(
            last_contact=Max("comms__comm_time"),
        ).order_by("-name", "-created_at")
        return self.filterset_class(self.request.GET, queryset=queryset).qs.distinct()

    def perform_create(self, serializer):
        assert serializer.is_valid()
        supplier = self.get_supplier()
        contact = serializer.save(supplier=supplier)
        backfill_supplier_contacts.delay(
            self.request.team.pk, supplier.pk, [contact.email]
        )
        return contact

    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except IntegrityError:
            return self.single_error_response(
                detail="A contact has already been added with this email!",
                attr="root",
                status=status.HTTP_409_CONFLICT,
            )

    def destroy(self, request, pk, supplier_id):
        contact = get_object_or_404(
            SupplierContact,
            pk=pk,
            supplier_id=supplier_id,
            supplier__team=self.request.team,
        )
        force = request.query_params.get("force", "").lower() == "true"

        if force:
            # Hard delete: permanently remove contact and all related data
            contact.delete()
            return Response(status=204)
        else:
            # Soft delete: archive the contact (existing behavior)
            if not contact.archived_at:
                contact.archived_at = timezone.now()
                contact.save(update_fields=["archived_at"])
            return Response({}, status=204)

    # Override the partial_update so we can get archived contacts
    def partial_update(self, request, *args, **kwargs):
        contact_pk = kwargs.get("pk")
        # can't use the self.queryset since that filters out archived supplier contacts and we
        # might be trying to unarchive a contact
        # TODO: make the queryset NOT filter out archived contacts. the client should decide that
        contact = get_object_or_404(
            SupplierContact, pk=contact_pk, supplier__team=self.request.team
        )
        contact_serializer = SupplierContactSerializer(
            contact, data=request.data, partial=True
        )

        if contact_serializer.is_valid():
            contact_serializer.save()
            return Response(data=contact_serializer.data, status=status.HTTP_200_OK)
        else:
            logger.error(
                f"Tried to partially update but the serialized object is invalid (id: {contact_pk})"
            )
            return Response(
                contact_serializer.errors, status=status.HTTP_400_BAD_REQUEST
            )
