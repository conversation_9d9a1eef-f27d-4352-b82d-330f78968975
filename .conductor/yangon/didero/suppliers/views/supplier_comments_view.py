from rest_framework.exceptions import NotFound

from didero.suppliers.serializers import SupplierCommentSerializer
from didero.views import BaseCommentViewset


class SupplierCommentsViewset(BaseCommentViewset):
    serializer_class = SupplierCommentSerializer

    def get_parent(self):
        team = self.request.team
        supplier = team.suppliers.filter(pk=self.kwargs["supplier_id"]).first()
        if not supplier:
            raise NotFound("Your team has no matching supplier")
        return supplier

    def get_parent_model_field(self):
        return "supplier"
