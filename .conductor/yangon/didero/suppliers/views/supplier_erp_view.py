from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.integrations.models import TeamIntegratedAppCredential
from didero.integrations.utils.alloy import get_alloy_vendor_count, get_alloy_vendors
from didero.suppliers.models import ERPSupplier
from didero.views import APIView


class AlloySupplierListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if "app" not in request.query_params:
            return Response({"error": "app is required"}, status=400)

        erp_app = request.query_params["app"]
        alloy_credential = TeamIntegratedAppCredential.objects.get(
            team=request.team, app_name=erp_app
        )

        page_number = request.query_params.get("page_number", 1)
        page_size = request.query_params.get("page_size", 25)
        vendor_name_contains = request.query_params.get("search_term", "")

        res = get_alloy_vendors(
            alloy_credential.credential_id,
            page_number,
            page_size,
            vendor_name_contains,
        )

        for vendor in res:
            try:
                supplier_relation = ERPSupplier.objects.get(
                    ipaas_supplier_id=vendor["id"],
                )
                vendor["didero_id"] = supplier_relation.supplier.id
            except ERPSupplier.DoesNotExist:
                vendor["didero_id"] = None

        return Response(res)


class AlloySupplierCountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if "app" not in request.query_params:
            return Response({"error": "app is required"}, status=400)

        erp_app = request.query_params["app"]
        alloy_credential = TeamIntegratedAppCredential.objects.get(
            team=request.team, app_name=erp_app
        )

        res = get_alloy_vendor_count(alloy_credential.credential_id)
        return Response(res)
