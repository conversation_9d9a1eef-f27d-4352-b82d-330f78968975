import re

from bs4 import BeautifulSoup

from didero.documents.schemas import DocumentType
from didero.suppliers.schemas import SupplierOnboardingRequirementType


class SupplierOnboardingRequirementConfig:
    req_type: SupplierOnboardingRequirementType
    doc_type: DocumentType
    name: str

    def __init__(self, **kwargs) -> None:
        req_type = kwargs.get("req_type")
        doc_type = kwargs.get("doc_type")
        name = kwargs.get("name")

        self.req_type = self.req_type_str_to_req_type(req_type)
        self.doc_type = self.doc_type_str_to_doc_type(doc_type) if doc_type else None
        self.name = name

        self.validate_config()

    def doc_type_str_to_doc_type(self, doc_type: str) -> DocumentType:
        try:
            return DocumentType[doc_type.upper()]
        except KeyError:
            raise InvalidSupplierOnboardingRequirementConfigException(
                f"Invalid onboarding document requirement type: {doc_type}"
            )

    def req_type_str_to_req_type(
        self, req_type: str
    ) -> SupplierOnboardingRequirementType:
        try:
            return SupplierOnboardingRequirementType[req_type.upper()]
        except KeyError:
            raise InvalidSupplierOnboardingRequirementConfigException(
                f"Invalid onboarding requirement type: {req_type}"
            )

    def validate_config(self):
        if self.req_type == SupplierOnboardingRequirementType.DOCUMENT:
            if not self.doc_type:
                raise InvalidSupplierOnboardingRequirementConfigException(
                    "req_type is document but no doc_type specified"
                )
        elif self.req_type == SupplierOnboardingRequirementType.INFORMATION:
            if not self.name:
                raise InvalidSupplierOnboardingRequirementConfigException(
                    "req_type is information but no name specified"
                )
        elif self.req_type == SupplierOnboardingRequirementType.CHECKBOX:
            if not self.name:
                raise InvalidSupplierOnboardingRequirementConfigException(
                    "req_type is checkbox but no name specified"
                )


def format_email_text_to_str(email_txt: str) -> str:
    """
    Identify if there are HTML tags and modify the email so it is formatted as a string.
    Also, replace multiple newlines with a single newline, newline followed by a space with a single newline,
    and newline followed by a tab with a single newline.

    The email_txt is sometimes an html text and sometimes a string; we always parse it to a string.

    Args:
        email_txt: The email text to format

    Returns:
        str: The formatted email text
    """

    # Parse the email text using BeautifulSoup to handle HTML content
    soup = BeautifulSoup(email_txt, "html.parser")

    # Extract text from the parsed HTML, separating lines with newline characters
    email_txt = soup.get_text(separator="\n")

    # Replace multiple newlines with a single newline
    email_txt = re.sub(r"\n+", "\n", email_txt)

    # Replace newlines followed by spaces with a single newline
    email_txt = re.sub(r"\n\s+", "\n", email_txt)

    # Replace newlines followed by tabs with a single newline
    email_txt = re.sub(r"\n\t+", "\n", email_txt)

    return email_txt


class InvalidSupplierOnboardingRequirementConfigException(Exception):
    base_message = "Supplier Onboarding Requirement Configuration error: "

    def __init__(self, error: str | None):
        super().__init__(f"{self.base_message}{error}")
