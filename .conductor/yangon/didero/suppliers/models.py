import hashlib
import os
from itertools import chain
from typing import TYPE_CHECKING, Any, Dict, TypeVar

import structlog
from auditlog.registry import auditlog
from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.contrib.postgres.indexes import GinIndex
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.utils import timezone
from django_cryptography.fields import encrypt
from taggit.managers import TaggableManager

from didero.common.models import TaggedItem
from didero.documents.schemas import DocumentType
from didero.integrations.schemas import ERP_APPS, IntegrationProvider
from didero.models import BaseComment, BaseModel, BaseModelWithWebsocketPublishing
from didero.orders.schemas import SHIPPING_TERMS_CHOICES
from didero.suppliers.schemas import (
    OnboardingStatus,
    SupplierOnboardingRequirementStatus,
    SupplierOnboardingRequirementType,
)
from didero.suppliers.utils import (
    InvalidSupplierOnboardingRequirementConfigException,
    SupplierOnboardingRequirementConfig,
    format_email_text_to_str,
)
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums
from didero.utils.utils import clean_website_url, is_domain

logger = structlog.get_logger(__name__)

if TYPE_CHECKING:
    pass


def validate_domain(value: str):
    if value is not None and not is_domain(value):
        raise ValidationError(f"{value} is not a valid domain")


class SupplierDomain(BaseModelWithWebsocketPublishing):
    domain = models.CharField(
        max_length=128, db_index=True, validators=[validate_domain]
    )
    supplier = models.ForeignKey(
        "suppliers.Supplier",
        on_delete=models.CASCADE,
        related_name="domains",
    )
    team = models.ForeignKey(
        "users.Team",
        on_delete=models.CASCADE,
        related_name="domains",
    )

    class Meta:
        unique_together = (("domain", "team"),)

        # we search by domain and team when an email is received
        indexes = [
            models.Index(fields=["domain", "team"]),
        ]

    def __str__(self):
        return f"{self.domain} ({self.supplier})"


auditlog.register(SupplierDomain)

T = TypeVar("T", bound="Supplier")


class SupplierManager(models.Manager[T]):
    # We defer (don't select) the embedding field by default to avoid memory issues
    # The embedding field is not exposed to users and is quite large.
    def get_queryset(self):
        return super().get_queryset().defer("embedding")

    def get_queryset_with_embedding(self):
        return super().get_queryset()


class Supplier(BaseModelWithWebsocketPublishing):
    # Define a custom manager for this model
    objects: SupplierManager["Supplier"] = SupplierManager()  # pyright: ignore - Django doesn't type hint an overriden manager well.

    # Model fields defined below
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=256)
    website_url = models.URLField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    notes = models.TextField(blank=True)
    payment_terms = models.TextField(blank=True)
    shipping_method = models.TextField(blank=True)
    default_shipping_terms = models.CharField(
        max_length=50,  # Increased length to accommodate custom terms
        null=True,
        blank=True,
        help_text="Default shipping terms for purchase orders with this supplier",
    )
    available_shipping_terms = ArrayField(
        models.CharField(max_length=50),  # Removed choices constraint
        default=list,
        null=True,
        blank=True,
        help_text="List of shipping terms this supplier supports (including custom terms)",
    )
    embedding = ArrayField(
        models.FloatField(),
        blank=True,
        null=True,
        help_text="Vector embedding of the supplier name for semantic matching",
    )
    embedding_metadata = models.JSONField(
        null=True,
        blank=True,
        help_text="Metadata about the embedding model used (type, version, dimensions)",
    )

    team = models.ForeignKey["Team"](
        "users.Team",
        on_delete=models.CASCADE,
        related_name="suppliers",
    )
    onboarding_status = models.CharField(
        max_length=20,
        choices=[(status.value, status.name) for status in OnboardingStatus],
        default=OnboardingStatus.ACTIVE.value,
    )
    archived_at = models.DateTimeField(
        null=True, default=None, db_index=True, blank=True
    )
    tags = TaggableManager(through=TaggedItem, blank=True)
    default_contact = models.ForeignKey(
        "suppliers.SupplierContact",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="suppliers_as_default",
        help_text="Default contact for this supplier used for general communications",
    )
    alert_ops_team = models.BooleanField(
        default=False,
        help_text="When toggled on, emails to this supplier (that came from an email credential that's also toggled on), will go through Zapier->Slack for the ops team.",
    )
    last_contacted_at = models.DateTimeField(
        null=True,
        blank=True,
        db_index=True,
        help_text="Timestamp of the most recent communication with this supplier",
    )

    # Define reverse relationships for models that have a FK relationship with this model.
    # We use type hints to ensure that the correct model types are used.
    onboarding_reqs: models.QuerySet["SupplierOnboardingRequirement"]
    blocked_email_addresses: models.QuerySet["BlockedEmailAddress"]
    contacts: models.QuerySet["SupplierContact"]
    domains: models.QuerySet["SupplierDomain"]

    class Meta:
        unique_together = [("team", "website_url")]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.pk:
            self._original_name = self.name

    def clean(self):
        if self.website_url:
            self.website_url = clean_website_url(self.website_url)

        # Validate that default_contact belongs to this supplier
        if self.default_contact and self.default_contact.supplier != self:
            raise ValidationError(
                {"default_contact": "The default contact must belong to this supplier."}
            )

    def save(self, *args, **kwargs):
        import logging

        logger = logging.getLogger(__name__)

        self.full_clean()
        is_new_object = not self.pk

        logger.info(
            f"Supplier save - PK: {self.pk}, Name: {self.name}, is_new_object: {is_new_object}"
        )
        logger.info(
            f"_original_name value: {getattr(self, '_original_name', 'NOT_SET')}"
        )

        # Auto-generate embedding if name has changed or is new
        if is_new_object or self.name != getattr(self, "_original_name", None):
            logger.info(
                f"Updating embedding for supplier {self.name} (new={is_new_object})"
            )
            from didero.ai.utils import get_text_embedding

            self.embedding, self.embedding_metadata = get_text_embedding(self.name)

        sor_configs = self.get_supplier_onboarding_config_for_team()
        if is_new_object and len(sor_configs) > 0:
            # Make this an atomic transaction so that SORs and Supplier and guaranteed to be created together
            # We need to save the supplier before the SORs can be created (SORs reference the supplier)
            with transaction.atomic():
                self.onboarding_status = OnboardingStatus.PENDING
                super().save(*args, **kwargs)
                self.create_sors_for_supplier(sors_configs=sor_configs)
        else:
            super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.pk}: {self.name} ({self.website_url})"

    def archive(self):
        self.archived_at = timezone.now()
        self.save()
        return self

    def is_archived(self) -> bool:
        return self.archived_at is not None

    @property
    def default_email(self) -> str | None:
        """Get the email address of the default contact, if available."""
        return self.default_contact.email if self.default_contact else None

    def add_new_contacts_by_email(self, potential_contacts: list[Dict[str, Any]]):
        """
        Inputs: [{'name': 'Joe Alcorn', 'email': '<EMAIL>'}]
        """

        # before we add anything we need to filter out contacts that we
        # shouldn't add or should auto-archive
        to_create = set()
        team_domains = self.team.find_domains()
        supplier_domains = set(self.domains.values_list("domain", flat=True))
        archive_checklist = ["noreply", "donotreply"]
        for con in potential_contacts:
            inbox, domain = con["email"].lower().split("@", 1)
            if domain not in supplier_domains:
                is_subdomain = any([domain.endswith(f".{d}") for d in supplier_domains])
                if not is_subdomain:
                    # Skip if this contact domain hasn't been set as a domain for the Supplier
                    logger.info(
                        "supplier.add_contact.skip",
                        contact_domain=domain,
                        supplier_domains=supplier_domains,
                        supplier_id=self.pk,
                    )
                    continue
                # this is a subdomain, add to supplier_domains to avoid the check next time
                # todo: should we add this as a mailing domain automatically?
                supplier_domains.add(domain)
                logger.info(
                    "is subdomain",
                    domain=domain,
                    supplier_domains=supplier_domains,
                )

            if domain in team_domains:
                # we don't want our team members to appear as contacts
                continue

            if domain == settings.INBOUND_MAIL_DOMAIN:
                continue

            sanitized_inbox = inbox.replace("-", "").replace("_", "").lower()
            is_noreply = any([word in sanitized_inbox for word in archive_checklist])
            if is_noreply:
                logger.debug(
                    "supplier.add_contact.noreply",
                    inbox=inbox,
                    domain=domain,
                    sanitized=sanitized_inbox,
                    supplier_domains=supplier_domains,
                    supplier_id=self.pk,
                )
                continue

            to_create.add(con["email"].lower())

        existing = self.contacts.filter(email__in=to_create).exclude(email="")
        existing_without_name = existing.filter(name="")
        to_update = {c.email.lower(): c for c in existing_without_name}
        to_create = to_create - set(existing.values_list("email", flat=True))

        filtered_contacts = [
            c
            for c in potential_contacts
            if c["email"].lower() in chain(to_create, to_update.keys())
        ]
        new_contacts_by_email = {}
        for new_contact in filtered_contacts:
            email = new_contact["email"].lower()
            inbox, domain = email.split("@", 1)
            contact_name = new_contact.get("name", "")

            if email in to_update:
                contact = to_update[email]
                if contact_name and not contact.name:
                    contact.name = contact_name
                    contact.save(update_fields=["name"])
                    logger.debug(
                        "updated contact name",
                        name=contact_name,
                        contact_id=contact.pk,
                    )
                    new_contacts_by_email.update({email: contact})
                continue

            sanitized_inbox = domain.replace("-", "").replace("_", "").lower()
            is_noreply = any([word in sanitized_inbox for word in archive_checklist])
            # could bulk create here if this gets used a lot
            contact = SupplierContact.objects.create(
                name=contact_name,
                email=email,
                phone="",
                supplier=self,
                archived_at=timezone.now() if is_noreply else None,
            )
            new_contacts_by_email.update({email: contact})

        logger.debug(
            "supplier.add_contact.complete",
            total=len(new_contacts_by_email),
            to_update=len(to_update),
            supplier_id=self.pk,
        )
        return new_contacts_by_email

    def set_status(self, status: OnboardingStatus):
        self.onboarding_status = status
        self.save()

    @property
    def email_blocklist(self):
        return self.blocked_email_addresses.values_list("email_address", flat=True)

    def create_sors_for_supplier(
        self, sors_configs: list[SupplierOnboardingRequirementConfig]
    ) -> None:
        for config in sors_configs:
            if config.req_type == SupplierOnboardingRequirementType.DOCUMENT:
                SupplierOnboardingRequirement.objects.create(
                    supplier=self,
                    team=self.team,
                    req_type=config.req_type,
                    req_document_label=config.name,
                    req_document_type=config.doc_type,
                )
            elif config.req_type == SupplierOnboardingRequirementType.INFORMATION:
                SupplierOnboardingRequirement.objects.create(
                    supplier=self,
                    team=self.team,
                    req_type=config.req_type,
                    req_information_name=config.name,
                )
            elif config.req_type == SupplierOnboardingRequirementType.CHECKBOX:
                SupplierOnboardingRequirement.objects.create(
                    supplier=self,
                    team=self.team,
                    req_type=config.req_type,
                    req_checkbox_name=config.name,
                    req_checkbox_value=False,
                )
            else:
                raise InvalidSupplierOnboardingRequirementConfigException(
                    f"Invalid req_type: {config.req_type}"
                )

    def get_supplier_onboarding_config_for_team(
        self,
    ) -> list[SupplierOnboardingRequirementConfig]:
        team = self.team
        try:
            sor_config_json = TeamSetting.objects.get(
                team=team,
                name=TeamSettingEnums.TEAM_SUPPLIER_ONBOARDING_REQUIREMENTS.value,
            ).value
        except TeamSetting.DoesNotExist:
            logger.warn(
                f"Could not find TEAM_SUPPLIER_ONBOARDING_REQUIREMENT for team {team.id}, skipping SOR creation"
            )
            sor_config_json = []

        sors_configs = [
            SupplierOnboardingRequirementConfig(**sor) for sor in sor_config_json
        ]
        return sors_configs


auditlog.register(Supplier)


@receiver(post_save, sender=Supplier)
def generate_supplier_description(
    sender: Supplier, instance: Supplier, **kwargs: dict[str, Any]
):
    if instance.website_url and not instance.description:
        from didero.suppliers.tasks import generate_supplier_description_task

        generate_supplier_description_task.apply_async(args=(instance.id,), countdown=3)


# This is a flexible data model. The only required fields are supplier and requirement type
# The fields that are required after that depend on the req_type
# If adding new fields, follow the convention req_{req_type}_your_field_name
# As this is a flexbible model, a lot of the validation for this model will need to be done at the view / api level.
# Django doesn't support a nice way to do this out of the box on the model level.
# However we want to do this validation on the model level, so this is the solution.
class SupplierOnboardingRequirement(BaseModelWithWebsocketPublishing):
    id = models.AutoField(primary_key=True)
    supplier = models.ForeignKey["Supplier"](
        "suppliers.Supplier",
        on_delete=models.CASCADE,
        related_name="onboarding_reqs",
    )
    team = models.ForeignKey["Team"]("users.Team", on_delete=models.CASCADE, null=True)
    req_type = models.CharField(
        choices=[
            (req_type.value, req_type.name)
            for req_type in SupplierOnboardingRequirementType
        ],
    )

    # All fields below are optional.
    # Use the naming convention req_{req_type}_your_field.
    req_document_label = models.CharField(
        blank=True, null=True
    )  # optional label for document to render on FE.
    req_document_type = models.CharField(
        choices=[(doc_type.value, doc_type.name) for doc_type in DocumentType],
        max_length=20,
        blank=True,
        null=True,
    )
    req_information_name = models.CharField(blank=True, null=True)
    req_information_value = models.CharField(blank=True, null=True)
    req_checkbox_name = models.CharField(blank=True, null=True)
    req_checkbox_value = models.BooleanField(blank=True, null=True)
    status = models.CharField(
        choices=[
            (status.value, status.name)
            for status in SupplierOnboardingRequirementStatus
        ],
        default=SupplierOnboardingRequirementStatus.INCOMPLETE.value,
    )

    def save(self, *args, **kwargs):
        # Validate that the fields being saved are valid for the req_type
        if self.pk:
            self.validate_field_changes(self._get_updated_fields_for_save())
            self._pre_save_move_sor_state()

        obj = super().save(*args, **kwargs)
        self._post_save_update_supplier_state()
        return obj

    def set_status(self, sor_status: SupplierOnboardingRequirementStatus):
        self.status = sor_status
        self.save()

    # This handles the logic for moving SOR state pre save of the object
    # If a value is set for a given requirement (information / checkbox) to a nonempty value
    # Document Requirement state updating is handled on DocumentLink save.
    def _pre_save_move_sor_state(self):
        if self.status == SupplierOnboardingRequirementStatus.INCOMPLETE and (
            self.req_checkbox_value or self.req_information_value
        ):
            self.status = SupplierOnboardingRequirementStatus.COMPLETE.value
        elif self.status == SupplierOnboardingRequirementStatus.COMPLETE:
            if (
                self.req_type == SupplierOnboardingRequirementType.INFORMATION.value
                and not self.req_information_value
            ) or (
                self.req_type == SupplierOnboardingRequirementType.CHECKBOX.value
                and not self.req_checkbox_value
            ):
                self.status = SupplierOnboardingRequirementStatus.INCOMPLETE.value

    # Determine where to move the supplier state.
    def _post_save_update_supplier_state(self):
        supplier: Supplier = self.supplier
        sors = supplier.onboarding_reqs.all()

        all_sors_completed = all(
            sor.status == SupplierOnboardingRequirementStatus.COMPLETE for sor in sors
        )

        if all_sors_completed and supplier.onboarding_status != OnboardingStatus.ACTIVE:
            supplier.set_status(OnboardingStatus.ACTIVE)
        elif (
            not all_sors_completed
            and supplier.onboarding_status == OnboardingStatus.ACTIVE
        ):
            supplier.set_status(OnboardingStatus.INACTIVE)

        # TODO: Move to inactive on not all_sors_completed and supplier is active?

    # We have to do this validation manually since the validation of these fields actually
    # depend of the value of the req_type rather than just the field itself.
    # Django doesn't support a nice way to do this out of the box on the model level.
    # However we want to do this validation on the model level, so this is the solution.
    def _get_updated_fields_for_save(self):
        original = SupplierOnboardingRequirement.objects.get(pk=self.pk)
        updated_fields = {
            field.name: getattr(self, field.name)
            for field in self._meta.fields
            if getattr(original, field.name) != getattr(self, field.name)
        }
        return updated_fields

    def validate_field_changes(self, updated_fields: dict[str, str]):
        if (
            "req_information_value" in updated_fields
            and self.req_type != SupplierOnboardingRequirementType.INFORMATION.value
        ):
            raise ValidationError(
                {
                    "req_information_value": "This field should not be provided unless req_type on the SOR is 'INFORMATION'."
                }
            )
        if (
            "req_checkbox_value" in updated_fields
            and self.req_type != SupplierOnboardingRequirementType.CHECKBOX.value
        ):
            raise ValidationError(
                {
                    "req_checkbox_value": "This field should not be provided unless req_type on the SOR is 'CHECKBOX'."
                }
            )


auditlog.register(SupplierOnboardingRequirement)


class SupplierContact(BaseModelWithWebsocketPublishing):
    name = models.CharField(max_length=256, blank=True)
    phone = models.CharField(max_length=15, blank=True, null=True)
    email = models.EmailField(max_length=256, blank=True, null=True)
    additional_info = models.TextField(blank=True)
    supplier = models.ForeignKey[Supplier](
        Supplier,
        on_delete=models.CASCADE,
        related_name="contacts",
    )
    archived_at = models.DateTimeField(
        null=True, default=None, db_index=True, blank=True
    )

    @property
    def team(self):
        if self._team_cache is None:
            self._team_cache = self.supplier.team
        return self._team_cache

    class Meta:
        unique_together = [("email", "supplier")]

    def clean(self):
        if self.email:
            self.email = self.email.lower()

    def __str__(self):
        return f"{self.name or self.email or self.phone} from {self.supplier.name}"


auditlog.register(SupplierContact)


class CommunicationEmailRecipient(BaseModel):
    """
    In order to account for to, cc, and bcc, we maintain a separate model
    from the Communication model itself.
    The email_address is the to/cc/bcc email, and the communication_* is the
    email itself.
    """

    email_address = encrypt(
        models.EmailField(max_length=255), key=settings.ENCRYPTION_KEYS["email_to"]
    )
    communication_to = models.ForeignKey(
        "suppliers.Communication",
        on_delete=models.CASCADE,
        related_name="email_to",
        null=True,
    )
    communication_cc = models.ForeignKey(
        "suppliers.Communication",
        on_delete=models.CASCADE,
        related_name="email_cc",
        null=True,
    )
    communication_bcc = models.ForeignKey(
        "suppliers.Communication",
        on_delete=models.CASCADE,
        related_name="email_bcc",
        null=True,
    )


class BlockedEmailAddress(models.Model):
    email_address = models.EmailField(max_length=255)
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name="blocked_email_addresses",
        null=True,
    )

    class Meta:
        unique_together = ["email_address", "supplier"]


auditlog.register(BlockedEmailAddress)


# TODO: This needs to be migrated to the emails app.
# https://linear.app/d4o/issue/EPD-2018/move-communication-model-to-emails-app
class Communication(BaseModelWithWebsocketPublishing):
    TYPE_EMAIL = "email"
    TYPE_PHONE = "phone"
    TYPES = (
        (TYPE_EMAIL, "Email"),
        (TYPE_PHONE, "Phone"),
    )

    DIRECTION_INCOMING = "incoming"
    DIRECTION_OUTGOING = "outgoing"
    DIRECTIONS = (
        (DIRECTION_INCOMING, "Incoming"),
        (DIRECTION_OUTGOING, "Outgoing"),
    )
    email_credential = models.ForeignKey(
        "emails.EmailCredentialNylas",
        on_delete=models.SET_NULL,
        related_name="comms",
        null=True,
    )
    team = models.ForeignKey(
        "users.Team",
        on_delete=models.CASCADE,
        related_name="comms",
    )
    supplier = models.ForeignKey(
        "suppliers.Supplier",
        on_delete=models.CASCADE,
        related_name="comms",
    )
    email_thread = models.ForeignKey(
        "emails.EmailThread",
        on_delete=models.CASCADE,
        related_name="emails",
        null=True,
        blank=True,
        default=None,
    )
    contacts = models.ManyToManyField(
        "suppliers.SupplierContact",
        related_name="comms",
    )
    comm_type = models.CharField(max_length=5, choices=TYPES)
    comm_time = models.DateTimeField(
        # sent time of email or logged time of phone call
        default=timezone.now,
        db_index=True,
    )
    direction = models.CharField(max_length=8, choices=DIRECTIONS)

    email_reference_hash = models.CharField(
        max_length=64,
        null=True,
        blank=True,
        db_index=True,
    )
    canonical_body_hash = models.CharField(
        max_length=64,
        null=True,  # Allow null for existing records
        blank=True,
        db_index=True,  # Add index for efficient lookups
        help_text="Hash of normalized email body content for deduplication",
    )
    email_message_id = models.CharField(
        max_length=1024,
        db_index=True,
        unique=True,
        blank=True,
    )
    email_from = encrypt(
        models.EmailField(max_length=255),
        key=settings.ENCRYPTION_KEYS["email_from"],
    )
    gmail_message_id = models.CharField(max_length=255, blank=True, default="")
    email_subject = encrypt(
        # watch out: email subject lines technically have no length limit
        models.CharField(max_length=1024, blank=True),
        key=settings.ENCRYPTION_KEYS["email_subjects"],
    )
    email_content = encrypt(
        models.TextField(blank=True),
        key=settings.ENCRYPTION_KEYS["email_contents"],
    )
    # email_uid
    # todo: encrypt headers
    # https://github.com/georgemarshall/django-cryptography/issues/23
    email_raw_headers = models.JSONField(default=dict, blank=True)
    email_folder = models.CharField(
        max_length=256, blank=True, default="", db_index=True
    )

    response_to = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        related_name="responses",
        null=True,
        blank=True,
        default=None,
    )
    pinecone_reference = models.ForeignKey(
        "emails.PineconeReference",
        on_delete=models.SET_NULL,
        related_name="comms",
        null=True,
        blank=True,
    )
    # The user who sends the email, not the necessarily same as email_from
    user = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        related_name="comms",
        null=True,
        blank=True,
    )
    is_draft = models.BooleanField(default=False)

    # Related names/fields:
    email_to: models.QuerySet["CommunicationEmailRecipient"]
    email_cc: models.QuerySet["CommunicationEmailRecipient"]
    email_bcc: models.QuerySet["CommunicationEmailRecipient"]

    def clean(self):
        if "<" in self.email_from:
            from didero.emails.parsing import parse_display_name_and_address

            self.email_from = parse_display_name_and_address(self.email_from)[1]

    def _add_email_attachment_names(self, email_txt: str):
        # Lazy import of Communication and Document models
        Document = apps.get_model("documents", "Document")
        Communication = apps.get_model("suppliers", "Communication")

        documents = Document.objects.filter(
            links__parent_object_type=ContentType.objects.get_for_model(Communication),
            links__parent_object_id=self.pk,
        )
        if documents.exists():
            email_txt += "\n\nThe email contains the following attachments:\n"
            for document in documents:
                email_txt += f"\n- {document.name}"
        return email_txt

    def as_display_string(self, include_attachment_names: bool = False):
        """
        NEVER LOG THIS STRING as it contains sensitive information
        """

        to_list = list(self.email_to.all().values_list("email_address", flat=True))
        cc_list = list(self.email_cc.all().values_list("email_address", flat=True))
        bcc_list = list(self.email_bcc.all().values_list("email_address", flat=True))
        email_content = format_email_text_to_str(self.email_content)
        email_txt = f"""
        FROM: {self.email_from}
        TO: {to_list}
        CC: {cc_list}
        BCC: {bcc_list}
        SUBJECT: {self.email_subject}
        BODY: {email_content}
        """.strip()
        if include_attachment_names:
            email_txt = self._add_email_attachment_names(email_txt)
        return email_txt


auditlog.register(Communication)


# Signal to upload to Pinecone
@receiver(post_save, sender=Communication)
def process_email(sender, instance, created, **kwargs):
    # We're using a signal so that we don't have to find every place we ingest / upload emails.
    # We should only run processing if the email hasn't been processed yet, so we
    # check for the created flag (i.e. is this a new email).
    def process_email_if_new():
        """
        Check for duplicate emails using both reference hash and canonical body hash.
        Links forwarded emails to original threads and prevents duplicate processing.
        """
        # 1. Check by reference hash first (existing approach)
        emails_same_hash = Communication.objects.filter(
            email_reference_hash=instance.email_reference_hash,
            team=instance.team,
        )
        if emails_same_hash.count() > 1:
            logger.info(
                "Skipping email processing as it has already been processed",
                email_id=instance.id,
                email_reference_hash=instance.email_reference_hash,
                team_id=instance.team.id,
            )
            return

        # 2. For all emails, check if canonical body hash matches existing emails
        if instance.canonical_body_hash:
            # No time/count restrictions - search ALL matching emails for the team
            potential_originals = (
                Communication.objects.filter(
                    team=instance.team,
                    supplier=instance.supplier,
                    comm_type=Communication.TYPE_EMAIL,
                    canonical_body_hash=instance.canonical_body_hash,
                )
                .exclude(id=instance.id)
                .order_by("comm_time")  # Order by oldest first to find the original
            )

            if potential_originals.exists():
                # Get the first (oldest) match - this is likely the original
                original = potential_originals.first()
                logger.info(
                    "Detected duplicate email content using canonical body hash",
                    email_id=instance.id,
                    original_email_id=original.id,
                    team_id=instance.team.id,
                    subject=instance.email_subject,
                    original_subject=original.email_subject,
                )

                # Link to the same thread if one exists
                if original.email_thread:
                    instance.email_thread = original.email_thread
                    instance.save(update_fields=["email_thread"])
                    logger.info(
                        "Linked duplicate email to existing thread",
                        email_id=instance.id,
                        thread_id=original.email_thread.id,
                    )

                # Skip further processing as this is a duplicate
                return

        # If we get here, this is a new email that should be processed
        namespace = str(instance.team.uuid)
        process_email_task.apply_async(args=(instance.id, namespace), countdown=3)

    if created and instance.comm_type == Communication.TYPE_EMAIL:
        # Import here - otherwise, circular import
        from didero.suppliers.tasks import process_email_task

        transaction.on_commit(lambda: process_email_if_new())


@receiver(post_save, sender=Communication)
def update_supplier_last_contacted_at(sender, instance, **kwargs):
    """
    Update the supplier's last_contacted_at field when a communication is created or updated.
    Uses the maximum of the current last_contacted_at and the new comm_time to ensure
    we never move the timestamp backwards.
    Uses transaction.on_commit to ensure the update happens after the communication is saved.
    """
    if not instance.supplier_id:
        return

    def update_last_contacted():
        from django.db.models import Case, F, Value, When
        from django.db.models.functions import Greatest

        # Update last_contacted_at to be the maximum of the current value and comm_time
        # If last_contacted_at is NULL, use comm_time
        Supplier.objects.filter(id=instance.supplier_id).update(
            last_contacted_at=Case(
                When(last_contacted_at__isnull=True, then=Value(instance.comm_time)),
                default=Greatest(F("last_contacted_at"), Value(instance.comm_time)),
            )
        )

    # Use on_commit to ensure this runs after the communication is saved
    transaction.on_commit(update_last_contacted)


class SupplierComment(BaseComment):
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name="comments",
    )


auditlog.register(SupplierComment)


def get_alert_thumb_path(instance, filename: str):
    instance.thumbnail.seek(0)
    contents = instance.thumbnail.read()
    instance.thumbnail.seek(0)
    content_hash = hashlib.sha256(contents).hexdigest()
    _, ext = os.path.splitext(filename)
    new_fname = f"{content_hash}{ext}"
    return f"alerts/thumbs/{new_fname}"


class SupplychainAlert(BaseModel):
    """
    These will show up in the alert center
    of related suppliers.

    Each supplier can individually subscribe to
    one or more topics.
    """

    headline = models.CharField(max_length=64)
    link = models.URLField(blank=True)
    thumbnail_w = models.PositiveSmallIntegerField(null=True, blank=True)
    thumbnail_h = models.PositiveSmallIntegerField(null=True, blank=True)
    thumbnail = models.ImageField(
        upload_to=get_alert_thumb_path,
        null=True,
        width_field="thumbnail_w",
        height_field="thumbnail_h",
        blank=True,
    )
    archived_at = models.DateTimeField(null=True, default=None, blank=True)
    topics = ArrayField(
        models.CharField(max_length=64),
        blank=True,
        default=list,
    )

    class Meta:
        indexes = [GinIndex(fields=["topics"])]


auditlog.register(SupplychainAlert)


class ERPSupplier(BaseModelWithWebsocketPublishing):
    """
    Keep track of suppliers imported from ERPs. They will be mapped to Didero Suppliers
    via the Supplier model.
    """

    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name="erp_suppliers",
    )
    erp_provider = models.CharField(
        choices=[(provider.value, provider.name) for provider in IntegrationProvider],
    )
    erp_app = models.CharField(
        choices=[(app.value, app.name) for app in ERP_APPS],
        max_length=64,
    )
    ipaas_supplier_id = models.CharField(max_length=64)
    remote_supplier_id = models.CharField(max_length=64)

    @property
    def team(self):
        if self._team_cache is None:
            self._team_cache = self.supplier.team
        return self._team_cache

    class Meta:
        indexes = [
            models.Index(fields=["supplier"]),
            models.Index(fields=["ipaas_supplier_id"]),
        ]


class SupplierFollowupConfiguration(BaseModel):
    """
    Supplier-specific overrides for follow-up workflow settings.

    This model stores JSON configuration that overrides team default follow-up
    settings for specific suppliers. This is useful for suppliers who need
    different timing (e.g., slower to respond) or escalation rules.

    Note: Team is accessible via supplier.team, so no separate FK is needed.
    """

    supplier = models.OneToOneField(
        "suppliers.Supplier",
        on_delete=models.CASCADE,
        related_name="followup_configuration",
    )
    configuration_override = models.JSONField(
        help_text="JSON configuration that overrides team follow-up settings"
    )
    notes = models.TextField(
        blank=True,
        help_text="Why these overrides are needed (e.g., 'Slow to respond', 'VIP supplier')",
    )
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who created these overrides",
    )

    @property
    def team(self):
        """Get the team via the supplier relationship."""
        return self.supplier.team

    class Meta:
        verbose_name = "Supplier Follow-up Configuration"
        verbose_name_plural = "Supplier Follow-up Configurations"
        indexes = [
            models.Index(fields=["supplier"], name="suppliers_followup_supplier"),
        ]

    def __str__(self):
        return f"Follow-up config for {self.supplier.name} (Team: {self.team.name})"


# Register models with auditlog for change tracking
auditlog.register(SupplierFollowupConfiguration)
