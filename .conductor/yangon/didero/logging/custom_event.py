import time
from typing import Any, Dict


###
# Extendable base class to create custom events for Grafana in JSON format.
# Do not use this class directly. Extend it to create custom events. See
# {error_event.py} if you're looking to create a custom event for an error.
###
class CustomEvent:
    def __init__(self, event_name: str, level: str = "INFO", **kwargs):
        self.data: Dict[str, Any] = kwargs
        self.data["event"] = event_name
        self.data["time"] = int(time.time())
        self.data["level"] = level
        # In order to get the logger name to be the class name, we need
        # to instantiate it on the implementation of this base class
        # see {document_view_page_event.py} for an example of how to extend this class
        self.logger = None

    def __getitem__(self, key: str) -> Any:
        return self.data.get(key)

    def __setitem__(self, key: str, value: Any) -> None:
        self.data[key] = value

    # To log an event you can just create a new event and call this method:
    # Example:
    # event = CustomEventImplementation(...)
    # event.log_event()
    def log_event(self):
        self.logger.info(**self.data)
