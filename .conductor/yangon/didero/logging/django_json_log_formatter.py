from datetime import datetime, timezone

import json_log_formatter
from opentelemetry import trace


class J<PERSON><PERSON>ormatter(json_log_formatter.JSONFormatter):
    def json_record(self, message, extra, record):
        # remove fields that don't play well with the serializer/duplicates
        safe_delete(extra, "_logger")
        safe_delete(extra, "_name")

        # handle requests
        if "request" in extra:
            # first look to see if this is a django server request
            if record.name == "django.server":
                return handle_django_server_record(message, extra, record)
            # if not a server reuqest, then this will be a WSGIRequest
            # flatten the WSGIRequest to avoid serializer errors
            extra["request_method"] = extra["request"].method
            extra["request"] = extra["request"].path

        # add time if not there
        if "time" not in extra:
            extra["time"] = datetime.now(timezone.utc)

        # Add OpenTelemetry trace context
        current_span = trace.get_current_span()
        if current_span:
            span_context = current_span.get_span_context()
            if span_context.is_valid:
                trace_id = f"{span_context.trace_id:032x}"
                span_id = f"{span_context.span_id:016x}"
                trace_flags = f"{span_context.trace_flags:02x}"

                # Convert OpenTelemetry trace ID to AWS X-Ray format
                # X-Ray format: 1-{8-digit hex timestamp}-{24-digit hex trace ID}
                xray_trace_id = f"1-{trace_id[:8]}-{trace_id[8:32]}"

                # Also add the raw format which is used directly by AWS X-Ray
                # Some AWS services expect this format in logs for correlation
                aws_xray_trace_header = (
                    f"Root=1-{trace_id[:8]}-{trace_id[8:32]};Parent={span_id};Sampled=1"
                )

                extra.update(
                    {
                        "trace_id": trace_id,
                        "span_id": span_id,
                        "trace_flags": trace_flags,
                        "aws.xray.trace_id": xray_trace_id,
                        "x-ray-trace": aws_xray_trace_header,
                    }
                )

        # add the default fields
        extra.update(
            {
                "source": "django",
                "level": record.levelname,
                "name": record.name,
                "lineno": record.lineno,
            }
        )

        # some messages are actually a dictionary of values (eg: celery, requests)
        # add all those values to the json log
        if type(record.msg) is dict:
            # make timestamp the time if it's present
            if "timestamp" in record.msg:
                record.msg["time"] = record.msg["timestamp"]
                del record.msg["timestamp"]

            # add all record.msg fields
            add_fields(extra, record.msg)

        return extra


# helper method to safely delete attributes from a dict
def safe_delete(extra: dict, key: str):
    if key in extra:
        del extra[key]


# go throuhg all the values and add them
# add any logic for special cases like exceptions
def add_fields(extra: dict, record_msg: dict):
    for field_name in record_msg.keys():
        # some exceptions get stored in event field; extract the data
        if field_name == "event":
            extra[field_name] = handle_event(record_msg[field_name])
        else:
            extra[field_name] = record_msg[field_name]


# tasks that error have a lot of repeated data in the event field
# take only the important data from every error type
def handle_event(event):
    try:
        if isinstance(event, Exception):
            event_class = event.__class__
            return f"{event_class.__module__}.{event_class.__name__}"
    except Exception as e:
        print(f"exception = {e}")
    return event


# handle the django server logs
def handle_django_server_record(message, extra, record):
    # record = LogRecord
    # extra = {'request': <socket.socket fd=8, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 61523)>, 'server_time': '02/Jun/2024 16:42:16', 'status_code': 200}
    # message = GET /api/imap/ HTTP/1.1" 200 343
    return {
        "status_code": extra["status_code"],
        "event": record.name,
        "message": message,
        "time": extra["server_time"],
    }
