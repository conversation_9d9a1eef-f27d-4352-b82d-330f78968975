import structlog

from .custom_event import CustomEvent


###
# Base class for error messages. If you're working on a flow that requires more
# details, you can extend this class; if not, you can use as is.
###
class ErrorEvent(CustomEvent):
    def __init__(self, stack_trace: str):
        super().__init__("ErrorEvent", level="ERROR", stack_trace=stack_trace)
        self.logger = structlog.get_logger(__name__)
