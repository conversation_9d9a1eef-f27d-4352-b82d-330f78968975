from decimal import Decimal

from rest_framework import serializers

from didero.addresses.models import Address
from didero.addresses.serializers import AddressSerializer
from didero.common.serializers import CustomTagListSerializerField
from didero.documents.models import Document
from didero.items.models import Item
from didero.items.serializers import ItemSerializer
from didero.orders.models import (
    LineItem,
    OrderApproval,
    OrderItem,
    PurchaseOrder,
    PurchaseOrderComment,
    Shipment,
    ShipmentLineItem,
)
from didero.orders.schemas import (
    SHIPPING_TERMS_CHOICES,
    STATUSES_EDITABLE,
    PaymentStatus,
    PurchaseOrderStatus,
)
from didero.suppliers.models import Supplier
from didero.suppliers.serializers import BaseCommentSerializer, BasicSupplierSerializer
from didero.users.serializers import UserGroupSerializer, UserSerializer


class AddressOrIdField(serializers.Field):
    """
    Custom field that accepts either an address ID or address data.
    If an ID is provided, it uses the existing address.
    If address data is provided, it validates and prepares it for creation.
    """

    def to_representation(self, value):
        if value:
            return AddressSerializer(value).data
        return None

    def to_internal_value(self, data):
        if data is None:
            return None

        # If it's just an ID, return the address instance
        if isinstance(data, (int, str)) and str(data).isdigit():
            try:
                return Address.objects.get(pk=int(data))
            except Address.DoesNotExist:
                raise serializers.ValidationError(
                    f"Address with id {data} does not exist."
                )

        # If it's a dictionary with just an ID, treat it as an ID
        if isinstance(data, dict) and "id" in data and len(data) == 1:
            try:
                return Address.objects.get(pk=data["id"])
            except Address.DoesNotExist:
                raise serializers.ValidationError(
                    f"Address with id {data['id']} does not exist."
                )

        # Otherwise, validate it as address data for creation
        if isinstance(data, dict):
            # Remove ID if present (we're creating a new address)
            data.pop("id", None)
            # Use AddressSerializer to validate the data
            serializer = AddressSerializer(data=data)
            serializer.is_valid(raise_exception=True)
            return serializer.validated_data

        raise serializers.ValidationError(
            "Address must be either an ID or address data."
        )


class OrderItemSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    item = ItemSerializer(read_only=True)
    item_id = serializers.PrimaryKeyRelatedField(
        queryset=Item.objects.all(), source="item", write_only=True
    )

    class Meta:
        model = OrderItem
        fields = (
            "id",
            "item",
            "item_id",
            "quantity",
            "price",
            "price_currency",
            "unit_of_measure",
            "units_per_measure",
            "requested_date",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        quantity = representation.get("quantity")
        price = representation.get("price")
        if quantity is not None:
            representation["quantity"] = float(Decimal(quantity).normalize())
        if price is not None:
            representation["price"] = float(Decimal(price).normalize())
        return representation


class LineItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = LineItem
        fields = ("id", "category", "description", "amount", "amount_currency")


class OrderApprovalSerializer(serializers.ModelSerializer):
    approver = UserSerializer(read_only=True)
    approver_group = UserGroupSerializer(read_only=True)

    class Meta:
        model = OrderApproval
        read_only_fields = (
            "id",
            "approver",
            "approver_group",
            "requested_at",
            "approved_at",
            "denied_at",
        )
        fields = read_only_fields


class ShipmentLineItemInnerSerializer(serializers.ModelSerializer):
    order_item = OrderItemSerializer(read_only=True)
    order_item_id = serializers.PrimaryKeyRelatedField(
        queryset=OrderItem.objects.all(), source="order_item", write_only=True
    )

    class Meta:
        model = ShipmentLineItem
        fields = (
            "order_item",
            "order_item_id",
            "shipped_quantity",
        )


class ShipmentSerializer(serializers.ModelSerializer):
    purchase_order_id = serializers.PrimaryKeyRelatedField(
        queryset=PurchaseOrder.objects.all(),
        source="purchase_order",
        write_only=True,
        required=False,
        allow_null=True,
    )
    line_items = ShipmentLineItemInnerSerializer(many=True, required=False)

    class Meta:
        model = Shipment
        fields = (
            "id",
            "purchase_order_id",
            "carrier",
            "carrier_type",
            "tracking_number",
            "shipment_date",
            "estimated_delivery_date",
            "actual_delivery_date",
            "line_items",
            "status",
        )

    def create(self, validated_data):
        """Use the custom manager to create shipment and update PO status"""
        line_items_data = validated_data.pop("line_items", [])
        shipment = Shipment.objects.create_shipment(**validated_data)
        for line_item_data in line_items_data:
            ShipmentLineItem.objects.create(shipment=shipment, **line_item_data)
        return shipment


class BasicPurchaseOrderSerializer(serializers.ModelSerializer):
    supplier_id = serializers.SerializerMethodField()

    class Meta:
        model = PurchaseOrder
        fields = ("id", "po_number", "order_status", "supplier_id")

    def get_supplier_id(self, obj):
        return obj.supplier.id if obj.supplier else None


class PurchaseOrderSerializer(serializers.ModelSerializer):
    class OrderDocumentSerializer(serializers.ModelSerializer):
        class Meta:
            model = Document
            fields = ("uuid", "name", "document", "content_hash", "filesize_bytes")

    po_number = serializers.CharField(max_length=255, required=False)
    supplier = BasicSupplierSerializer(read_only=True, required=False)
    items = OrderItemSerializer(many=True, required=False)
    line_items = LineItemSerializer(many=True, required=False)
    approvals = OrderApprovalSerializer(many=True, required=False)
    placed_by = UserSerializer(read_only=True)
    placed_by_id = serializers.IntegerField(required=False)
    external_requestor_email = serializers.EmailField(read_only=True, required=False)
    requestor_email = serializers.SerializerMethodField()
    requestor_display_name = serializers.SerializerMethodField()
    sender_address = AddressOrIdField(required=False)
    shipping_address = AddressOrIdField(required=False)
    tags = CustomTagListSerializerField(required=False)
    document = OrderDocumentSerializer(read_only=True, required=False)
    shipments = ShipmentSerializer(many=True, required=False)

    requested_date = serializers.DateField(required=False, allow_null=True)
    shipping_terms = serializers.CharField(
        max_length=50,
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="Shipping terms (supports both standard Incoterms and custom terms)",
    )
    date_field_label = serializers.SerializerMethodField()
    date_help_text = serializers.SerializerMethodField()

    supplier_id = serializers.PrimaryKeyRelatedField(
        write_only=True,
        queryset=Supplier.objects.all(),
        source="supplier",
        allow_null=True,
        required=False,
    )

    class Meta:
        model = PurchaseOrder
        fields = (
            "id",
            "team",
            "supplier",
            "supplier_id",
            "po_number",
            "order_status",
            "payment_status",
            "requested_date",
            "shipping_terms",
            "date_field_label",
            "date_help_text",
            "items",
            "line_items",
            "source",
            "placed_by_id",
            "placed_by",
            "external_requestor_email",
            "requestor_email",
            "requestor_display_name",
            "approvals",
            "internal_notes",
            "vendor_notes",
            "payment_terms",
            "shipping_method",
            "placement_time",
            "tags",
            "archived_at",
            "sender_address",
            "shipping_address",
            "total_cost",
            "total_cost_currency",
            "document",
            "shipments",
            "is_external_po_processing",
            "is_po_editable",
        )

    def get_is_po_editable(self, obj: PurchaseOrder):
        return obj.order_status in STATUSES_EDITABLE and obj.is_po_editable

    def get_date_field_label(self, obj: PurchaseOrder):
        return obj.get_date_field_label()

    def get_date_help_text(self, obj: PurchaseOrder):
        return obj.get_date_help_text()

    def get_requestor_email(self, obj: PurchaseOrder):
        return obj.requestor_email

    def get_requestor_display_name(self, obj: PurchaseOrder):
        return obj.requestor_display_name

    def to_representation(self, instance):
        """Return the order data with the new requested_date and shipping_terms fields"""
        data = super().to_representation(instance)

        # Ensure requested_date is properly serialized
        if instance.requested_date:
            data["requested_date"] = instance.requested_date.isoformat()

        return data

    def create(self, validated_data):
        items_data = validated_data.pop("items", [])
        line_items_data = validated_data.pop("line_items", [])
        sender_address = validated_data.pop("sender_address", None)
        shipping_address = validated_data.pop("shipping_address", None)

        # Pre-populate shipping terms from supplier default if not provided
        if not validated_data.get("shipping_terms") and validated_data.get("supplier"):
            supplier = validated_data["supplier"]
            if (
                hasattr(supplier, "default_shipping_terms")
                and supplier.default_shipping_terms
            ):
                validated_data["shipping_terms"] = supplier.default_shipping_terms

        # Handle address creation/reuse
        # If address is already an Address instance, use it directly
        # If it's a dict, create a new address
        if sender_address and isinstance(sender_address, dict):
            sender_address = Address.objects.create(**sender_address)

        if shipping_address and isinstance(shipping_address, dict):
            shipping_address = Address.objects.create(**shipping_address)

        order = PurchaseOrder.objects.create(
            sender_address=sender_address,
            shipping_address=shipping_address,
            **validated_data,
        )

        for item_data in items_data:
            OrderItem.objects.create(purchase_order=order, **item_data)
        for line_item_data in line_items_data:
            LineItem.objects.create(purchase_order=order, **line_item_data)

        return order

    def update(self, instance: PurchaseOrder, validated_data):
        if "items" in validated_data:
            instance.items.all().delete()

        if "line_items" in validated_data:
            instance.line_items.all().delete()

        items_data = validated_data.pop("items", [])
        line_items_data = validated_data.pop("line_items", [])
        sender_address = validated_data.pop("sender_address", None)
        shipping_address = validated_data.pop("shipping_address", None)

        # Handle sender address
        if sender_address is not None:
            if isinstance(sender_address, Address):
                # Using existing address
                instance.sender_address = sender_address
            elif isinstance(sender_address, dict):
                # Creating new address
                instance.sender_address = Address.objects.create(**sender_address)

        # Handle shipping address
        if shipping_address is not None:
            if isinstance(shipping_address, Address):
                # Using existing address
                instance.shipping_address = shipping_address
            elif isinstance(shipping_address, dict):
                # Creating new address
                instance.shipping_address = Address.objects.create(**shipping_address)

        for item_data in items_data:
            OrderItem.objects.create(
                purchase_order=instance,
                **item_data,
            )

        for line_item_data in line_items_data:
            LineItem.objects.create(purchase_order=instance, **line_item_data)

        for key, value in validated_data.items():
            setattr(instance, key, value)
        instance.save()

        return instance


class PurchaseOrderCommentSerializer(BaseCommentSerializer):
    class Meta:
        model = PurchaseOrderComment
        read_only_fields = ("id", "created_by", "created_at")
        fields = read_only_fields + ("comment",)


class PurchaseOrderStatusSerializer(serializers.Serializer):
    order_status = serializers.ChoiceField(
        choices=[(tag.value, tag.name) for tag in PurchaseOrderStatus], required=False
    )


class PaymentStatusSerializer(serializers.Serializer):
    payment_status = serializers.ChoiceField(
        choices=[(tag.value, tag.name) for tag in PaymentStatus], required=False
    )
