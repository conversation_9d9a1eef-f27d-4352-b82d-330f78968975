from django.urls import path
from rest_framework.routers import DefaultRouter

from didero.documents.views import DocumentsViewset
from didero.orders.views import (
    ApprovalAssigneesView,
    OrderItemViewSet,
    PurchaseOrderCommentViewSet,
    PurchaseOrderCountView,
    PurchaseOrderViewset,
)
from didero.orders.views.shipment_views import ShipmentViewSet

router = DefaultRouter()

urlpatterns = [
    path(
        "api/orders",
        PurchaseOrderViewset.as_view(
            {
                "post": "create",
                "get": "list",
            }
        ),
        name="purchase-order-list",
    ),
    path(
        "api/orders/<int:pk>",
        PurchaseOrderViewset.as_view(
            {
                "get": "retrieve",
                "put": "update",
                "delete": "destroy",
            }
        ),
        name="purchase-order-detail",
    ),
    path(
        "api/orders/<int:pk>/reference_count",
        PurchaseOrderViewset.as_view({"get": "get_reference_count"}),
        name="order-reference-count",
    ),
    path(
        "api/orders/<int:po_pk>/items",
        OrderItemViewSet.as_view({"get": "list", "post": "create"}),
        name="order-items-create",
    ),
    path(
        "api/orders/<int:po_pk>/items/<int:order_item_pk>",
        OrderItemViewSet.as_view(
            {
                "get": "retrieve",
                "put": "update",
                "patch": "partial_update",
                "delete": "destroy",
            }
        ),
        name="order-items-update",
    ),
    path(
        "api/orders/<int:po_pk>/items/bulk_delete",
        OrderItemViewSet.as_view({"delete": "bulk_delete"}),
        name="order-items-bulk-delete",
    ),
    path(
        "api/orders/pdf",
        PurchaseOrderViewset.as_view({"post": "generate_pdf"}),
        name="purchase-order-pdf",
    ),
    path(
        "api/orders/<int:purchase_order_id>/action",
        PurchaseOrderViewset.as_view({"post": "action"}),
        name="order-action",
    ),
    path(
        "api/orders/<int:purchase_order_id>/shipments",
        ShipmentViewSet.as_view({"post": "create_po_shipments"}),
        name="order-shipments",
    ),
    path(
        "api/shipments/<int:pk>",
        ShipmentViewSet.as_view({"patch": "patch_shipment"}),
        name="update-shipment",
    ),
    path(
        "api/orders/count",
        PurchaseOrderCountView.as_view(),
        name="purchase-order-count",
    ),
    path(
        "api/orders/approval-assignees",
        ApprovalAssigneesView.as_view(),
        name="approval-assignees",
    ),
    path(
        "api/orders/<int:purchase_order_id>/comments",
        PurchaseOrderCommentViewSet.as_view({"get": "list", "post": "create"}),
        name="purchase-order-comment-list",
    ),
    path(
        "api/orders/<int:purchase_order_id>/comments/<int:pk>",
        PurchaseOrderCommentViewSet.as_view(
            {
                "get": "retrieve",
                "delete": "destroy",
                "put": "update",
                "patch": "partial_update",
            }
        ),
        name="purchase-order-comment-list",
    ),
    path(
        "api/orders/<int:purchase_order_id>/docs",
        DocumentsViewset.as_view({"get": "list", "post": "create"}),
        name="oder-documents",
    ),
    path(
        "api/orders/<int:purchase_order_id>/docs/<str:uuid>",
        DocumentsViewset.as_view(
            {
                "get": "retrieve",
                "put": "update",
                "patch": "partial_update",
                "delete": "destroy",
            }
        ),
        name="supplier-doc-detail",
    ),
]
