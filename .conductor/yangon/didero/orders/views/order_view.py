import structlog
from django.contrib.admin.options import get_content_type_for_model
from django.db import IntegrityError
from django.db.models import Case, IntegerField, Q, When
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.serializers import ValidationError

from didero.common.utils import update_object_with_tags
from didero.documents.models import Document
from didero.emails.models import EmailThreadToPurchaseOrderLink
from didero.filters import NullsLastOrderingFilter
from didero.orders.models import POEditReason, PurchaseOrder
from didero.orders.schemas import (
    ORDER_STATUSES_TERMINAL,
    STATUSES_EDITABLE,
    PurchaseOrderStatus,
    SourceChoices,
)
from didero.orders.serializers import (
    PurchaseOrderSerializer,
)
from didero.orders.utils.order_pdf_util import po_to_pdf
from didero.orders.utils.order_util import OrderHelper
from didero.orders.utils.order_view_util import (
    PurchaseOrderFilter,
    get_total_cost_currency_from_request_data,
    get_total_cost_from_request_data,
)
from didero.suppliers.models import Communication, Supplier, SupplierContact
from didero.views import APIView, PagesPagination, TeamOwnerFilteredModelView

logger = structlog.get_logger()


class PurchaseOrderViewset(APIView, TeamOwnerFilteredModelView[PurchaseOrder]):
    """
    Handles the creation and viewing of Purchase orders, including PDF generation of the invoice.
    """

    serializer_class = PurchaseOrderSerializer
    pagination_class = PagesPagination
    filter_backends = [DjangoFilterBackend, NullsLastOrderingFilter]
    filterset_class = PurchaseOrderFilter
    # The fields we can sort by:
    ordering_fields = [
        "order_status_int",
        "placement_time",
        "po_number",
        "supplier",
        "total_cost",
    ]
    model = PurchaseOrder

    # default ordering is by order_status_int (the annotated field from the get_queryset method)
    ordering = ["order_status_int"]

    def get_permissions(self):
        ### TODO: uncomment this when we roll out permissions
        # if self.action == "create":
        #     permission_classes = [CanRequestPurchaseOrder]
        # else:
        #     permission_classes = [IsAuthenticated]
        permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def handle_po_create_update_exception(self, e: IntegrityError):
        if "po_number_team" in str(e):
            return self.error_response(
                [
                    {
                        "code": "invalid",
                        "detail": "Duplicate PO number for the given team is not allowed.",
                        "attr": "poNumber",
                    },
                ],
                status=status.HTTP_409_CONFLICT,
            )
        raise e

    def get_queryset(self):
        queryset = super().get_queryset()
        # Prefetch tags to optimize queries
        queryset = queryset.prefetch_related("tags")
        queryset = queryset.annotate(
            # annotate with new field that allows for custom ordering
            # FE will query by this field when trying to sort by order status
            order_status_int=Case(
                When(order_status=PurchaseOrderStatus.DRAFT, then=1),
                When(order_status=PurchaseOrderStatus.APPROVAL_PENDING, then=2),
                When(order_status=PurchaseOrderStatus.APPROVAL_DENIED, then=3),
                When(order_status=PurchaseOrderStatus.APPROVED, then=4),
                When(order_status=PurchaseOrderStatus.ISSUED, then=5),
                When(order_status=PurchaseOrderStatus.PENDING_ACCEPTANCE, then=6),
                When(order_status=PurchaseOrderStatus.AWAITING_SHIPMENT, then=7),
                When(order_status=PurchaseOrderStatus.READY_FOR_PICKUP, then=8),
                When(order_status=PurchaseOrderStatus.SUPPLIER_REJECTED, then=9),
                When(order_status=PurchaseOrderStatus.SHIPMENT_DELAYED, then=10),
                When(order_status=PurchaseOrderStatus.PARTIALLY_SHIPPED, then=11),
                When(order_status=PurchaseOrderStatus.SHIPPED, then=12),
                When(order_status=PurchaseOrderStatus.DELIVERY_DELAYED, then=13),
                When(order_status=PurchaseOrderStatus.RECEIVED, then=14),
                When(order_status=PurchaseOrderStatus.PARTIALLY_RECEIVED, then=15),
                When(order_status=PurchaseOrderStatus.CANCELED, then=16),
                When(order_status=PurchaseOrderStatus.INVOICE_MATCHED, then=17),
                When(order_status=PurchaseOrderStatus.ISSUE, then=18),
                output_field=IntegerField(),
            )
        )
        # do not show ones whose supplier is archived
        queryset = queryset.exclude(Q(supplier__archived_at__isnull=False))

        # Filter out non-editable POs for non-AI users
        from didero.utils.utils import get_didero_ai_user

        if self.request.user and self.request.team:
            didero_ai_user = get_didero_ai_user(self.request.team)
            # If the current user is not the AI user, exclude non-editable POs
            if didero_ai_user and self.request.user.id != didero_ai_user.id:
                queryset = queryset.exclude(is_po_editable=False)

        # Apply filters
        return self.filterset_class.get_filtered_queryset(
            self.request, queryset
        ).distinct()

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, pk=None):
        """
        Get a specific PO.
        """
        logger.info(
            f"Retrieving purchase order {pk} for team {request.team.pk}",
            user_id=request.user.id,
        )
        order = self.get_object(pk)
        # check if the user is authorized to view this order
        has_access, denied_reason = OrderHelper.has_access_to_po(
            order, request.user.pk, request.team
        )
        if has_access:
            serializer = self.serializer_class(order)
            return Response(serializer.data)

        logger.error(
            f"User {request.user.pk} attempted to access purchase order {pk} for team {request.team.pk} but does not have access",
            user_id=request.user.id,
            po_id=pk,
            team_id=request.team.id,
            denied_reason=denied_reason,
        )
        raise NotFound("Purchase order not found")

    def get_reference_count(self, request, pk=None):
        """
        Get the count of Comms, Docs, Contacts, and Comments for the Purchase Order.
        """
        order = self.get_object(pk)
        count = {}

        has_access, denied_reason = OrderHelper.has_access_to_po(
            order, request.user.pk, request.team
        )
        if has_access:
            # Add docs count
            docsCount = Document.objects.filter(
                links__parent_object_type=get_content_type_for_model(PurchaseOrder),
                links__parent_object_id=order.id,
            ).count()
            count["docs_count"] = docsCount

            # Add contacts count
            supplierCount = SupplierContact.objects.filter(
                supplier=order.supplier,
                supplier__archived_at__isnull=True,
            ).count()
            count["contacts_count"] = supplierCount

            # Add email threads count
            thread_ids = EmailThreadToPurchaseOrderLink.objects.filter(
                purchase_order_id=order.id
            ).values_list("email_thread", flat=True)
            commsCount = Communication.objects.filter(pk__in=thread_ids).count()
            count["email_count"] = commsCount

            # Add comments count
            commentsCount = order.comments.count()
            count["comments_count"] = commentsCount

            return Response(count)

        logger.error(
            f"User {request.user.pk} attempted to get reference count for purchase order {pk} for team {request.team.pk} but does not have access",
            user_id=request.user.id,
            po_id=pk,
            team_id=request.team.id,
            denied_reason=denied_reason,
        )
        raise NotFound("Purchase order not found")

    def get_object(self, pk) -> PurchaseOrder:
        # First, check if the order exists at all (regardless of team)
        raw_order = PurchaseOrder.objects.filter(pk=pk).first()

        if raw_order:
            team_id = self.request.team.id if self.request.team else None
            supplier_archived = (
                raw_order.supplier.archived_at is not None
                if raw_order.supplier
                else False
            )
            user_email = getattr(self.request.user, "email", "no-email")

            # Determine why the order might be filtered out
            filtered_reasons = []
            if raw_order.team_id != team_id and not (
                self.request.user.is_staff or self.request.user.is_superuser
            ):
                filtered_reasons.append(
                    f"team mismatch (order team: {raw_order.team_id}, user team: {team_id})"
                )
            if supplier_archived:
                filtered_reasons.append("supplier is archived")

            log_level = "error" if filtered_reasons else "info"
            log_message = (
                f"get_object: Order {pk} exists with team {raw_order.team_id}, "
                f"user team: {team_id}, user: {self.request.user.id} ({user_email}), "
                f"supplier archived: {supplier_archived}"
            )
            if filtered_reasons:
                log_message += (
                    f", will be filtered out due to: {', '.join(filtered_reasons)}"
                )

            getattr(logger, log_level)(
                log_message,
                user_id=self.request.user.id,
                user_email=user_email,
                po_id=pk,
                order_team_id=raw_order.team_id,
                user_team_id=team_id,
                supplier_archived=supplier_archived,
                filtered_reasons=filtered_reasons,
            )
        else:
            logger.error(
                f"get_object: Order {pk} does not exist in database",
                user_id=self.request.user.id,
                po_id=pk,
            )

        # Build a queryset that includes archived orders for detail view
        # Use the base queryset and apply the same filtering as the list view,
        # but explicitly include archived orders regardless of query params
        queryset = super().get_queryset()
        # Prefetch tags to optimize queries
        queryset = queryset.prefetch_related("tags")
        # do not show ones whose supplier is archived
        queryset = queryset.exclude(Q(supplier__archived_at__isnull=False))

        # Now try to get it from the queryset (including archived orders)
        return get_object_or_404(queryset, pk=pk)

    def create(self, request, *args, **kwargs):
        """
        Create a PurchaseOrder with optional fields.
        This endpoint is hit when a user is creating a new PO from the website.
        The source is always Didero unless specified otherwise in the request.
        """
        request.data.setdefault("source", SourceChoices.DIDERO.value)
        request.data.setdefault("team", request.team.pk)
        request.data.setdefault("placed_by_id", request.user.id)
        request.data.setdefault(
            "total_cost", get_total_cost_from_request_data(request.data)
        )
        request.data.setdefault(
            "total_cost_currency",
            get_total_cost_currency_from_request_data(request.data),
        )
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # TODO: https://linear.app/d4o/issue/EPD-2021/check-if-supplier-is-active-before-creating-purchase-orders
        # This was implemented in the SOR MVP but turned off as it was disruptive to users and demos
        # Eventually, this will be turned back on for v2 of the Supplier Onboarding experienc.
        try:
            _ = serializer.save()
        except IntegrityError as e:
            return self.handle_po_create_update_exception(e)

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, pk=None):
        """
        Update a PO. Only some fields are allowed to be edited - depending on the
        status of the PO itself.
        """
        # Fetch the existing PO; raise 404 if not found
        purchase_order = self.get_object(pk)
        has_access, denied_reason = OrderHelper.has_access_to_po(
            purchase_order, request.user.pk, request.team
        )
        if not has_access:
            logger.error(
                f"User {request.user.pk} attempted to update purchase order {pk} for team {request.team.pk} but does not have access",
                user_id=request.user.id,
                po_id=pk,
                team_id=request.team.id,
                denied_reason=denied_reason,
            )
            raise NotFound("Purchase order not found")

        # Handle making a PO editable by providing a reason
        if (
            "is_po_editable_reason" in request.data
            and request.data["is_po_editable_reason"]
        ):
            # Create a record of the edit reason
            POEditReason.objects.create(
                purchase_order=purchase_order,
                reason=request.data["is_po_editable_reason"],
                requested_by=request.user,
            )
            # Make the PO editable
            purchase_order.is_po_editable = True
            purchase_order.save(update_fields=["is_po_editable", "modified_at"])
            logger.info(
                "PO made editable via override",
                po_id=purchase_order.id,
                user_id=request.user.id,
                reason=request.data["is_po_editable_reason"],
            )
            return Response(
                PurchaseOrderSerializer(purchase_order).data,
                status=status.HTTP_200_OK,
            )

        # Check if PO is editable based on both status and is_po_editable flag
        if (
            purchase_order.order_status not in STATUSES_EDITABLE
            or not purchase_order.is_po_editable
        ):
            edited = False
            # Only some fields are editable no matter the status
            if "tags" in request.data:
                tags = request.data["tags"]
                tag_update_success = update_object_with_tags(purchase_order, tags)
                if not tag_update_success:
                    return self.single_error_response(
                        detail="Tag does not exist for this team.",
                        attr="tags",
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                del request.data["tags"]
                edited = True
            # You can unarchive an order no matter the status (although the status should be CANCELED).
            # If you want to archive an order, the request should go through the DELETE endpoint / destroy() below.
            if "archived_at" in request.data and request.data["archived_at"] is None:
                purchase_order.archived_at = request.data["archived_at"]
                purchase_order.save(update_fields=["archived_at", "modified_at"])
                edited = True

            # The handling of is_po_editable_reason is now done at the top of the method (lines 191-211)

            # Return appropriate response based on whether any fields were edited
            if edited:
                # Refresh the instance to get the updated tags
                purchase_order.refresh_from_db()
                return Response(PurchaseOrderSerializer(purchase_order).data)
            else:
                return Response(
                    {
                        "error": f"PO is not editable. Status: {purchase_order.order_status}, is_po_editable: {purchase_order.is_po_editable}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if "order_status" in request.data:
            return Response(
                {"error": "Order status cannot be updated directly"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(
            instance=purchase_order, data=request.data, partial=True
        )
        # Validate the provided data. We should continue adding validation for other fields
        try:
            serializer.is_valid(raise_exception=True)
            if "supplier_id" in request.data:
                supplier_id = request.data["supplier_id"]
                if not Supplier.objects.filter(
                    pk=supplier_id, team=purchase_order.team
                ).exists():
                    return Response(
                        {"error": "Supplier does not belong to this team."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

        except ValidationError as e:
            return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)

        try:
            serializer.save()
        except IntegrityError as e:
            return self.handle_po_create_update_exception(e)
        # Serialize and return the updated PO
        return Response(serializer.data)

    # DELETE requests should:
    # 1. delete the PO if it's a draft
    # 2. archive the PO otherwise
    def destroy(self, request, pk=None):
        purchase_order = self.get_object(pk)
        if purchase_order.order_status == PurchaseOrderStatus.DRAFT:
            purchase_order.delete()
            return Response(
                {"message": f"Purchase order {pk} deleted successfully"},
                status=status.HTTP_200_OK,
            )
        elif not purchase_order.archived_at:
            # We change the status to CANCELED if the current status is not a terminal state
            if purchase_order.order_status not in ORDER_STATUSES_TERMINAL:
                updated_status = PurchaseOrderStatus.CANCELED
            else:
                updated_status = purchase_order.order_status
            purchase_order.archived_at = timezone.now()
            purchase_order.order_status = updated_status
            purchase_order.save(
                update_fields=["archived_at", "order_status", "modified_at"]
            )
            return Response(
                {"message": f"Purchase order {pk} was archived successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "message": f"Purchase order {pk} was previously archived at {purchase_order.archived_at}"
                },
                status=status.HTTP_200_OK,
            )

    def generate_pdf(self, request):
        data = request.data
        data["team"] = request.team.id
        pdfResult, error = po_to_pdf(data)

        # TODO: Save the PDF result to S3 and return a public link instead. Right now this is returning the raw PDF response
        if error:
            return HttpResponse(
                "Unable to parse PDF", status_code=400, content_type="text/plain"
            )
        return HttpResponse(pdfResult.getvalue(), content_type="application/pdf")

    def action(self, request, purchase_order_id=None):
        action = request.data.pop("action")
        params = request.data.pop("params", {})
        po = PurchaseOrder.objects.filter(pk=purchase_order_id).get()
        po.run_action(action, **{**params, "user_actor": request.user})

        return Response(self.get_serializer(po).data)


class PurchaseOrderCountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        team = self.request.team
        approver_id = request.query_params.get("approver_id")

        # exclude archived POs and POs from suppliers that are archived
        orders = PurchaseOrder.objects.filter(
            team=team, archived_at__isnull=True, supplier__archived_at__isnull=True
        )

        # Filter out non-editable POs for non-AI users
        from didero.utils.utils import get_didero_ai_user

        if request.user and team:
            didero_ai_user = get_didero_ai_user(team)
            # If the current user is not the AI user, exclude non-editable POs
            if didero_ai_user and request.user.id != didero_ai_user.id:
                orders = orders.exclude(is_po_editable=False)

        # If an approverId is provided, filter by approver
        if approver_id:
            orders = orders.filter(approvals__approver__id=approver_id)

        return Response(
            {
                "count": PurchaseOrderFilter.get_filtered_queryset(
                    self.request, orders
                ).count()
            }
        )


class ApprovalAssigneesView(APIView):
    """
    Returns available users and groups that can be assigned as approvers for purchase orders.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        from didero.users.models.user_models import DideroUserGroup, User
        from didero.users.serializers import UserGroupSerializer, UserSerializer

        team = self.request.team

        # Get users with approve purchase order permission
        users = User.objects.filter(
            team_memberships__team=team,
            team_memberships__role__permissions__contains=["APPROVE_PURCHASE_ORDER"],
        ).distinct()

        # Get all user groups for the team
        groups = DideroUserGroup.objects.filter(team=team)

        return Response(
            {
                "users": UserSerializer(users, many=True).data,
                "groups": UserGroupSerializer(groups, many=True).data,
            }
        )
