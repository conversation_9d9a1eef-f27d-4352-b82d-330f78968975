from functools import wraps

from django.http import Http404
from django.shortcuts import get_object_or_404
from rest_framework import status, viewsets
from rest_framework.exceptions import PermissionDenied
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.orders.models import OrderItem, PurchaseOrder
from didero.orders.serializers import OrderItemSerializer
from didero.orders.utils.order_util import OrderHelper
from didero.views import APIView


def check_purchase_order_access(view_func):
    """
    This decorator:
    1. Checks if the PO exists, and if the user has access to the order
    2. Adds purchase_order as an arg to be used in the individual
    methods.
    We currently only added it to the OrderItem viewset, but eventually it should be added to the
    parent PurchaseOrder viewset.
    """

    @wraps(view_func)
    def wrapper(self, request, *args, **kwargs):
        purchase_order_id = self.kwargs.get("po_pk") or kwargs.get("po_pk")
        purchase_order = get_object_or_404(
            PurchaseOrder, pk=purchase_order_id, team=request.team
        )
        if not OrderHelper.has_access_to_po(
            purchase_order, request.user.pk, request.team
        ):
            raise PermissionDenied("Access denied.")

        kwargs["purchase_order"] = purchase_order
        return view_func(self, request, *args, **kwargs)

    return wrapper


class OrderItemViewSet(APIView, viewsets.ModelViewSet):
    """
    ViewSet for creating, retrieving, updating, and deleting OrderItems.
    """

    permission_classes = [IsAuthenticated]
    serializer_class = OrderItemSerializer
    # URL will use order_item_pk, and we map that to id, which is the pk of the order item.
    lookup_field = "id"
    lookup_url_kwarg = "order_item_pk"

    def get_purchase_order(self, request, pk) -> PurchaseOrder:
        purchase_order = get_object_or_404(PurchaseOrder, pk=pk, team=self.request.team)
        # If user doesn't have access, has_access_to_po() will throw an error on its own.
        assert OrderHelper.has_access_to_po(
            purchase_order, request.user.pk, request.team
        )
        return purchase_order

    def get_queryset(self):
        """
        Return a queryset of OrderItems filtered by the PurchaseOrder primary key (po_pk).
        """
        purchase_order_id = self.kwargs.get("po_pk")
        purchase_order = get_object_or_404(
            PurchaseOrder, pk=purchase_order_id, team=self.request.team
        )

        if not OrderHelper.has_access_to_po(
            purchase_order, self.request.user.pk, self.request.team
        ):
            raise PermissionDenied("Access denied.")

        return OrderItem.objects.filter(purchase_order=purchase_order)

    @check_purchase_order_access
    def create(self, request, *args, **kwargs):
        """
        Create an OrderItem for a specific PurchaseOrder. The order must include an item_id, and can also include
        quantity and price.
        """
        purchase_order = kwargs.get("purchase_order")
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer, purchase_order)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def perform_create(self, serializer, purchase_order):
        """
        Create an OrderItem, linking it to a PurchaseOrder.
        """
        serializer.save(purchase_order=purchase_order)

    @check_purchase_order_access
    def update(self, request, *args, **kwargs):
        """
        Update an existing OrderItem with partial data.
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.serializer_class(instance, data=request.data, partial=partial)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @check_purchase_order_access
    def destroy(self, request, *args, **kwargs):
        """
        Delete an OrderItem.
        """
        order_item_pk = self.kwargs.get("order_item_pk")
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response(
                {"message": f"OrderItem with id {order_item_pk} deleted successfully"},
                status=status.HTTP_200_OK,
            )
        except Http404:
            return Response(
                {"error": "Resource not found or access denied."},
                status=status.HTTP_404_NOT_FOUND,
            )

    @check_purchase_order_access
    def bulk_delete(self, request, *args, **kwargs):
        purchase_order = kwargs.get("purchase_order")
        order_item_pks = request.data.get("order_item_pks", [])

        if not order_item_pks:
            return Response(
                {"error": "No order item IDs provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        order_items = OrderItem.objects.filter(
            id__in=order_item_pks, purchase_order=purchase_order
        )
        found_ids = list(order_items.values_list("id", flat=True))
        not_found_ids = [pk for pk in order_item_pks if pk not in found_ids]

        if not_found_ids:
            return Response(
                {"error": "Some items could not be found or access denied."},
                status=status.HTTP_404_NOT_FOUND,
            )

        order_items.delete()
        return Response(
            {
                "message": "Order items deleted successfully.",
                "deleted_order_item_ids": found_ids,
            },
            status=status.HTTP_200_OK,
        )
