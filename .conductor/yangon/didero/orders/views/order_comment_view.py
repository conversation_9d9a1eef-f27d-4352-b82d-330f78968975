from rest_framework.exceptions import NotFound

from didero.orders.models import PurchaseOrder
from didero.orders.serializers import PurchaseOrderCommentSerializer
from didero.views import BaseCommentViewset


class PurchaseOrderCommentViewSet(BaseCommentViewset):
    serializer_class = PurchaseOrderCommentSerializer

    def get_parent(self):
        order_id = self.kwargs.get("purchase_order_id")
        order = PurchaseOrder.objects.filter(
            pk=order_id, team=self.request.team
        ).first()
        if not order:
            raise NotFound("Order not found, or does not belong to this team")
        return order

    def get_parent_model_field(self):
        return "purchase_order"
