from datetime import date
from typing import Any, Dict, List, Optional

from django.contrib.contenttypes.models import ContentType
from django_currentuser.middleware import get_current_user
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from didero.orders.models import PurchaseOrder, Shipment
from didero.orders.serializers import PurchaseOrderSerializer, ShipmentSerializer
from didero.tasks.utils import (
    create_ship_date_changed_task,
)
from didero.views import APIView


def format_date(date_obj: date) -> str:
    return date_obj.strftime("%Y-%m-%d")


def create_shipment_update_task(purchase_order: PurchaseOrder) -> None:
    actor = get_current_user()
    # If the requestor is the one who updated the shipment, they don't need to get notified
    if not purchase_order.placed_by or actor == purchase_order.placed_by:
        return

    is_single_shipment_order = purchase_order.shipments.count() == 1
    ship_date_string = ""

    if is_single_shipment_order:
        shipment = purchase_order.shipments.first()
        # exclude ones that don't have an ETA set yet (e.g. if only have a tracking number)
        if not shipment or not shipment.estimated_delivery_date:
            return
        ship_date_string = f"{purchase_order.po_number} is scheduled to ship on {format_date(shipment.estimated_delivery_date)}."
    else:
        shipments = purchase_order.shipments.exclude(
            estimated_delivery_date__isnull=True
        )
        first_shipment = shipments.order_by("estimated_delivery_date").first()
        last_shipment = shipments.order_by("-estimated_delivery_date").first()

        if not first_shipment or not last_shipment:
            return

        earliest = first_shipment.estimated_delivery_date
        latest = last_shipment.estimated_delivery_date

        if not earliest or not latest:
            return

        if earliest == latest:
            ship_date_string = f"{purchase_order.po_number} is scheduled to ship on {format_date(earliest)}."
        else:
            ship_date_string = f"{purchase_order.po_number} has shipments scheduled from {format_date(earliest)} to {format_date(latest)}."

    if ship_date_string:
        create_ship_date_changed_task(
            user=purchase_order.placed_by,
            model_type=ContentType.objects.get_for_model(PurchaseOrder),
            model_id=purchase_order.pk,
            ship_date_string=ship_date_string,
        )


class ShipmentViewSet(APIView, viewsets.GenericViewSet):
    """
    ViewSet for managing shipments. Provides endpoints for managing shipments.
    """

    permission_classes = [IsAuthenticated]
    serializer_class = ShipmentSerializer

    def get_queryset(self):
        """Return only shipments that belong to the user's team"""
        from django.db.models import Q

        return Shipment.objects.filter(
            Q(purchase_order__team=self.request.team) | Q(team=self.request.team)
        )

    def get_purchase_order(self, purchase_order_id: int) -> Optional[PurchaseOrder]:
        """Get purchase order and ensure it belongs to the user's team"""
        try:
            # The team attribute is added to the request by middleware
            return self.request.team.purchase_orders.get(pk=purchase_order_id)  # type: ignore
        except PurchaseOrder.DoesNotExist:
            return None

    def create_po_shipments(
        self, request: Request, purchase_order_id: Optional[int] = None
    ) -> Response:
        """
        Create or replace shipments for a purchase order.

        POST: Takes a list of shipments for the purchase order.
        - Deletes all existing shipments
        - Creates new shipments from the provided data
        - Updates the PO status
        """
        order = (
            self.get_purchase_order(purchase_order_id) if purchase_order_id else None
        )
        if not order:
            return Response({"error": "Purchase order not found"}, status=404)

        # Validate all shipment data first
        validated_shipments_data: List[Dict[str, Any]] = []
        for shipment_data in request.data:  # type: ignore
            # Create a mutable copy of the data - DRF request.data is a QueryDict or similar type
            data_copy = dict(shipment_data)  # type: ignore
            data_copy["purchase_order_id"] = order.pk  # type: ignore
            slzr = ShipmentSerializer(data=data_copy)
            slzr.is_valid(raise_exception=True)
            validated_shipments_data.append(slzr.validated_data)

        # Use the shipment manager to bulk create shipments
        # This will delete existing shipments, create new ones, and update the PO status
        # The custom method is defined on the manager
        Shipment.objects.bulk_create_shipments(order, validated_shipments_data)  # type: ignore

        # Create task about shipment update (if needed)
        create_shipment_update_task(order)

        return Response(PurchaseOrderSerializer(order).data)

    def patch_shipment(self, request: Request, pk: Optional[int] = None) -> Response:
        """
        Update a single shipment.

        PATCH: Updates specific fields of an existing shipment
        - Updates the specified shipment
        - Updates the PO status
        """
        if not pk:
            return Response({"error": "Shipment ID is required"}, status=400)

        try:
            # Use the filtered queryset to ensure team ownership
            shipment = self.get_queryset().get(pk=pk)
        except Shipment.DoesNotExist:
            return Response({"error": "Shipment not found"}, status=404)

        # Validate shipment data
        slzr = ShipmentSerializer(shipment, data=request.data, partial=True)  # type: ignore
        slzr.is_valid(raise_exception=True)

        # Update the shipment using our manager
        # The custom method is defined on the manager
        updated_shipment = Shipment.objects.update_shipment(  # type: ignore
            shipment_id=pk, **slzr.validated_data
        )

        # Create task about shipment update (if needed)
        create_shipment_update_task(updated_shipment.purchase_order)

        return Response(ShipmentSerializer(updated_shipment).data)
