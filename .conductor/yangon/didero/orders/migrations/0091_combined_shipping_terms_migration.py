# Generated by Django 4.2.7 on 2025-06-27

from django.db import migrations, models


def migrate_to_requested_date(apps, schema_editor):
    """
    Forward migration: Copy data from old fields to new field.
    Since no orders in production use requested_ship_date, we only need to copy requested_delivery_date.
    """
    PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    OrderItem = apps.get_model("orders", "OrderItem")

    # Bulk update PurchaseOrders - copy requested_delivery_date to requested_date
    PurchaseOrder.objects.filter(requested_delivery_date__isnull=False).update(
        requested_date=models.F("requested_delivery_date")
    )

    # Bulk update OrderItems - copy requested_delivery_date to requested_date
    OrderItem.objects.filter(requested_delivery_date__isnull=False).update(
        requested_date=models.F("requested_delivery_date")
    )


def reverse_migrate_to_requested_date(apps, schema_editor):
    """
    Reverse migration: Copy data from new field back to old fields.
    """
    PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    OrderItem = apps.get_model("orders", "OrderItem")

    # Copy requested_date back to requested_delivery_date
    PurchaseOrder.objects.filter(requested_date__isnull=False).update(
        requested_delivery_date=models.F("requested_date")
    )

    OrderItem.objects.filter(requested_date__isnull=False).update(
        requested_delivery_date=models.F("requested_date")
    )


class Migration(migrations.Migration):
    atomic = False  # Allow partial rollback if needed

    dependencies = [
        ("orders", "0090_alter_purchaseorder_order_status"),
    ]

    operations = [
        # Step 1: Add new fields
        migrations.AddField(
            model_name="purchaseorder",
            name="requested_date",
            field=models.DateField(
                blank=True,
                null=True,
                help_text="Interpretation depends on shipping_terms: null='general requested date', EXW='ready for pickup', DAP='delivery date', etc.",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="shipping_terms",
            field=models.CharField(
                max_length=10,
                choices=[
                    ("EXW", "Ex Works - Buyer picks up"),
                    ("FCA", "Free Carrier - Deliver to carrier"),
                    ("FOB", "Free on Board - Load on vessel"),
                    ("DAP", "Delivered at Place - Seller delivers"),
                    ("DDP", "Delivered Duty Paid - Full service"),
                    ("CPT", "Carriage Paid To"),
                    ("CIP", "Carriage and Insurance Paid To"),
                    ("DPU", "Delivered at Place Unloaded"),
                    ("FAS", "Free Alongside Ship"),
                    ("CFR", "Cost and Freight"),
                    ("CIF", "Cost Insurance and Freight"),
                ],
                blank=True,
                null=True,
                help_text="Defines shipping responsibility and date interpretation",
            ),
        ),
        migrations.AddField(
            model_name="orderitem",
            name="requested_date",
            field=models.DateField(
                blank=True,
                null=True,
                help_text="Interpretation inherits from PurchaseOrder's shipping_terms",
            ),
        ),
        # Step 2: Migrate data from old fields to new fields
        migrations.RunPython(
            migrate_to_requested_date,
            reverse_migrate_to_requested_date,
        ),
        # Step 3: Remove old fields
        migrations.RemoveField(
            model_name="purchaseorder",
            name="requested_delivery_date",
        ),
        migrations.RemoveField(
            model_name="purchaseorder",
            name="requested_ship_date",
        ),
        migrations.RemoveField(
            model_name="orderitem",
            name="requested_delivery_date",
        ),
        migrations.RemoveField(
            model_name="orderitem",
            name="requested_ship_date",
        ),
    ]
