# Generated by Django 4.2.7 on 2024-12-11 20:20

from django.db import migrations, models

from didero.orders.models import OrderItem, PurchaseOrder


# Goal - On OrderItem set requested_delivery_date, requested_ship_date should be None for now
def backfill_requested_delivery_date_on_order_item(apps, schema_editor):
    OrderItemModel: OrderItem = apps.get_model("orders", "OrderItem")
    for instance in OrderItemModel.objects.all():
        instance.requested_delivery_date = instance.requested_ship_date
        instance.requested_ship_date = None
        instance.save()


def reverse_backfill_requested_delivery_date_on_order_item(apps, schema_editor):
    OrderItemModel: OrderItem = apps.get_model("orders", "OrderItem")
    for instance in OrderItemModel.objects.all():
        instance.requested_ship_date = instance.requested_delivery_date  # safer in the reverse to just use the requested_delivery_date rather than referring to PO as PO changes in this migration as well.
        instance.requested_delivery_date = None
        instance.save()


# Goal - On PurchaseOrder set requested_delivery_date to requested_ship_date, requested_ship_date should be None for now
def migrate_po_requested_ship_date_to_delivery_date(apps, schema_editor):
    PurchaseOrderModel: PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    for instance in PurchaseOrderModel.objects.all():
        instance.requested_delivery_date = instance.requested_ship_date
        instance.requested_ship_date = None
        instance.save()


def reverse_migrate_po_requested_ship_date_to_delivery_date(apps, schema_editor):
    PurchaseOrderModel: PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    for instance in PurchaseOrderModel.objects.all():
        instance.requested_ship_date = instance.requested_delivery_date
        instance.requested_delivery_date = None
        instance.save()


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0064_auto_20241205_2056"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderitem",
            name="requested_delivery_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="requested_delivery_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.RunPython(
            backfill_requested_delivery_date_on_order_item,
            reverse_code=reverse_backfill_requested_delivery_date_on_order_item,
        ),
        migrations.RunPython(
            migrate_po_requested_ship_date_to_delivery_date,
            reverse_code=reverse_migrate_po_requested_ship_date_to_delivery_date,
        ),
    ]
