# Generated by Django 4.2.7 on 2025-06-25 02:54

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0090_alter_purchaseorder_order_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="shipment",
            name="erp_sync_attempted_at",
            field=models.DateTimeField(
                blank=True, help_text="Last ERP sync attempt timestamp", null=True
            ),
        ),
        migrations.AddField(
            model_name="shipment",
            name="erp_sync_completed_at",
            field=models.DateTimeField(
                blank=True, help_text="Successful ERP sync timestamp", null=True
            ),
        ),
        migrations.AddField(
            model_name="shipment",
            name="erp_sync_error",
            field=models.TextField(blank=True, help_text="Last ERP sync error message"),
        ),
        migrations.AddField(
            model_name="shipment",
            name="erp_sync_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("in_progress", "In Progress"),
                    ("success", "Success"),
                    ("failed", "Failed"),
                    ("not_configured", "Not Configured"),
                ],
                default="not_configured",
                help_text="Status of ERP synchronization",
                max_length=20,
            ),
        ),
    ]
