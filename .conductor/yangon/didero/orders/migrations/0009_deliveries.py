# Generated by Django 4.2.7 on 2024-05-02 14:36

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    replaces = [
        ("orders", "0009_purchaseorder_created_at_purchaseorder_modified_at_and_more"),
        ("orders", "0010_alter_delivery_options"),
    ]

    dependencies = [
        ("orders", "0008_added_purchaseorder_number_index"),
        ("suppliers", "0031_communication_email_from_communication_email_to_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="purchaseorder",
            name="created_at",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="modified_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="delivery_status",
            field=models.CharField(
                choices=[
                    ("undelivered", "UNDELIVERED"),
                    ("partially_delivered", "PARTIALLY_DELIVERED"),
                    ("delivered", "DELIVERED"),
                    ("issue", "ISSUE"),
                ],
                default="undelivered",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="Delivery",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("received_date", models.DateField(default=datetime.date.today)),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deliveries",
                        to="orders.purchaseorder",
                    ),
                ),
                (
                    "receipt_document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="delivery_receipts",
                        to="suppliers.document",
                    ),
                ),
                (
                    "received_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "verbose_name_plural": "Deliveries",
            },
        ),
    ]
