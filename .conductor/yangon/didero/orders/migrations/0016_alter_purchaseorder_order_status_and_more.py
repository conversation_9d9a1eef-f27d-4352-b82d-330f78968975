# Generated by Django 4.2.7 on 2024-05-10 15:59

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0015_orderapproval_approver_number_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorder",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("confirmed", "SUPPLIER_CONFIRMED"),
                    ("rejected", "SUPPLIER_REJECTED"),
                    ("in_production", "IN_PRODUCTION"),
                    ("canceled", "CANCELED"),
                    ("shipped", "SHIPPED"),
                    ("shipping_problem", "SHIPPING_PROBLEM"),
                    ("partially_received", "PARTIALLY_RECEIVED"),
                    ("received", "RECEIVED"),
                    ("completed", "COMPLETED"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="orderapproval",
            unique_together={("purchase_order", "approver_number")},
        ),
        migrations.AddIndex(
            model_name="orderapproval",
            index=models.Index(
                fields=["purchase_order", "approver_number"],
                name="orders_orde_purchas_4ff088_idx",
            ),
        ),
    ]
