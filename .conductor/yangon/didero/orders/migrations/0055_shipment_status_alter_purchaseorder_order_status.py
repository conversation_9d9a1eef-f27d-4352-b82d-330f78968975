# Generated by Django 4.2.7 on 2024-10-07 16:24

from django.db import migrations, models


def supplier_accepted_to_awaiting_shipment(apps, schema_editor):
    PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    for po in PurchaseOrder.objects.all():
        if po.order_status == "supplier_accepted":
            po.order_status = "awaiting_shipment"
            po.save()


def awaiting_shipment_to_supplier_accepted(apps, schema_editor):
    PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    for po in PurchaseOrder.objects.all():
        if po.order_status == "awaiting_shipment":
            po.order_status = "supplier_accepted"
            po.save()


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0054_remove_shipment_team_alter_purchaseorder_team"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorder",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("pending_acceptance", "PENDING_ACCEPTANCE"),
                    ("supplier_accepted", "SUPPLIER_ACCEPTED"),
                    ("awaiting_shipment", "AWAITING_SHIPMENT"),
                    ("supplier_rejected", "SUPPLIER_REJECTED"),
                    ("shipment_delayed", "SHIPMENT_DELAYED"),
                    ("shipped", "SHIPPED"),
                    ("partially_shipped", "PARTIALLY_SHIPPED"),
                    ("delivery_delayed", "DELIVERY_DELAYED"),
                    ("received", "RECEIVED"),
                    ("partially_received", "PARTIALLY_RECEIVED"),
                    ("canceled", "CANCELED"),
                    ("invoice_matched", "INVOICE_MATCHED"),
                    ("issue", "ISSUE"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
        migrations.RunPython(
            supplier_accepted_to_awaiting_shipment,
            reverse_code=awaiting_shipment_to_supplier_accepted,
        ),
    ]
