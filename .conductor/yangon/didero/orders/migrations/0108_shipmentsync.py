# Generated by Django 4.2.7 on 2025-07-30 21:26

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0107_orderacknowledgement_global_promised_delivery_date_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShipmentSync",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("in_progress", "IN_PROGRESS"),
                            ("success", "SUCCESS"),
                            ("failed", "FAILED"),
                        ],
                        default="in_progress",
                        help_text="Current status of the sync operation",
                        max_length=20,
                    ),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="When the sync operation started",
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the sync operation completed (success or failure)",
                        null=True,
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="Error details if sync failed", null=True
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional data from the sync operation (API responses, etc.)",
                    ),
                ),
                (
                    "stagehand_job_id",
                    models.CharField(
                        blank=True,
                        help_text="Stagehand job ID for async tracking",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "shipment",
                    models.ForeignKey(
                        help_text="The shipment being synced",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sync_attempts",
                        to="orders.shipment",
                    ),
                ),
            ],
            options={
                "verbose_name": "Shipment Sync",
                "verbose_name_plural": "Shipment Syncs",
                "db_table": "shipment_sync",
                "ordering": ["-started_at"],
                "indexes": [
                    models.Index(
                        fields=["shipment", "-started_at"],
                        name="shipment_sy_shipmen_135853_idx",
                    ),
                    models.Index(
                        fields=["status"], name="shipment_sy_status_8fc490_idx"
                    ),
                    models.Index(
                        fields=["status", "stagehand_job_id"],
                        name="shipment_sy_status_dc1323_idx",
                    ),
                ],
            },
        ),
    ]
