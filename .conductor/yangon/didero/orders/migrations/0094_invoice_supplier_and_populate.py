# Generated by Django 4.2.7 on 2025-07-08 16:24

import django.db.models.deletion
from django.db import migrations, models


def populate_invoice_supplier(apps, schema_editor):
    """
    Populate supplier field for existing invoices using their purchase order's supplier.
    """
    Invoice = apps.get_model("orders", "Invoice")

    # Get all invoices that have a purchase order but no supplier
    invoices_to_update = Invoice.objects.filter(
        purchase_order__isnull=False, supplier__isnull=True
    ).select_related("purchase_order__supplier")

    # Bulk update invoices with their PO's supplier
    for invoice in invoices_to_update:
        if invoice.purchase_order.supplier:
            invoice.supplier = invoice.purchase_order.supplier

    # Use bulk_update for efficiency
    if invoices_to_update:
        Invoice.objects.bulk_update(invoices_to_update, ["supplier"], batch_size=500)
        print(
            f"Updated {len(invoices_to_update)} invoices with supplier information from their purchase orders."
        )


def reverse_populate_invoice_supplier(apps, schema_editor):
    """
    Reverse migration: Clear supplier field for invoices that have the same supplier as their PO.
    """
    Invoice = apps.get_model("orders", "Invoice")

    # Clear supplier for invoices where supplier matches PO's supplier
    invoices_to_clear = Invoice.objects.filter(
        purchase_order__isnull=False, supplier__isnull=False
    ).select_related("purchase_order__supplier")

    updated_count = 0
    for invoice in invoices_to_clear:
        if invoice.purchase_order.supplier == invoice.supplier:
            invoice.supplier = None
            updated_count += 1

    # Use bulk_update for efficiency
    if updated_count > 0:
        Invoice.objects.bulk_update(invoices_to_clear, ["supplier"], batch_size=500)
        print(f"Cleared supplier field for {updated_count} invoices.")


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0095_supplier_available_shipping_terms_and_more"),
        ("orders", "0093_merge_20250701_1519"),
    ]

    operations = [
        migrations.AddField(
            model_name="invoice",
            name="supplier",
            field=models.ForeignKey(
                blank=True,
                help_text="The supplier this invoice is from",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="invoices",
                to="suppliers.supplier",
            ),
        ),
        migrations.RunPython(
            populate_invoice_supplier, reverse_populate_invoice_supplier
        ),
    ]
