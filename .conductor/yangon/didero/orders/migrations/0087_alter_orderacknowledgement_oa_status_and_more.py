# Generated by Django 4.2.7 on 2025-06-05 21:44

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0086_change_oa_status_partial_to_incomplete"),
    ]

    operations = [
        migrations.AlterField(
            model_name="orderacknowledgement",
            name="oa_status",
            field=models.CharField(
                choices=[
                    ("COMPLETE", "Complete - has all needed info"),
                    ("INCOMPLETE", "Incomplete - missing ship date"),
                    (
                        "PENDING_CLARIFICATION",
                        "Pending clarification - validation issues detected",
                    ),
                    ("REJECTED", "Rejected - supplier cannot fulfill order"),
                ],
                default="INCOMPLETE",
                help_text="Status of this order acknowledgment",
                max_length=30,
            ),
        ),
        migrations.AddIndex(
            model_name="orderacknowledgement",
            index=models.Index(
                fields=["oa_status"], name="orders_orde_oa_stat_eff74e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="orderacknowledgement",
            index=models.Index(
                fields=["promised_ship_date"], name="orders_orde_promise_faa592_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="orderacknowledgement",
            index=models.Index(
                fields=["created_at"], name="orders_orde_created_a3afa1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="orderacknowledgement",
            index=models.Index(
                fields=["purchase_order", "order_number"],
                name="orders_orde_purchas_28e82e_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="orderacknowledgement",
            constraint=models.UniqueConstraint(
                condition=models.Q(("order_number__isnull", False)),
                fields=("purchase_order", "order_number"),
                name="unique_oa_per_po_and_order_number",
            ),
        ),
    ]
