# Generated by Django 4.2.7 on 2025-06-13 19:47

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0089_alter_invoiceitem_total_price_currency_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="purchaseorder",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("pending_acceptance", "PENDING_ACCEPTANCE"),
                    ("awaiting_shipment", "AWAITING_SHIPMENT"),
                    ("ready_for_pickup", "READY_FOR_PICKUP"),
                    ("supplier_rejected", "SUPPLIER_REJECTED"),
                    ("shipment_delayed", "SHIPMENT_DELAYED"),
                    ("shipped", "SHIPPED"),
                    ("partially_shipped", "PARTIALLY_SHIPPED"),
                    ("delivery_delayed", "DELIVERY_DELAYED"),
                    ("received", "RECEIVED"),
                    ("partially_received", "PARTIALLY_RECEIVED"),
                    ("canceled", "CANCELED"),
                    ("invoice_matched", "INVOICE_MATCHED"),
                    ("issue", "ISSUE"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
    ]
