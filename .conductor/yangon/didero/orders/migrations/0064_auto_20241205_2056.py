# Generated by Django 4.2.7 on 2024-12-05 20:56

from django.db import migrations

from didero.orders.models import OrderItem


def backfill_requested_ship_date_on_order_item(apps, schema_editor):
    OrderItemModel: OrderItem = apps.get_model("orders", "OrderItem")
    for instance in OrderItemModel.objects.all():
        if not instance.requested_ship_date:
            instance.requested_ship_date = instance.purchase_order.requested_ship_date


def reverse_backfill_requested_ship_date_on_order_item(apps, schema_editor):
    OrderItemModel: OrderItem = apps.get_model("orders", "OrderItem")
    for instance in OrderItemModel.objects.all():
        if instance.requested_ship_date == instance.purchase_order.requested_ship_date:
            instance.requested_ship_date = None


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0063_rename_requested_delivery_date_orderitem_requested_ship_date"),
    ]

    operations = [
        migrations.RunPython(
            backfill_requested_ship_date_on_order_item,
            reverse_code=reverse_backfill_requested_ship_date_on_order_item,
        )
    ]
