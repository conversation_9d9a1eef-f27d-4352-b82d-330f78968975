# Generated by Django 4.2.7 on 2024-03-19 18:19

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0003_remove_orderitem_price"),
    ]

    operations = [
        migrations.RenameField(
            model_name="orderitem",
            old_name="purchaseOrder",
            new_name="purchase_order",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="deliveryStatus",
            new_name="delivery_status",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="internalNotes",
            new_name="internal_notes",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="orderStatus",
            new_name="order_status",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="paymentStatus",
            new_name="payment_status",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="placementTime",
            new_name="placement_time",
        ),
        migrations.<PERSON>ameField(
            model_name="purchaseorder",
            old_name="requestedDeliveryDate",
            new_name="requested_ship_date",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="shippingAddressLine1",
            new_name="shipping_address_line_1",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="shippingAddressLine2",
            new_name="shipping_address_line_2",
        ),
        migrations.RenameField(
            model_name="purchaseorder",
            old_name="vendorNotes",
            new_name="vendor_notes",
        ),
    ]
