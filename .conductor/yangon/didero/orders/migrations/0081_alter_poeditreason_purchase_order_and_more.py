# Generated by Django 4.2.7 on 2025-04-28 10:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0080_merge_20250425_1759"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="poeditreason",
            name="purchase_order",
            field=models.ForeignKey(
                help_text="The purchase order this edit reason is associated with",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="edit_reasons",
                to="orders.purchaseorder",
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="is_po_editable",
            field=models.BooleanField(blank=True, default=True, null=True),
        ),
    ]
