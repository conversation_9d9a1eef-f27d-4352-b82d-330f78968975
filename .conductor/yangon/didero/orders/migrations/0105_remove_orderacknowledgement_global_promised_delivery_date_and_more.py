# Generated by Django 4.2.7 on 2025-07-22 22:06

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0104_merge_20250721_1956"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="orderacknowledgement",
            name="global_promised_delivery_date",
        ),
        migrations.RemoveField(
            model_name="orderacknowledgement",
            name="global_promised_ship_date",
        ),
        migrations.AlterField(
            model_name="orderacknowledgement",
            name="oa_status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("COMPLETE", "Complete - has all needed info"),
                    ("INCOMPLETE", "Incomplete - missing item ship dates"),
                    (
                        "PENDING_CLARIFICATION",
                        "Pending clarification - validation issues detected",
                    ),
                    ("REJECTED", "Rejected - supplier cannot fulfill order"),
                ],
                default="INCOMPLETE",
                help_text="Status of this order acknowledgment",
                max_length=30,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="purchaseorder",
            name="order_status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("pending_acceptance", "PENDING_ACCEPTANCE"),
                    ("oa_mismatch", "OA_MISMATCH"),
                    ("awaiting_shipment", "AWAITING_SHIPMENT"),
                    ("ready_for_pickup", "READY_FOR_PICKUP"),
                    ("supplier_rejected", "SUPPLIER_REJECTED"),
                    ("shipment_delayed", "SHIPMENT_DELAYED"),
                    ("shipped", "SHIPPED"),
                    ("partially_shipped", "PARTIALLY_SHIPPED"),
                    ("delivery_delayed", "DELIVERY_DELAYED"),
                    ("received", "RECEIVED"),
                    ("partially_received", "PARTIALLY_RECEIVED"),
                    ("canceled", "CANCELED"),
                    ("invoice_matched", "INVOICE_MATCHED"),
                    ("issue", "ISSUE"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="carrier_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("fedex", "FEDEX"),
                    ("zim", "ZIM"),
                    ("one", "ONE"),
                    ("unknown", "UNKNOWN"),
                ],
                null=True,
            ),
        ),
    ]
