# Generated by Django 4.2.7 on 2024-05-14 20:02

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0017_merge_20240510_2119"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="purchaseorder",
            unique_together=set(),
        ),
        migrations.AddConstraint(
            model_name="purchaseorder",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("po_number__isnull", False), ("supplier__isnull", False)
                ),
                fields=("po_number", "supplier"),
                name="unique_po_number_supplier",
            ),
        ),
    ]
