# Generated by Django 4.2.7 on 2024-10-18 17:24

from django.db import migrations


def remove_empty_shipments(apps, schema_editor):
    Shipment = apps.get_model("orders", "Shipment")
    for shipment in Shipment.objects.all():
        if not shipment.line_items.exists():
            shipment.delete()


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0055_shipment_status_alter_purchaseorder_order_status"),
    ]

    operations = [
        migrations.RunPython(remove_empty_shipments),
    ]
