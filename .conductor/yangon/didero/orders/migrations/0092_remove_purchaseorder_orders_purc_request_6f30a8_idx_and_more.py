# Generated by Django 4.2.7 on 2025-06-30 19:29

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0091_combined_shipping_terms_migration"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="purchaseorder",
            name="orders_purc_request_6f30a8_idx",
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="requested_date",
            field=models.DateField(
                blank=True,
                help_text="Interpretation depends on shipping_terms: EXW='ready for pickup', DAP='delivery date', etc.",
                null=True,
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["requested_date"], name="orders_purc_request_eef280_idx"
            ),
        ),
    ]
