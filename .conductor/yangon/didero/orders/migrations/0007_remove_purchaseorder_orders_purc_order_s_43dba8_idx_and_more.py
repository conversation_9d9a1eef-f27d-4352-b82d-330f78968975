# Generated by Django 4.2.7 on 2024-04-10 20:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0006_add_idxs_purchaseorder"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="purchaseorder",
            name="orders_purc_order_s_43dba8_idx",
        ),
        migrations.RemoveField(
            model_name="purchaseorder",
            name="items",
        ),
        migrations.AlterField(
            model_name="orderitem",
            name="purchase_order",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="orders.purchaseorder",
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("pending", "PENDING"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("confirmed", "CONFIRMED"),
                    ("rejected", "REJECTED"),
                    ("in_production", "IN_PRODUCTION"),
                    ("canceled", "CANCELED"),
                    ("shipped", "SHIPPED"),
                    ("received", "RECEIVED"),
                    ("completed", "COMPLETED"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["order_status"], name="orders_purc_order_s_3a7f61_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["payment_status"], name="orders_purc_payment_d8f0b3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["delivery_status"], name="orders_purc_deliver_2255e6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(fields=["source"], name="orders_purc_source_872d95_idx"),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["placement_time"], name="orders_purc_placeme_5271f7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["requested_ship_date"], name="orders_purc_request_6f30a8_idx"
            ),
        ),
    ]
