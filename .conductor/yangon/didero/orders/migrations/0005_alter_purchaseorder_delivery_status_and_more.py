# Generated by Django 4.2.7 on 2024-03-21 15:31

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0004_rename_purchaseorder_orderitem_purchase_order_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorder",
            name="delivery_status",
            field=models.CharField(
                choices=[
                    ("undelivered", "UNDELIVERED"),
                    ("partially_delivered", "PARTIALLY_DELIVERED"),
                    ("delivered", "DELIVERED"),
                ],
                default="undelivered",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("pending", "PENDING"),
                    ("approved", "APPROVED"),
                    ("rejected", "REJECTED"),
                    ("canceled", "CANCELED"),
                    ("shipped", "SHIPPED"),
                    ("received", "RECEIVED"),
                    ("completed", "COMPLETED"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="purchaseorder",
            name="payment_status",
            field=models.CharField(
                choices=[
                    ("unpaid", "UNPAID"),
                    ("invoice_received", "INVOICE_RECEIVED"),
                    ("ready_to_pay", "READY_TO_PAY"),
                    ("paid", "PAID"),
                ],
                default="unpaid",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="source",
            field=models.CharField(
                choices=[("email", "EMAIL"), ("erp", "ERP"), ("didero", "DIDERO")],
                max_length=20,
            ),
        ),
    ]
