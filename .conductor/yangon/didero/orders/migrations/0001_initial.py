# Generated by Django 4.2.7 on 2024-03-12 17:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("suppliers", "0031_communication_email_from_communication_email_to_and_more"),
        ("users", "0014_alter_team_uuid"),
        ("items", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("quantity", models.PositiveIntegerField()),
                ("price", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="items.item"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PurchaseOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("po_number", models.CharField(max_length=255)),
                ("shippingAddressLine1", models.CharField(max_length=255)),
                ("shippingAddressLine2", models.CharField(blank=True, max_length=255)),
                ("internalNotes", models.TextField(blank=True)),
                ("vendorNotes", models.TextField(blank=True)),
                (
                    "orderStatus",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("canceled", "Canceled"),
                            ("shipped", "Shipped"),
                            ("received", "Received"),
                            ("completed", "Completed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "paymentStatus",
                    models.CharField(
                        choices=[
                            ("unpaid", "Unpaid"),
                            ("invoice_received", "Invoice Received"),
                            ("ready_to_pay", "Ready to Pay"),
                            ("paid", "Paid"),
                        ],
                        default="unpaid",
                        max_length=20,
                    ),
                ),
                (
                    "deliveryStatus",
                    models.CharField(
                        choices=[
                            ("undelivered", "Undelivered"),
                            ("partially_delivered", "Partially Delivered"),
                            ("delivered", "Delivered"),
                        ],
                        default="undelivered",
                        max_length=20,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("erp", "ERP"),
                            ("didero", "Didero"),
                        ],
                        default="didero",
                        max_length=10,
                    ),
                ),
                ("placementTime", models.DateTimeField(auto_now_add=True)),
                ("requestedDeliveryDate", models.DateField(blank=True, null=True)),
                (
                    "items",
                    models.ManyToManyField(through="orders.OrderItem", to="items.item"),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="suppliers.supplier",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.team"
                    ),
                ),
            ],
            options={
                "unique_together": {("po_number", "supplier")},
            },
        ),
        migrations.AddField(
            model_name="orderitem",
            name="purchaseOrder",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="orders.purchaseorder"
            ),
        ),
    ]
