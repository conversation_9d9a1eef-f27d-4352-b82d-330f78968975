from django.db import migrations
from djmoney.models.fields import MoneyField, <PERSON><PERSON><PERSON>cyField


def populate_orderitem_price(apps, schema_editor):
    Item = apps.get_model("items", "Item")
    OrderItem = apps.get_model("orders", "OrderItem")
    for order_item in OrderItem.objects.all():
        item = Item.objects.filter(id=order_item.item_id).first()
        if item and item.price is not None:
            order_item.price = item.price
            order_item.price_currency = item.price_currency
        else:
            order_item.price = 0.0
            order_item.price_currency = "USD"
        order_item.save()


class Migration(migrations.Migration):
    from django.apps import apps

    Item = apps.get_model("items", "Item")
    OrderItem = apps.get_model("orders", "OrderItem")

    dependencies = [
        ("orders", "0012_alter_purchaseorder_internal_notes_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderitem",
            name="price",
            field=MoneyField(
                max_digits=14, decimal_places=2, default_currency="USD", null=True
            ),
        ),
        migrations.AddField(
            model_name="orderitem",
            name="price_currency",
            field=CurrencyField(
                choices=Item._meta.get_field("price_currency").choices,
                default="USD",
                editable=False,
                max_length=3,
                null=True,
            ),
        ),
        migrations.RunPython(populate_orderitem_price),
        migrations.AlterField(
            model_name="orderitem",
            name="price",
            field=MoneyField(
                max_digits=14, decimal_places=2, default_currency="USD", null=False
            ),
        ),
        migrations.AlterField(
            model_name="orderitem",
            name="price_currency",
            field=CurrencyField(
                choices=Item._meta.get_field("price_currency").choices,
                default="USD",
                editable=False,
                max_length=3,
                null=False,
            ),
        ),
    ]
