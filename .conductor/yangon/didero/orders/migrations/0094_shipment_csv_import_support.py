# Generated manually for shipment CSV import support

import django.db.models.deletion
from django.db import migrations, models


def migrate_team_to_shipments(apps, schema_editor):
    """Copy team from purchase order to shipment for existing shipments."""
    Shipment = apps.get_model("orders", "Shipment")

    # Update shipments that have a purchase order
    for shipment in Shipment.objects.filter(
        purchase_order__isnull=False
    ).select_related("purchase_order__team"):
        if shipment.purchase_order and shipment.purchase_order.team:
            shipment.team = shipment.purchase_order.team
            shipment.save(update_fields=["team"])


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0093_merge_20250701_1519"),
        ("users", "0057_alter_teamsetting_name"),
    ]

    operations = [
        # Add container and BOL fields
        migrations.AddField(
            model_name="shipment",
            name="container_number",
            field=models.CharField(
                max_length=255,
                null=True,
                blank=True,
                help_text="Container number for the shipment",
            ),
        ),
        migrations.AddField(
            model_name="shipment",
            name="bol_number",
            field=models.CharField(
                max_length=255,
                null=True,
                blank=True,
                help_text="Bill of Lading (BOL) number for the shipment",
            ),
        ),
        # Add team field to shipment
        migrations.AddField(
            model_name="shipment",
            name="team",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shipments",
                to="users.team",
            ),
        ),
        # Run data migration to copy team from purchase orders
        migrations.RunPython(
            migrate_team_to_shipments,
            reverse_code=migrations.RunPython.noop,
        ),
        # Make purchase_order nullable (it already is in the model)
        migrations.AlterField(
            model_name="shipment",
            name="purchase_order",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shipments",
                to="orders.purchaseorder",
            ),
        ),
        # Update carrier_type field choices
        migrations.AlterField(
            model_name="shipment",
            name="carrier_type",
            field=models.CharField(
                blank=True,
                choices=[("fedex", "FEDEX"), ("zim", "ZIM"), ("unknown", "UNKNOWN")],
                null=True,
            ),
        ),
        # Add metadata field if it doesn't exist
        migrations.AddField(
            model_name="shipment",
            name="metadata",
            field=models.JSONField(
                default=dict,
                blank=True,
                help_text="Additional shipment data like port of departure, port of arrival, etc.",
            ),
        ),
    ]
