# Generated by Django 4.2.7 on 2024-06-17 18:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


def note_to_comment(apps, schema_editor):
    PurchaseOrderNote = apps.get_model("orders", "PurchaseOrderNote")
    PurchaseOrderComment = apps.get_model("orders", "PurchaseOrderComment")
    for note in PurchaseOrderNote.objects.all():
        PurchaseOrderComment.objects.create(
            created_at=note.created_at,
            modified_at=note.modified_at,
            comment=note.note,
            created_by=note.created_by,
            purchase_order=note.purchase_order,
        )


def comment_to_note(apps, schema_editor):
    PurchaseOrderNote = apps.get_model("orders", "PurchaseOrderNote")
    PurchaseOrderComment = apps.get_model("orders", "PurchaseOrderComment")
    for comment in PurchaseOrderComment.objects.all():
        PurchaseOrderNote.objects.create(
            created_at=comment.created_at,
            modified_at=comment.modified_at,
            note=comment.comment,
            created_by=comment.created_by,
            purchase_order=comment.purchase_order,
        )


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("orders", "0029_lineitem"),
    ]

    operations = [
        migrations.CreateModel(
            name="PurchaseOrderComment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("comment", models.TextField()),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notes",
                        to="orders.purchaseorder",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.RunPython(note_to_comment, comment_to_note),
        migrations.DeleteModel(
            name="PurchaseOrderNote",
        ),
    ]
