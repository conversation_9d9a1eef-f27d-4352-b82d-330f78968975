# Generated by Django 4.2.7 on 2024-05-08 18:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0032_alter_suppliernote_created_by_documentnote"),
        ("orders", "0011_alter_purchaseorder_order_status_orderapprovalrule_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorder",
            name="internal_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="po_number",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="shipping_address_line_1",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="shipping_address_line_2",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="purchaseorder",
            name="supplier",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="suppliers.supplier",
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="vendor_notes",
            field=models.TextField(blank=True, null=True),
        ),
    ]
