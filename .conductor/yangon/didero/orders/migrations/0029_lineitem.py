# Generated by Django 4.2.7 on 2024-06-14 18:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0028_remove_purchaseorder_unique_po_number_supplier_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="LineItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("tax", "TAX"),
                            ("discount", "DISCOUNT"),
                            ("shipping", "SHIPPING"),
                            ("handling", "HANDLING"),
                            ("fee", "FEE"),
                            ("rebate", "REBATE"),
                            ("service", "SERVICE"),
                            ("material", "MATERIAL"),
                            ("promotion", "PROMOTION"),
                            ("surcharge", "SURCHARGE"),
                            ("refund", "REFUND"),
                            ("custom", "CUSTOM"),
                        ],
                        default="custom",
                        max_length=20,
                    ),
                ),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="line_items",
                        to="orders.purchaseorder",
                    ),
                ),
            ],
        ),
    ]
