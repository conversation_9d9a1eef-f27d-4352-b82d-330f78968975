# Generated by Django 4.2.7 on 2024-10-02 04:19

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models

from didero.orders.schemas import ShipmentStatus


def populate_shipments_from_po(apps, schema_editor):
    PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    Shipment = apps.get_model("orders", "Shipment")
    for po in PurchaseOrder.objects.all():
        ship_status = ShipmentStatus.AWAITING_SHIPMENT.value
        if po.order_status == ShipmentStatus.SHIPPED.value:
            ship_status = ShipmentStatus.SHIPPED.value
        elif po.order_status == ShipmentStatus.RECEIVED.value:
            ship_status = ShipmentStatus.RECEIVED.value

        s = Shipment.objects.create(
            purchase_order=po,
            team=po.team,
            carrier=po.carrier,
            tracking_number=po.tracking_number,
            estimated_delivery_date=po.estimated_delivery_date,
            actual_delivery_date=po.actual_delivery_date,
            status=ship_status,
        )
        for order_item in po.items.all():
            s.line_items.create(
                shipment=s,
                order_item=order_item,
                shipped_quantity=order_item.quantity,
            )


def populate_pos_from_shipments(apps, schema_editor):
    PurchaseOrder = apps.get_model("orders", "PurchaseOrder")
    for po in PurchaseOrder.objects.all():
        shipment = po.shipments.first()
        if shipment:
            po.carrier = shipment.carrier
            po.tracking_number = shipment.tracking_number
            po.estimated_delivery_date = shipment.estimated_delivery_date
            po.actual_delivery_date = shipment.actual_delivery_date
            po.save()


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0025_team_default_po_email_credential"),
        ("orders", "0052_alter_orderitem_quantity"),
    ]

    operations = [
        migrations.CreateModel(
            name="Shipment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("carrier", models.CharField(blank=True, null=True)),
                ("tracking_number", models.CharField(blank=True, null=True)),
                ("estimated_delivery_date", models.DateField(blank=True, null=True)),
                ("actual_delivery_date", models.DateField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ShipmentLineItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("shipped_quantity", models.FloatField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="shipmentlineitem",
            name="order_item",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shipment_line_items",
                to="orders.orderitem",
            ),
        ),
        migrations.AddField(
            model_name="shipmentlineitem",
            name="shipment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="line_items",
                to="orders.shipment",
            ),
        ),
        migrations.AddField(
            model_name="shipment",
            name="purchase_order",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shipments",
                to="orders.purchaseorder",
            ),
        ),
        migrations.AddField(
            model_name="shipment",
            name="team",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shipments",
                to="users.team",
            ),
        ),
        migrations.AddField(
            model_name="shipment",
            name="status",
            field=models.CharField(
                choices=[
                    ("awaiting_shipment", "AWAITING_SHIPMENT"),
                    ("shipped", "SHIPPED"),
                    ("received", "RECEIVED"),
                    ("canceled", "CANCELED"),
                ],
                default="awaiting_shipment",
            ),
        ),
        migrations.RunPython(
            populate_shipments_from_po, reverse_code=populate_pos_from_shipments
        ),
        migrations.RemoveIndex(
            model_name="purchaseorder",
            name="orders_purc_estimat_b90093_idx",
        ),
        migrations.RemoveIndex(
            model_name="purchaseorder",
            name="orders_purc_actual__7c444d_idx",
        ),
        migrations.RemoveIndex(
            model_name="purchaseorder",
            name="orders_purc_carrier_71e95e_idx",
        ),
        migrations.RemoveIndex(
            model_name="purchaseorder",
            name="orders_purc_trackin_7deba4_idx",
        ),
        migrations.RemoveField(
            model_name="purchaseorder",
            name="actual_delivery_date",
        ),
        migrations.RemoveField(
            model_name="purchaseorder",
            name="carrier",
        ),
        migrations.RemoveField(
            model_name="purchaseorder",
            name="estimated_delivery_date",
        ),
        migrations.RemoveField(
            model_name="purchaseorder",
            name="tracking_number",
        ),
    ]
