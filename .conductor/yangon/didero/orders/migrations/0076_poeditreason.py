# Generated by Django 4.2.7 on 2025-04-23 15:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("orders", "0075_alter_purchaseorder_is_po_editable"),
    ]

    operations = [
        migrations.CreateModel(
            name="POEditReason",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reason",
                    models.TextField(
                        help_text="The reason for editing the purchase order"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="Date of creation"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, help_text="Last updated date"),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        help_text="The purchase order this edit reason is associated with",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="edit_reasons",
                        to="orders.purchaseorder",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="The user who requested the edit",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
