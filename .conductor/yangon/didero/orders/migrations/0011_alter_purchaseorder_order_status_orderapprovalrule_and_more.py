# Generated by Django 4.2.7 on 2024-05-07 16:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0014_alter_team_uuid"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("orders", "0010_purchaseordernote"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorder",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("confirmed", "CONFIRMED"),
                    ("rejected", "REJECTED"),
                    ("in_production", "IN_PRODUCTION"),
                    ("canceled", "CANCELED"),
                    ("shipped", "SHIPPED"),
                    ("received", "RECEIVED"),
                    ("completed", "COMPLETED"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="OrderApprovalRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cost_threshold",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "approver",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.team"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="OrderApproval",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_approved", models.BooleanField(default=False)),
                ("approved_at", models.DateTimeField(null=True)),
                (
                    "approver",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="approvals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="approvals",
                        to="orders.purchaseorder",
                    ),
                ),
            ],
            options={
                "permissions": [
                    ("approve_purchase_order", "Can approve purchase orders")
                ],
            },
        ),
    ]
