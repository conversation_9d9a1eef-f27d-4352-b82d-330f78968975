# Generated by Django 4.2.7 on 2025-04-22 19:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0073_invoice_orderacknowledgement_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="purchaseorder",
            name="is_po_editable",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="source",
            field=models.CharField(
                choices=[
                    ("email", "EMAIL"),
                    ("erp", "ERP"),
                    ("external_po_import", "EXTERNAL_PO_IMPORT"),
                    ("didero", "DIDERO"),
                    ("netsuite_sync", "NETSUITE_SYNC"),
                ],
                max_length=20,
            ),
        ),
    ]
