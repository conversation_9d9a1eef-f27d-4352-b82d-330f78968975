# Generated by Django 4.2.7 on 2025-06-05 18:30

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0083_make_approver_nullable"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderacknowledgement",
            name="oa_status",
            field=models.CharField(
                choices=[
                    ("COMPLETE", "Complete - has all needed info"),
                    ("PARTIAL", "Partial - missing ship date or other details"),
                    ("PENDING_CLARIFICATION", "Needs clarification from buyer"),
                    ("REJECTED", "Supplier rejected or cannot fulfill"),
                ],
                default="PARTIAL",
                help_text="Status of this order acknowledgment",
                max_length=30,
            ),
        ),
        migrations.AddField(
            model_name="orderacknowledgement",
            name="promised_ship_date",
            field=models.DateField(
                blank=True,
                help_text="Ship date promised by supplier in this OA",
                null=True,
            ),
        ),
    ]
