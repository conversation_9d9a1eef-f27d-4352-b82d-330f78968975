# Generated by Django 4.2.7 on 2025-06-06 14:04

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0087_alter_orderacknowledgement_oa_status_and_more"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="orderacknowledgement",
            name="orders_orde_promise_faa592_idx",
        ),
        migrations.RemoveField(
            model_name="orderacknowledgement",
            name="promised_delivery_date",
        ),
        migrations.RemoveField(
            model_name="orderacknowledgement",
            name="promised_ship_date",
        ),
        migrations.AddField(
            model_name="orderacknowledgementitem",
            name="promised_delivery_date",
            field=models.DateField(
                blank=True,
                help_text="Delivery date promised by supplier for this specific item",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="orderacknowledgementitem",
            name="promised_ship_date",
            field=models.DateField(
                blank=True,
                help_text="Ship date promised by supplier for this specific item",
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="orderacknowledgement",
            name="oa_status",
            field=models.CharField(
                choices=[
                    ("COMPLETE", "Complete - has all needed info"),
                    ("INCOMPLETE", "Incomplete - missing item ship dates"),
                    (
                        "PENDING_CLARIFICATION",
                        "Pending clarification - validation issues detected",
                    ),
                    ("REJECTED", "Rejected - supplier cannot fulfill order"),
                ],
                default="INCOMPLETE",
                help_text="Status of this order acknowledgment",
                max_length=30,
            ),
        ),
        migrations.AddIndex(
            model_name="orderacknowledgementitem",
            index=models.Index(
                fields=["promised_ship_date"], name="orders_orde_promise_9324d6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="orderacknowledgementitem",
            index=models.Index(
                fields=["promised_delivery_date"], name="orders_orde_promise_4e153d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="orderacknowledgementitem",
            index=models.Index(
                fields=["order_acknowledgement", "item_number"],
                name="orders_orde_order_a_1475a6_idx",
            ),
        ),
    ]
