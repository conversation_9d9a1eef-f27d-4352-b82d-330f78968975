# Generated by Django 4.2.7 on 2024-11-18 22:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflows", "0005_alter_userworkflow_trigger"),
        ("orders", "0060_purchaseorder_is_external_po_processing"),
    ]

    operations = [
        migrations.CreateModel(
            name="PurchaseOrderWorkflowMilestoneLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("milestone_name", models.CharField(max_length=64)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workflow_milestone_logs",
                        to="orders.purchaseorder",
                    ),
                ),
                (
                    "workflow_run",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="milestone_logs",
                        to="workflows.workflowrun",
                    ),
                ),
            ],
        ),
    ]
