# Generated by Django 4.2.7 on 2024-08-26 20:18

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0041_alter_purchaseorder_tags"),
    ]

    operations = [
        migrations.AddField(
            model_name="delivery",
            name="delivery_status",
            field=models.CharField(
                choices=[
                    ("partially_delivered", "PARTIALLY_DELIVERED"),
                    ("delivered", "DELIVERED"),
                    ("issue", "ISSUE"),
                ],
                default="delivered",
                max_length=20,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("draft", "DRAFT"),
                    ("approval_pending", "APPROVAL_PENDING"),
                    ("approval_denied", "APPROVAL_DENIED"),
                    ("approved", "APPROVED"),
                    ("issued", "ISSUED"),
                    ("pending_acceptance", "PENDING_ACCEPTANCE"),
                    ("supplier_accepted", "SUPPLIER_ACCEPTED"),
                    ("supplier_rejected", "SUPPLIER_REJECTED"),
                    ("shipped", "SHIPPED"),
                    ("partially_received", "PARTIALLY_RECEIVED"),
                    ("received", "RECEIVED"),
                    ("canceled", "CANCELED"),
                    ("invoice_matched", "INVOICE_MATCHED"),
                    ("issue", "ISSUE"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
    ]
