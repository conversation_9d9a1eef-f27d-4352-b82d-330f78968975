from copy import deepcopy
from datetime import date
from typing import TYPE_CHECKING

import structlog
from celery import shared_task
from celery_singleton import Singleton
from django.contrib.contenttypes.models import ContentType

from didero.ai.po_extraction.po_extraction import (
    process_external_po,
    process_external_quote_to_po,
)
from didero.emails.postmark_service import PostmarkEmailService
from didero.emails.utils.email_utils import map_order_to_thread
from didero.integrations.utils.fedex.fedex import get_tracking_info_for_tracking_number
from didero.orders.models import (
    LineItem,
    OrderItem,
    PurchaseOrder,
    PurchaseOrderComment,
    Shipment,
)
from didero.orders.schemas import (
    CarrierType,
    LineItemCategoryChoices,
    PurchaseOrderStatus,
    ShipmentStatus,
)
from didero.orders.utils.order_approvals_util import OrderApprovalsHelper
from didero.orders.utils.order_util import OrderHelper
from didero.runtime_configs.models import RuntimeConfig
from didero.suppliers.models import Communication
from didero.tasks.schemas import TaskType as TaskTypeName
from didero.tasks.utils import create_po_created_from_quotation_task
from didero.users.models.team_models import Team
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums

if TYPE_CHECKING:
    from didero.documents.models import Document

logger = structlog.get_logger(__name__)

LOCK_EXPIRE = 60 * 60  # 1 hour


@shared_task(base=Singleton, lock_expiry=LOCK_EXPIRE, queue="periodic_tasks")
def send_po_approval_reminder_email():
    if RuntimeConfig.get_tasks_email_service_disabled():
        logger.info(
            "RuntimeConfig is disabling the email service; skipping 'send_notifications_reminder_email'"
        )
        return

    # {"approver_email": [str]}
    pending_approvals = {}

    # get POs that are pending approval
    pending_pos = PurchaseOrder.objects.filter(
        order_status=PurchaseOrderStatus.APPROVAL_PENDING
    )

    for po in pending_pos:
        # get the approvals that are still open and add to the pending_approvals map
        po_pending_approval = OrderApprovalsHelper.get_next_pending_approval_for_po(po)
        if po_pending_approval:
            pending_approvals.setdefault(po_pending_approval.approver, []).append(
                po_pending_approval
            )
        else:
            # this should not heppen. should we raise an exception?
            logger.info(f"PO {po.po_number} does not have any approvals.")

    for approver in pending_approvals.keys():
        # go through all approvers that owe approvals and send them an email each
        PostmarkEmailService.send_pending_approvals_reminder_email(
            approver, pending_approvals[approver]
        )


@shared_task(base=Singleton, lock_expiry=LOCK_EXPIRE, queue="periodic_tasks")
def archive_purchase_orders():
    logger.info("Starting the order archiving process for the day.")
    # go through teams
    for team in Team.objects.all():
        # get team auto archive after days setting
        auto_archive_after_days = (
            TeamSetting.objects.filter(
                team=team, name=TeamSettingEnums.TEAM_AUTO_ARCHIVE_AFTER_DAYS
            )
            .get()
            .value
        )
        # call the helper to archive POs
        OrderHelper.archive_pos_for_team(team, auto_archive_after_days)


@shared_task(base=Singleton, lock_expiry=60 * 5, queue="ai_workflows")
def process_external_quote_task(order_pk, comm_pk=None):
    order = PurchaseOrder.objects.get(id=order_pk)
    doc_or_comm: Document | Communication = None
    if comm_pk:
        try:
            doc_or_comm = Communication.objects.get(id=comm_pk)
        except Communication.DoesNotExist:
            logger.error(
                f"Process External Quote: No Communication found with pk {comm_pk} when processing async order {order_pk}, skipping processing"
            )
            return
    else:
        if not order.document:
            logger.error(
                f"Process External Quote: No Document attached to async order {order_pk}, skipping processing"
            )
            return
        doc_or_comm = order.document

    logger.info(f"Processing External Quote with id {order.id}")
    result = process_external_quote_to_po(doc_or_comm, order_id=order.id)

    # If no supplier found, consider it a failure and end the creation process
    if not result.supplier:
        logger.error(
            f"Processing External Quote with id {order.id} failed as supplier is empty. This was the response {result}"
        )
        order.is_external_po_processing = False
        order.save()
        return

    order.supplier = result.supplier
    order.sender_address = result.supplier_address

    # TODO For now create the OrderItem with a quantity of 1.
    # We need to think through what this looks like from a product experience
    # Draft Order Items with the ability to set quantities?
    if result.items:
        for item in result.items:
            _ = OrderItem.objects.create(
                purchase_order=order,
                item=item,
                quantity=1,
                price=item.price,
            )

    if result.additional_line_items:
        for adtl_line_item in result.additional_line_items:
            _ = LineItem.objects.create(
                purchase_order=order,
                category=LineItemCategoryChoices.CUSTOM.value,
                description=adtl_line_item.description,
                amount=adtl_line_item.price / 100.0,
            )

    order.is_external_po_processing = False

    # If this was triggered by a Communication, create a task which lets the team know a PO was created from a Quotation.
    if isinstance(doc_or_comm, Communication):
        map_order_to_thread(
            order, doc_or_comm
        )  # map the communication that triggered this PO to get created to the PO for accessibility.
        create_po_created_from_quotation_task(
            task_type_name=TaskTypeName.PO_CREATED_FROM_QUOTATION,
            user=order.placed_by,  # TODO: this will be the AI user (contractors+ for now) since these are AI generated orders.
            model_type=ContentType.objects.get_for_model(PurchaseOrder),
            model_id=order.id,
            po_number=order.po_number,
        )

    order.save()
