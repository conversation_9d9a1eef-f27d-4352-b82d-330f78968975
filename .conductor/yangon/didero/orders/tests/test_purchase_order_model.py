import datetime

from django.core.exceptions import ValidationError
from djmoney.money import Money

from didero.orders.models import LineItem
from didero.orders.schemas import (
    ORDER_STATUS_TRANSITIONS,
    PAYMENT_STATUS_TRANSITIONS,
    LineItemCategoryChoices,
    PurchaseOrderStatus,
)
from didero.tasks.models import Task
from didero.testing.cases import TestCase
from didero.utils.utils import get_didero_ai_user

CURRENCY_CODE = "USD"


##
# Tests for the purchase order model
##
class TestPurchaseOrderModel(TestCase):
    def setUp(self):
        user = self.create_user(
            email="<EMAIL>",
            first_name="Bam",
            last_name="Adebayo",
        )
        self.user = user
        self.team = user.teams.first()

        self.supplier = self.create_supplier(team=self.team)
        self.order = self.create_purchase_order(
            supplier=self.supplier, team=self.team, placed_by=user
        )

        self.item = self.create_item(
            supplier=self.supplier, team_id=self.team.id, price=Money(10, CURRENCY_CODE)
        )

        self.order_item = self.create_order_item(
            purchase_order=self.order, item=self.item, quantity=1
        )

    # When a team has a default requestor, if the Didero admin account
    # creates a PO, the PO's placed_by should be changed to the default requestor
    def test_default_requestor_null_placed_by(self):
        self.team.default_requestor = self.user
        self.team.save()
        test_order = self.create_purchase_order(
            po_number="PO-345", supplier=self.supplier, team=self.team, placed_by=None
        )
        self.assertEqual(test_order.placed_by, self.user)

    def test_default_requestor_admin_placed_by(self):
        self.team.default_requestor = self.user
        self.team.save()
        admin_user = get_didero_ai_user(self.team)
        test_order = self.create_purchase_order(
            po_number="PO-456",
            supplier=self.supplier,
            team=self.team,
            placed_by=admin_user,
        )
        self.assertEqual(test_order.placed_by, self.user)

    def test_no_dupe_transitions(self):
        order_status_triggers = [
            transition["trigger"] for transition in ORDER_STATUS_TRANSITIONS
        ]
        payment_status_triggers = [
            transition["trigger"] for transition in PAYMENT_STATUS_TRANSITIONS
        ]

        overlap = set(order_status_triggers) & set(payment_status_triggers)
        self.assertEqual(
            len(overlap),
            0,
            f"Overlap between order status triggers and payment status triggers: {overlap}",
        )

    # test that when a PO is sent for approval, but then reverted to a draft, we delete all approvals and tasks
    def test_reverting_to_draft(self):
        # Revert the PO to a draft
        self.order.order_status = PurchaseOrderStatus.DRAFT.value
        self.order.save()

        # Check that the approvals and tasks are deleted
        self.assertEqual(self.order.approvals.count(), 0)
        self.assertEqual(
            Task.objects.filter(
                model_id=self.order.id, task_type__name="PO_APPROVAL_REQUEST"
            ).count(),
            0,
        )

    def test_get_total_cost(self):
        self.assertEqual(self.order.get_total_cost(), self.order.items.first().price)
        self.assertEqual(
            self.order.get_total_cost_currency(),
            self.order.items.first().price_currency,
        )

    def test_get_total_cost_eur(self):
        self.order_item.delete()
        self.item.delete()

        item = self.create_item(
            supplier=self.supplier, team_id=self.team.id, price=Money(100, "USD")
        )
        self.create_order_item(
            purchase_order=self.order, item=item, quantity=1, price=Money(55, "EUR")
        )

        # we have to call get_total_cost() since the PO got items AFTER being created
        self.assertEqual(self.order.get_total_cost(), self.order.items.first().price)
        self.assertEqual(
            self.order.get_total_cost_currency(),
            self.order.items.first().price_currency,
        )

    def test_get_total_cost_line_items(self):
        LineItem.objects.create(
            purchase_order_id=self.order.pk,
            category=LineItemCategoryChoices.TAX,
            amount=Money(200, CURRENCY_CODE),
        )
        # we have to call get_total_cost() since the PO got line items AFTER being created
        self.assertEqual(
            self.order.get_total_cost(),
            self.order.items.first().price + self.order.line_items.first().amount,
        )

    def test_purchase_order_with_different_requested_dates_on_order_items(
        self,
    ):
        # Create two order items with different requested dates
        item1 = self.create_item(
            item_number="MYTESTNUMBER",
            supplier=self.supplier,
            team_id=self.team.id,
            price=Money(50, "USD"),
        )
        order_item1 = self.create_order_item(
            purchase_order=self.order,
            item=item1,
            quantity=1,
            requested_date="2023-10-01",
        )

        item2 = self.create_item(
            item_number="MYTESTNUMBER2",
            supplier=self.supplier,
            team_id=self.team.id,
            price=Money(75, "USD"),
        )
        order_item2 = self.create_order_item(
            purchase_order=self.order,
            item=item2,
            quantity=1,
            requested_date="2023-10-15",
        )

        # Assert that the requested dates are correctly set
        self.assertEqual(order_item1.requested_date, "2023-10-01")
        self.assertEqual(order_item2.requested_date, "2023-10-15")

        # Assert that the purchase order contains both order items
        self.assertIn(order_item1, self.order.items.all())
        self.assertIn(order_item2, self.order.items.all())

    def test_purchase_order_with_shipments(self):
        test_order = self.create_purchase_order(
            po_number="PO-789",
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )
        test_order.shipments.create(
            carrier="Fedex",
            tracking_number="1234567890",
            shipment_date="2023-10-01",
            estimated_delivery_date="2023-10-07",
        )

        self.assertEqual(test_order.shipments.count(), 1)

        shipment = test_order.shipments.first()
        self.assertIsNotNone(shipment)
        self.assertEqual(shipment.carrier, "Fedex")
        self.assertEqual(shipment.tracking_number, "1234567890")
        self.assertEqual(shipment.shipment_date, datetime.date(2023, 10, 1))
        self.assertEqual(shipment.estimated_delivery_date, datetime.date(2023, 10, 7))
