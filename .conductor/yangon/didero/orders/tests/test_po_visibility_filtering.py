from rest_framework.authtoken.models import Token

from didero.orders.models import Pur<PERSON><PERSON>rder
from didero.orders.schemas import Pur<PERSON><PERSON><PERSON>rStatus, SourceChoices
from didero.suppliers.models import Supplier
from didero.testing.cases import TestCase
from didero.users.models import Team, User
from didero.users.models.team_models import TeamDomain
from didero.utils.utils import get_didero_ai_user


class POVisibilityFilteringTestCase(TestCase):
    """Test that non-editable POs are only visible to the Didero AI user"""

    def setUp(self):
        super().setUp()

        # Create a regular user
        self.regular_user = self.create_user(
            email="<EMAIL>",
            first_name="Regular",
            last_name="User",
        )
        self.team = self.regular_user.teams.first()

        # Ensure the team has a domain (required for AI user email)
        if not self.team.team_domains.exists():
            TeamDomain.objects.create(team=self.team, domain="test.com")

        # Get or create the Didero AI user for this team
        self.ai_user = get_didero_ai_user(self.team)
        if not self.ai_user:
            # Create AI user if it doesn't exist
            from didero.utils.utils import get_didero_ai_user_email

            self.ai_user = User.objects.create(
                email=get_didero_ai_user_email(self.team),
                first_name="Didero",
                last_name="AI",
            )
            self.ai_user.teams.add(self.team)

        # Create a supplier
        self.supplier = Supplier.objects.create(
            name="Test Supplier",
            team=self.team,
        )

        # Authenticate as regular user
        self.auth_as_user(self.regular_user)

        # Create separate client for AI user
        from rest_framework.test import APIClient

        self.ai_client = APIClient()
        ai_token = Token.objects.create(user=self.ai_user)
        self.ai_client.credentials(HTTP_AUTHORIZATION=f"Token {ai_token.key}")

    def test_regular_user_cannot_see_non_editable_pos(self):
        """Test that regular users cannot see POs with is_po_editable=False"""
        # Create a non-editable PO (pending AI validation)
        non_editable_po = PurchaseOrder.objects.create(
            po_number="PO-001",
            team=self.team,
            supplier=self.supplier,
            is_po_editable=False,
            order_status=PurchaseOrderStatus.DRAFT.value,
            source=SourceChoices.EMAIL.value,
        )

        # Create an editable PO
        editable_po = PurchaseOrder.objects.create(
            po_number="PO-002",
            team=self.team,
            supplier=self.supplier,
            is_po_editable=True,
            order_status=PurchaseOrderStatus.ISSUED.value,
            source=SourceChoices.DIDERO.value,
        )

        # Regular user should only see the editable PO
        response = self.client.get("/api/orders")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["po_number"], "PO-002")

    def test_ai_user_can_see_all_pos(self):
        """Test that the Didero AI user can see all POs"""
        # Create a non-editable PO (pending AI validation)
        non_editable_po = PurchaseOrder.objects.create(
            po_number="PO-001",
            team=self.team,
            supplier=self.supplier,
            is_po_editable=False,
            order_status=PurchaseOrderStatus.DRAFT.value,
            source=SourceChoices.EMAIL.value,
        )

        # Create an editable PO
        editable_po = PurchaseOrder.objects.create(
            po_number="PO-002",
            team=self.team,
            supplier=self.supplier,
            is_po_editable=True,
            order_status=PurchaseOrderStatus.ISSUED.value,
            source=SourceChoices.DIDERO.value,
        )

        # AI user should see both POs
        response = self.ai_client.get("/api/orders")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["count"], 2)

        po_numbers = {po["po_number"] for po in response.data["results"]}
        self.assertEqual(po_numbers, {"PO-001", "PO-002"})

    def test_count_endpoint_filters_correctly(self):
        """Test that the count endpoint also filters non-editable POs"""
        # Create a non-editable PO
        PurchaseOrder.objects.create(
            po_number="PO-001",
            team=self.team,
            supplier=self.supplier,
            is_po_editable=False,
            order_status=PurchaseOrderStatus.DRAFT.value,
            source=SourceChoices.EMAIL.value,
        )

        # Create two editable POs
        for i in range(2, 4):
            PurchaseOrder.objects.create(
                po_number=f"PO-00{i}",
                team=self.team,
                supplier=self.supplier,
                is_po_editable=True,
                order_status=PurchaseOrderStatus.ISSUED.value,
                source=SourceChoices.DIDERO.value,
            )

        # Regular user count should be 2
        response = self.client.get("/api/orders/count")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["count"], 2)

        # AI user count should be 3
        response = self.ai_client.get("/api/orders/count")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["count"], 3)
