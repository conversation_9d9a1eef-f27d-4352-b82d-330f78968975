import json

from django.urls import reverse

from didero.orders.models import Shipment
from didero.orders.schemas import PurchaseOrderStatus, ShipmentStatus
from didero.testing.cases import TestCase


class TestShipments(TestCase):
    def setUp(self):
        super().setUp()

        user = self.create_user(
            email="<EMAIL>", first_name="<PERSON>", last_name="<PERSON><PERSON><PERSON>"
        )
        self.auth_as_user(user)
        team = user.teams.first()
        supplier = self.create_supplier(team=team, pk=1, name="Acme")
        item_1 = self.create_item(
            supplier=supplier, team_id=team.id, item_number="item1", price=100
        )
        item_2 = self.create_item(
            supplier=supplier, team_id=team.id, item_number="item2", price=100
        )

        self.order = self.create_purchase_order(
            supplier=supplier,
            team=team,
            placed_by=user,
            po_number="PO-1234",
        )

        self.order_item_1 = self.create_order_item(
            purchase_order=self.order, item=item_1, quantity=10
        )
        self.order_item_2 = self.create_order_item(
            purchase_order=self.order, item=item_2, quantity=10
        )

    def test_partially_shipped(self):
        url = reverse("order-shipments", args=[self.order.id])

        data = [
            {
                "carrier": "USPS",
                "status": "shipped",
                "line_items": [
                    {
                        "order_item_id": self.order_item_1.pk,
                        "shipped_quantity": 1,
                    }
                ],
            }
        ]

        self.client.post(url, json.dumps(data), content_type="application/json")
        shipment = Shipment.objects.all()[0]
        self.assertEqual(shipment.status, ShipmentStatus.SHIPPED.value)
        self.order.refresh_from_db()
        self.assertEqual(
            self.order.order_status, PurchaseOrderStatus.PARTIALLY_SHIPPED.value
        )

    def test_all_received(self):
        url = reverse("order-shipments", args=[self.order.id])

        data = [
            {
                "carrier": "USPS",
                "status": "received",
                "line_items": [
                    {
                        "order_item_id": self.order_item_1.pk,
                        "shipped_quantity": 10,
                    }
                ],
            },
            {
                "carrier": "Fedex",
                "status": "received",
                "line_items": [
                    {
                        "order_item_id": self.order_item_2.pk,
                        "shipped_quantity": 10,
                    }
                ],
            },
        ]

        self.client.post(url, json.dumps(data), content_type="application/json")
        shipment = Shipment.objects.all()[0]
        self.assertEqual(shipment.status, ShipmentStatus.RECEIVED.value)
        self.order.refresh_from_db()
        self.assertEqual(self.order.order_status, PurchaseOrderStatus.RECEIVED.value)

    def test_update_individual_shipment(self):
        # First create some shipments - make sure all items are fully shipped
        url = reverse("order-shipments", args=[self.order.id])
        data = [
            {
                "carrier": "USPS",
                "status": "shipped",
                "line_items": [
                    {
                        "order_item_id": self.order_item_1.pk,
                        "shipped_quantity": 10,  # Fully ship
                    }
                ],
            },
            {
                "carrier": "Fedex",
                "status": "awaiting_shipment",
                "line_items": [
                    {
                        "order_item_id": self.order_item_2.pk,
                        "shipped_quantity": 10,  # Fully ship
                    }
                ],
            },
        ]
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(response.status_code, 200)

        # Order should be partially shipped
        self.order.refresh_from_db()
        self.assertEqual(
            self.order.order_status, PurchaseOrderStatus.PARTIALLY_SHIPPED.value
        )

        # Now update one of the shipments to be received
        shipment = Shipment.objects.filter(carrier="USPS").first()
        self.assertIsNotNone(shipment)

        update_url = reverse("update-shipment", args=[shipment.id])
        update_data = {"status": "received"}
        response = self.client.patch(
            update_url, json.dumps(update_data), content_type="application/json"
        )
        self.assertEqual(response.status_code, 200)

        # Order should now be partially received
        self.order.refresh_from_db()
        self.assertEqual(
            self.order.order_status, PurchaseOrderStatus.PARTIALLY_RECEIVED.value
        )

        # Update the second shipment to be received
        shipment2 = Shipment.objects.filter(carrier="Fedex").first()
        self.assertIsNotNone(shipment2)

        update_url = reverse("update-shipment", args=[shipment2.id])
        update_data = {"status": "received"}
        response = self.client.patch(
            update_url, json.dumps(update_data), content_type="application/json"
        )
        self.assertEqual(response.status_code, 200)

        # Order should now be fully received
        self.order.refresh_from_db()
        self.assertEqual(self.order.order_status, PurchaseOrderStatus.RECEIVED.value)

    def test_delete_shipment_updates_order_status(self):
        # Create two shipments, both in RECEIVED status
        url = reverse("order-shipments", args=[self.order.id])
        data = [
            {
                "carrier": "USPS",
                "status": "received",
                "line_items": [
                    {
                        "order_item_id": self.order_item_1.pk,
                        "shipped_quantity": 10,
                    }
                ],
            },
            {
                "carrier": "Fedex",
                "status": "received",
                "line_items": [
                    {
                        "order_item_id": self.order_item_2.pk,
                        "shipped_quantity": 10,
                    }
                ],
            },
        ]
        self.client.post(url, json.dumps(data), content_type="application/json")

        # Order should be RECEIVED
        self.order.refresh_from_db()
        self.assertEqual(self.order.order_status, PurchaseOrderStatus.RECEIVED.value)

        # Delete one of the shipments - order should remain RECEIVED if at least one item has a RECEIVED shipment
        shipment = Shipment.objects.filter(carrier="USPS").first()
        self.assertIsNotNone(shipment)
        shipment.delete()

        # Order should still be RECEIVED as second shipment is still RECEIVED
        self.order.refresh_from_db()
        self.assertEqual(
            self.order.order_status, PurchaseOrderStatus.PARTIALLY_RECEIVED.value
        )

        # Delete all shipments
        Shipment.objects.all().delete()

        # Order status should be updated appropriately (will remain as is since there are no shipments)
        self.order.refresh_from_db()
        # Since there are no shipments, the PO should keep its previous status
        # The current implementation doesn't change the status when all shipments are deleted
