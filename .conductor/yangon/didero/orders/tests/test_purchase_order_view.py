from django.urls import reverse
from django.utils import timezone
from djmoney.money import Money

from didero.orders.models import PurchaseOrder
from didero.orders.schemas import PurchaseOrderStatus
from didero.testing.cases import TestCase


class TestPurchaseOrderModel(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(
            email="<EMAIL>", first_name="Bam", last_name="<PERSON>ebay<PERSON>"
        )
        self.auth_as_user(self.user)

        team = self.user.teams.first()
        self.team = team
        self.team_id = team.id
        self.supplier = self.create_supplier(team=team, pk=1)
        self.item_1 = self.create_item(
            supplier=self.supplier,
            price=Money(1, "USD"),
            item_number="TEST-1",
            team_id=self.team_id,
        )
        self.item_2 = self.create_item(
            supplier=self.supplier,
            price=Money(1, "USD"),
            item_number="TEST-2",
            team_id=self.team_id,
        )
        self.item_3 = self.create_item(
            supplier=self.supplier,
            price=Money(0.255, "USD"),
            item_number="TEST-MORE-PRECISE",
            team_id=self.team_id,
        )
        self.data = {
            "poNumber": "PO-test456456456kjndanmasdmndasmnasdmn",
            "supplierId": 1,
            "requestedShipDate": "2024-06-13",
            "placedBy": self.user.pk,
            "items": [
                {
                    "id": 1,
                    "itemId": self.item_1.pk,
                    "price": 3,
                    "quantity": 10,
                    "priceCurrency": "EUR",
                    "teamId": self.team_id,
                }
            ],
            "shippingAddress": {
                "id": 78,
                "line1": "saasdas",
                "line2": "",
                "city": "Jersey City",
                "stateOrProvince": "New Jersey",
                "postalCode": "07203",
                "country": "US",
                "phoneNumber": "",
                "isDefault": False,
                "team": self.team_id,
            },
            "senderAddress": {
                "id": 77,
                "line1": "formpo address",
                "line2": "asd",
                "city": "aklmasdkmladklm",
                "stateOrProvince": "klmadklmadklm",
                "postalCode": "12321",
                "country": "US",
                "phoneNumber": "",
                "isDefault": False,
                "team": self.team_id,
            },
            "carrier": "DHL",
        }

    def test_create_purchase_order_set_total_cost(self):
        url = reverse("purchase-order-list")
        response = self.client.post(url, self.data, format="json").json()
        self.assertEqual(response["totalCost"], "30.00")
        self.assertEqual(response["totalCostCurrency"], "EUR")

    def test_create_purchase_order_set_total_cost_2_items(self):
        self.data["items"].append(
            {
                "id": 2,
                "itemId": self.item_2.pk,
                "price": 2,
                "quantity": 10,
                "priceCurrency": "EUR",
            }
        )
        url = reverse("purchase-order-list")
        response = self.client.post(url, self.data, format="json").json()
        self.assertEqual(response["totalCost"], "50.00")
        self.assertEqual(response["totalCostCurrency"], "EUR")

    # def test_create_purchase_order_with_carrier_and_tracking_number(self):
    #     url = reverse("purchase-order-list")
    #     response = self.client.post(url, self.data, format="json")
    #     self.assertEqual(response.status_code, 201)
    #     self.assertEqual(response.json()["carrier"], "DHL")
    #     self.assertIsNone(response.json()["trackingNumber"])

    #     # Update the purchase order to set the tracking number
    #     update_url = reverse("purchase-order-detail", args=[response.json()["id"]])
    #     update_data = {"trackingNumber": "DHL-1234"}
    #     update_response = self.client.put(update_url, update_data, format="json")
    #     self.assertEqual(update_response.status_code, 200)
    #     self.assertEqual(update_response.json()["trackingNumber"], "DHL-1234")

    def test_purchase_order_serializer_price_precision_handles_more_precise_prices(
        self,
    ):
        url = reverse("purchase-order-list")
        self.data["items"] = [
            {
                "id": 3,
                "itemId": self.item_3.pk,
                "price": 0.25555555,
                "quantity": 10,
                "priceCurrency": "USD",
            }
        ]
        response = self.client.post(url, self.data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()["items"][0]["price"], 0.25555555)
        self.assertEqual(response.json()["totalCost"], "2.56")
        self.assertEqual(response.json()["totalCostCurrency"], "USD")

    # If try to delete draft order, should be deleted
    def test_delete_draft_order(self):
        order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
            order_status=PurchaseOrderStatus.DRAFT,
        )
        order_id = order.pk
        url = reverse("purchase-order-detail", args=[order.pk])
        response = self.client.delete(url, format="json")
        self.assertEqual(response.status_code, 200)
        # confirm the order was deleted
        self.assertEqual(PurchaseOrder.objects.filter(pk=order_id).count(), 0)

    # If try to delete a non-draft and non-terminal-state order, status should move to canceled,
    # and should be archived
    def test_archive_in_flight_order(self):
        order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )
        order.order_status = PurchaseOrderStatus.APPROVED
        order.save()
        url = reverse("purchase-order-detail", args=[order.pk])
        response = self.client.delete(url, format="json")
        self.assertEqual(response.status_code, 200)
        refreshed_order = PurchaseOrder.objects.get(pk=order.pk)
        self.assertEqual(refreshed_order.order_status, PurchaseOrderStatus.CANCELED)
        self.assertIsNotNone(refreshed_order.archived_at)

    # However, if try to delete terminal-state order, don't change status,
    # just archive
    def test_archive_terminal_state_order(self):
        order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )
        order.order_status = PurchaseOrderStatus.INVOICE_MATCHED
        order.save()
        url = reverse("purchase-order-detail", args=[order.pk])
        response = self.client.delete(url, format="json")
        self.assertEqual(response.status_code, 200)
        refreshed_order = PurchaseOrder.objects.get(pk=order.pk)
        self.assertEqual(
            refreshed_order.order_status, PurchaseOrderStatus.INVOICE_MATCHED
        )
        self.assertIsNotNone(refreshed_order.archived_at)

    # Should be able to unarchive an order no matter the status
    # (although the status should be CANCELED).
    def test_unarchive_order(self):
        order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
            archived_at=timezone.now(),
        )
        # order.archived_at = timezone.now()
        # order.save()
        url = reverse("purchase-order-detail", args=[order.pk])
        response = self.client.put(url, {"archived_at": None}, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["archivedAt"], None)
        refreshed_order = PurchaseOrder.objects.get(pk=order.pk)
        self.assertEqual(refreshed_order.archived_at, None)

    def test_purchase_order_creation_allow_blank_po(self):
        url = reverse("purchase-order-list")
        response = self.client.post(
            url, {"source": "external_po_import"}, format="json"
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()["source"], "external_po_import")

    def test_create_blank_po_and_and_update_after(self):
        url = reverse("purchase-order-list")
        response_post = self.client.post(
            url, {"source": "external_po_import"}, format="json"
        )

        order_id = response_post.json()["id"]
        put_url = reverse("purchase-order-detail", args=[order_id])
        response = self.client.put(put_url, self.data, format="json")

        self.assertEqual(response.status_code, 200)
