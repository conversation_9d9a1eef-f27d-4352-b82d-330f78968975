from decimal import Decimal

from currency_symbols import CurrencySymbols

from didero.orders.models import PurchaseOrder
from didero.orders.schemas import PurchaseOrderStatus
from didero.orders.utils.order_util import OrderHelper, money_to_string
from didero.testing.cases import TestCase

AUTO_ARCHIVE_ORDER_STATUS = [
    PurchaseOrderStatus.RECEIVED,
    PurchaseOrderStatus.SUPPLIER_REJECTED,
    PurchaseOrderStatus.CANCELED,
]


class TestPurchaseHelper(TestCase):
    def setUp(self):
        self.user = self.create_user(
            email="<EMAIL>", first_name="<PERSON><PERSON>", last_name="Adebayo"
        )
        self.team = self.user.teams.first()

        self.supplier = self.create_supplier(team=self.team)
        self.order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )

    def testArchivePOsForTeamThresholdReached(self):
        for status in PurchaseOrderStatus:
            # set the order to received; can't do this
            # on creation so we need to update the po here
            self.order.order_status = status
            self.order.save()

            # unfortunately, we cannot override the modified_at field
            # since it gets set by a db trigger; therefore we will set
            # TEAM_AUTO_ARCHIVE_AFTER_DAYS to 0 to simulate the functionality
            OrderHelper.archive_pos_for_team(self.team, 0)

            # get the PO again and verify it was archived for the order statuses that should
            archived_order = PurchaseOrder.objects.filter(pk=self.order.pk).get()
            if status in AUTO_ARCHIVE_ORDER_STATUS:
                self.assertIsNotNone(archived_order.archived_at)
            else:
                self.assertIsNone(archived_order.archived_at)

    def testArchivePOsForTeamThresholdNotReached(self):
        for status in PurchaseOrderStatus:
            # set the order to received; can't do this
            # on creation so we need to update the po here
            self.order.order_status = status
            self.order.save()

            # unfortunately, we cannot override the modified_at field
            # since it gets set by a db trigger; therefore we will set
            # TEAM_AUTO_ARCHIVE_AFTER_DAYS to 1 to simulate the functionality
            # we should get NO archived POs
            OrderHelper.archive_pos_for_team(self.team, 1)

            # get the PO again and verify it was NOT archived
            archived_order = PurchaseOrder.objects.filter(pk=self.order.pk).get()
            self.assertIsNone(archived_order.archived_at)

    def test_get_po_items_for_email(self):
        self.team = self.user.teams.first()
        self.supplier = self.create_supplier(team=self.team)
        self.order.delete()
        self.order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )

        item1 = self.create_item(
            supplier=self.supplier,
            team_id=self.team.id,
            description="Item 1",
            price=0.0789,
            item_number="t-123",
        )
        item2 = self.create_item(
            supplier=self.supplier,
            team_id=self.team.id,
            description="Item 2",
            price=0.0789,
            item_number="t-1234",
        )

        # Create items with specific quantities and prices
        order_item_1 = self.create_order_item(
            purchase_order=self.order,
            description="Item 1",
            item=item1,
            price=0.0789,
            quantity=1.359,
            # price_currency="TRY",
        )
        order_item_2 = self.create_order_item(
            purchase_order=self.order,
            description="Item 2",
            item=item2,
            price=0.0789,
            quantity=1.359,
            # price_currency="TRY",
        )

        po_items = OrderHelper.get_po_items_for_email(self.order)

        # Check if the total cost is correct and rounded to 2 decimal places
        expected_total_price = round(
            (
                Decimal(order_item_1.price.amount) * Decimal(order_item_1.quantity)
                + Decimal(order_item_2.price.amount) * Decimal(order_item_2.quantity)
            ),
            2,
        )
        self.assertEqual(
            po_items["order_total_price"],
            money_to_string(
                expected_total_price,
                CurrencySymbols.get_symbol(self.order.get_total_cost_currency()),
            ),
        )

        # Check if individual item prices are correct and rounded to 2 decimal places
        for order_item in [order_item_1, order_item_2]:
            item_total_price = round(
                Decimal(order_item.price.amount) * Decimal(order_item.quantity), 2
            )
            self.assertEqual(
                po_items["po_items"][order_item.item.description]["item_total_price"],
                money_to_string(
                    item_total_price,
                    CurrencySymbols.get_symbol(order_item.price.currency.code),
                ),
            )
