from enum import Enum, StrEnum
from typing import List, Optional

from pydantic import BaseModel, Field

from didero.addresses.schemas import AddressSchema


class PurchaseOrderStatus(str, Enum):
    """
    Adding a new status? Please make sure to:
        1. add it to order_status_int in the order view queryset, for custom ordering!
            The current order below follows the presumed flow - for any status below,
            all of its possible previous/parents statuses should be above it
        2. Add a new PO_STATUS_UPDATE_* tasktype for it, in base_task_types.py
        3. Add its kwargs to NEW_STATUSES_TO_TASK_TYPES in the PurchaseOrder
            model's _after_state_change()
    """

    DRAFT = "draft"
    APPROVAL_PENDING = "approval_pending"
    APPROVAL_DENIED = "approval_denied"
    APPROVED = "approved"
    ISSUED = "issued"
    PENDING_ACCEPTANCE = "pending_acceptance"  # this means supplier acknowledged receipt of PO, but has not yet accepted it
    OA_MISMATCH = "oa_mismatch"  # order acknowledgement received but has mismatches requiring review
    AWAITING_SHIPMENT = "awaiting_shipment"
    READY_FOR_PICKUP = "ready_for_pickup"
    SUPPLIER_REJECTED = "supplier_rejected"
    SHIPMENT_DELAYED = "shipment_delayed"
    SHIPPED = "shipped"
    PARTIALLY_SHIPPED = "partially_shipped"
    DELIVERY_DELAYED = "delivery_delayed"
    RECEIVED = "received"
    PARTIALLY_RECEIVED = "partially_received"
    CANCELED = "canceled"
    INVOICE_MATCHED = "invoice_matched"
    ISSUE = "issue"

    def human_readable(self):
        return self.value.replace("_", " ").title()


class ShipmentStatus(str, Enum):
    AWAITING_SHIPMENT = "awaiting_shipment"
    READY_FOR_PICKUP = "ready_for_pickup"
    SHIPPED = "shipped"
    RECEIVED = "received"
    CANCELED = "canceled"


# As we support more carriers, we can add more types here.
class CarrierType(StrEnum):
    FEDEX = "fedex"
    ZIM = "zim"
    ONE = "one"
    MSC = "msc"
    COSCO = "cosco"
    EVERGREEN = "evergreen"
    UNKNOWN = "unknown"


class ShipmentSyncStatus(str, Enum):
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"


# statuses wherein the PO information can still be edited by user
STATUSES_EDITABLE = [PurchaseOrderStatus.DRAFT, PurchaseOrderStatus.APPROVAL_DENIED]

STATUSES_PRE_APPROVAL = [
    PurchaseOrderStatus.DRAFT,
    PurchaseOrderStatus.APPROVAL_PENDING,
    PurchaseOrderStatus.APPROVAL_DENIED,
]

STATUSES_POST_APPROVAL = [
    PurchaseOrderStatus.APPROVED,
    PurchaseOrderStatus.PENDING_ACCEPTANCE,
    PurchaseOrderStatus.ISSUED,
    PurchaseOrderStatus.AWAITING_SHIPMENT,
    PurchaseOrderStatus.READY_FOR_PICKUP,
    PurchaseOrderStatus.SUPPLIER_REJECTED,
    PurchaseOrderStatus.SHIPMENT_DELAYED,
    PurchaseOrderStatus.SHIPPED,
    PurchaseOrderStatus.PARTIALLY_SHIPPED,
    PurchaseOrderStatus.DELIVERY_DELAYED,
    PurchaseOrderStatus.RECEIVED,
    PurchaseOrderStatus.PARTIALLY_RECEIVED,
    PurchaseOrderStatus.CANCELED,
    PurchaseOrderStatus.INVOICE_MATCHED,
    PurchaseOrderStatus.ISSUE,
]

ORDER_STATUSES_TERMINAL = [
    PurchaseOrderStatus.RECEIVED,
    PurchaseOrderStatus.INVOICE_MATCHED,
]

STATUSES_USER_SETTABLE = STATUSES_POST_APPROVAL + [PurchaseOrderStatus.DRAFT]


class PurchaseOrderTrigger(str, Enum):
    SUBMIT_FOR_APPROVAL = "submit_for_approval"
    APPROVE_BY_ALL = "approve_by_all"
    DENY_APPROVAL = "deny_approval"
    SEND_TO_SUPPLIER = "send_to_supplier"
    REVERT_TO_DRAFT = "revert_to_draft"
    MARK_AS_RECEIVED = "mark_as_received"


ORDER_STATUS_TRANSITIONS = [
    {
        "trigger": PurchaseOrderTrigger.SUBMIT_FOR_APPROVAL.value,
        "source": PurchaseOrderStatus.DRAFT.value,
        "dest": PurchaseOrderStatus.APPROVAL_PENDING.value,
        "after": "trigger_approval_flows",
    },
    {
        "trigger": PurchaseOrderTrigger.APPROVE_BY_ALL.value,
        "source": PurchaseOrderStatus.APPROVAL_PENDING.value,
        "dest": PurchaseOrderStatus.APPROVED.value,
    },
    {
        "trigger": PurchaseOrderTrigger.DENY_APPROVAL.value,
        "source": PurchaseOrderStatus.APPROVAL_PENDING.value,
        "dest": PurchaseOrderStatus.APPROVAL_DENIED.value,
    },
    {
        "trigger": PurchaseOrderTrigger.SEND_TO_SUPPLIER.value,
        "source": PurchaseOrderStatus.APPROVED.value,
        "dest": PurchaseOrderStatus.ISSUED.value,
    },
    {
        "trigger": PurchaseOrderTrigger.REVERT_TO_DRAFT.value,
        "source": [status.value for status in PurchaseOrderStatus],
        "dest": PurchaseOrderStatus.DRAFT.value,
        "after": "handle_revert_to_draft",
    },
    {
        "trigger": PurchaseOrderTrigger.MARK_AS_RECEIVED.value,
        "source": PurchaseOrderStatus.SHIPPED.value,
        "dest": PurchaseOrderStatus.RECEIVED.value,
    },
    # TODO: the rest of the transitions
]


# These will be statuses that can be reached by a supplier sending the user an email.
# The other statuses should not be able to be updated in this manner.
class UpdateablePurchaseOrderStatusFromIncomingEmail(str, Enum):
    # We transitioned "supplier_accepted" to "awaiting_shipment"
    # However, that's not very clear in an AI prompt
    # For now, we hard-code the old status
    # When we complete https://linear.app/d4o/issue/EPD-1564/targeted-openai-queries-based-on-status,
    # we can work on cleaning these statuses/schemas
    SUPPLIER_ACCEPTED = "supplier_accepted"
    SUPPLIER_REJECTED = PurchaseOrderStatus.SUPPLIER_REJECTED.value


# This is the response we ask OpenAI to send us back after processing an email response
class PurchaseOrderParsedResponse(BaseModel):
    po_number: Optional[str] = None
    updated_order_status: Optional[UpdateablePurchaseOrderStatusFromIncomingEmail] = (
        None
    )


class PaymentStatus(str, Enum):
    UNPAID = "unpaid"
    INVOICE_RECEIVED = "invoice_received"
    READY_TO_PAY = "ready_to_pay"
    PAID = "paid"


# TODO: fill this out
PAYMENT_STATUS_TRANSITIONS = [
    {
        "trigger": "pay",
        "source": PaymentStatus.READY_TO_PAY.value,
        "dest": PaymentStatus.PAID.value,
    }
]


class SourceChoices(str, Enum):
    EMAIL = "email"
    ERP = "erp"  # these are external POs from a direct integration with external ERP.
    EXTERNAL_PO_IMPORT = "external_po_import"  # PO created from an external import async. TODO: Change this as Quotes use this now too.
    DIDERO = "didero"
    NETSUITE_SYNC = "netsuite_sync"  # PO created or updated via NetSuite sync process


class DeliveryStatus(str, Enum):
    PARTIALLY_DELIVERED = "partially_delivered"
    DELIVERED = "delivered"
    ISSUE = "issue"


class LineItemCategoryChoices(str, Enum):
    TAX = "tax"
    DISCOUNT = "discount"
    SHIPPING = "shipping"
    HANDLING = "handling"
    FEE = "fee"
    REBATE = "rebate"
    SERVICE = "service"
    MATERIAL = "material"
    PROMOTION = "promotion"
    SURCHARGE = "surcharge"
    REFUND = "refund"
    CUSTOM = "custom"


# Used in the Order Acknowledgement Workflow


# TODO: Ideally we should use Decimal for the prices, but we need to use strings because temporal expect json serializable types.
# and Decimal is not json serializable. Tried using config_dict and json_encoders, but it didn't work


class OrderAcknowledgementOrderItem(BaseModel):
    item_number: str = Field(..., description="The unique identifier for the item")
    item_description: str = Field(..., description="The description of the item")
    quantity: float = Field(..., description="The quantity of the item ordered")
    unit_of_measure: str = Field(..., description="The unit of measure for the item")
    unit_price: str = Field(..., description="The price per unit of the item")
    total_price: str = Field(
        ..., description="The total price for the item (quantity * unit price)"
    )
    is_backorder: bool = Field(..., description="Indicates if the item is on backorder")

    # Follow-up workflow fields at item level for better granularity
    promised_ship_date: Optional[str] = Field(
        None,
        description="Ship date promised by supplier for this specific item (YYYY-MM-DD)",
    )
    promised_delivery_date: Optional[str] = Field(
        None,
        description="Delivery date promised by supplier for this specific item (YYYY-MM-DD)",
    )


# Decimals are not json serializable, so we needed to use strings.
# TODO: Investigate extracting price fields into cents(or smallest currency unit) to avoid precision issues and avoid using strings.
class OrderAcknowledgement(BaseModel):
    # Core identifiers
    po_number: str = Field(..., description="Customer's purchase order number")
    email_id: str = Field(..., description="The email ID of the acknowledgement email")

    # Company information
    shipping_address: AddressSchema = Field(
        ..., description="The address where the order will be shipped to"
    )

    # Order details
    order_number: str = Field(..., description="Supplier's order/sales order number")
    order_items: List[OrderAcknowledgementOrderItem] = Field(
        ..., description="List of items in the order acknowledgement"
    )

    # Global date fields for when dates apply to all items
    global_promised_ship_date: Optional[str] = Field(
        None,
        description="Ship date promised by supplier for ALL items when no item-specific dates are provided (YYYY-MM-DD)",
    )
    global_promised_delivery_date: Optional[str] = Field(
        None,
        description="Delivery date promised by supplier for ALL items when no item-specific dates are provided (YYYY-MM-DD)",
    )

    # Pricing information
    subtotal: str = Field(..., description="Subtotal amount of the order")
    tax_rate: Optional[float] = Field(
        None, description="Applicable tax rate for the order"
    )
    tax_amount: Optional[str] = Field(
        None, description="Total tax amount for the order"
    )

    total_amount: str = Field(
        ..., description="Total amount of the order including all charges"
    )
    shipping_or_freight_charges: Optional[str] = Field(
        None, description="Shipping or freight charges applicable to the order"
    )
    other_special_charges: Optional[str] = Field(
        None, description="Other special charges applicable to the order"
    )

    # Shipping and payment details
    shipping_method: Optional[str] = Field(
        None, description="Method of shipping used for the order"
    )
    shipping_terms: Optional[str] = Field(
        None, description="Terms of shipping, e.g., FOB Origin"
    )
    payment_terms: Optional[str] = Field(
        None, description="Terms of payment, e.g., Net 30, 2% 10 Net 30"
    )

    # Additional information
    notes: Optional[str] = Field(
        None, description="Additional notes related to the order"
    )
    special_instructions: Optional[str] = Field(
        None, description="Special instructions for handling the order"
    )

    # Follow-up workflow fields moved to item level for better granularity


class OrderItemSupplyOn(BaseModel):
    """Represents an individual line item in the order response."""

    position: int = Field(..., description="Position of the item in the order.")
    customer_material_number: str = Field(
        ..., description="Customer's material number."
    )
    supplier_material_number: str = Field(
        ..., description="Supplier's material number."
    )
    customer_material_description: str = Field(
        ..., description="Material description from the customer's side."
    )
    supplier_material_description: str = Field(
        ..., description="Material description from the supplier's side."
    )
    confirmed_quantity: float = Field(
        ..., description="Quantity confirmed in the order response."
    )
    unit_of_measure: str = Field(..., description="Unit of measure (e.g., PCE, KG).")
    confirmed_delivery_date: str = Field(
        ..., description="Confirmed delivery date for the item."
    )
    price_net_value: float = Field(..., description="Total net value of the item.")
    price_per_unit: float = Field(..., description="Unit price of the item.")
    price_unit: str = Field(
        ..., description="Price unit basis (e.g., per item, per 100 items)."
    )


class OrderAcknowledgementSupplyOn(OrderAcknowledgement):
    """
    SupplyOn-specific version of the OrderAcknowledgement schema.
    """

    supplier_address: AddressSchema = Field(
        ..., description="The address of the supplier"
    )
    order_items: List[OrderItemSupplyOn] = Field(  # pyright: ignore .
        # This is typing error is caused by order_items field with List[OrderAcknowledgementOrderItem] being
        # immutable in the base class. Tried several things (subclassing, generic type, etc.) to fix this,
        # but none fix the error and keep the functionality.
        ...,
        description="List of items in the order acknowledgement (SupplyOn format)",
    )


# Shipping terms constants based on Incoterms 2020
SHIPPING_TERMS_CHOICES = [
    # Most common terms for general use
    ("EXW", "Ex Works - Buyer picks up"),
    ("FCA", "Free Carrier - Deliver to carrier"),
    ("FOB", "Free on Board - Load on vessel"),
    ("DAP", "Delivered at Place - Seller delivers"),
    ("DDP", "Delivered Duty Paid - Full service"),
    ("PPA", "Prepaid and Allowed - Seller pays freight"),
    # Additional terms for comprehensive support
    ("CPT", "Carriage Paid To"),
    ("CIP", "Carriage and Insurance Paid To"),
    ("DPU", "Delivered at Place Unloaded"),
    ("FAS", "Free Alongside Ship"),
    ("CFR", "Cost and Freight"),
    ("CIF", "Cost Insurance and Freight"),
]

# Terms where the date represents when goods are ready for pickup
PICKUP_READY_TERMS = {"EXW", "FCA", "FAS"}

# Terms where the date represents when goods will be shipped/loaded
SHIP_DATE_TERMS = {"FOB"}

# Terms where the date represents delivery at destination
DELIVERY_DATE_TERMS = {"DAP", "DDP", "DPU", "CPT", "CIP", "CFR", "CIF", "PPA"}


def get_date_field_label(shipping_terms: str) -> str:
    """
    Returns the appropriate label for the date field based on shipping terms.
    Falls back to "Requested Date" for custom terms.
    """
    if not shipping_terms:
        return "Requested Date"

    # Check if it's a known standard term
    if shipping_terms in PICKUP_READY_TERMS:
        if shipping_terms == "EXW":
            return "Ready for Pickup Date"
        elif shipping_terms == "FCA":
            return "Ready for Carrier Date"
        elif shipping_terms == "FAS":
            return "Ready at Port Date"
    elif shipping_terms in SHIP_DATE_TERMS:
        return "Ship Date"
    elif shipping_terms in DELIVERY_DATE_TERMS:
        return "Delivery Date"

    # For custom terms, return a generic label
    return "Requested Date"


def get_date_help_text(shipping_terms: str) -> str:
    """
    Returns help text explaining what the date means for given shipping terms.
    Falls back to generic help text for custom terms.
    """
    if not shipping_terms:
        return "Date interpretation depends on shipping terms"

    help_texts = {
        "EXW": "Date when goods will be ready for pickup at seller's location",
        "FCA": "Date when goods will be ready for carrier at named place",
        "FAS": "Date when goods will be delivered alongside vessel at port",
        "FOB": "Date when goods will be loaded on vessel",
        "CPT": "Date when goods will arrive at destination (seller pays freight)",
        "CIP": "Date when goods will arrive at destination (seller pays freight + insurance)",
        "CFR": "Date when goods will arrive at destination port",
        "CIF": "Date when goods will arrive at destination port (with insurance)",
        "DAP": "Date when goods will be delivered to buyer's location",
        "DPU": "Date when goods will be delivered and unloaded at buyer's location",
        "DDP": "Date when goods will be delivered (including customs clearance)",
        "PPA": "Date when goods will be delivered (seller pays freight and allows costs)",
    }

    return help_texts.get(
        shipping_terms,
        "Date interpretation depends on specific shipping terms agreed with supplier",
    )
