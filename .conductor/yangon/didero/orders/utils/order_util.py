from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from random import randint
from typing import Optional

import structlog
from currency_symbols import CurrencySymbols
from django.apps import apps
from django.core.cache import cache
from django.http import HttpResponse
from django.utils import timezone
from rest_framework.exceptions import PermissionDenied

from didero.orders.schemas import PurchaseOrderStatus
from didero.users.models.team_models import Team
from didero.utils.utils import format_quantity

logger = structlog.get_logger(__name__)


PO_ACCESS_CACHE_PREFIX = "PO_ACCESS"
TEAM_PO_ACCESS_CACHE_PREFIX = "TEAM_PO_ACCESS"
CACHE_DURATION = 60 * 1


# Helper class for PurchaseOrder(s)
class OrderHelper(object):
    @staticmethod
    def get_po_items_for_email(po):
        # returns a map {'po_items': {'description':  {'price', 'quantity', 'item_total_price'}}, 'order_total_price': "$20"}
        po_currency_symbol = CurrencySymbols.get_symbol(po.get_total_cost_currency())
        item_data = {"po_items": {}, "order_total_price": f"{po_currency_symbol}0.00"}
        order_total_price = 0
        # go through items and extract the info we need
        order_items = po.items.all()
        for order_item in order_items:
            # add the item to the dict of results
            item_total_price = Decimal(order_item.price.amount) * Decimal(
                order_item.quantity
            )
            item_currency_symbol = CurrencySymbols.get_symbol(
                order_item.item.price.currency.code
            )
            item_data["po_items"][order_item.item.description] = {
                "description": order_item.item.description,
                "item_price": money_to_string(
                    order_item.price.amount, item_currency_symbol
                ),
                "item_quantity": format_quantity(order_item.quantity),
                "item_total_price": money_to_string(
                    item_total_price, item_currency_symbol
                ),
                "item_currency_symbol": item_currency_symbol,
            }
            # add the item's price to the po's total price
            order_total_price += item_total_price
        # add the order's total price string
        # using the last item's currency_symbol as the order's symbol; assuming all items use the same currency
        item_data["order_total_price"] = money_to_string(
            order_total_price, po_currency_symbol
        )
        return item_data

    @staticmethod
    def has_access_to_po(
        order, user_pk: str, user_team: Team
    ) -> tuple[bool, Optional[str]]:
        # Get the user model
        User = apps.get_model("users", "User")
        user = User.objects.filter(pk=user_pk).first()

        # Allow admin or superusers to access any PurchaseOrder
        if user and (user.is_staff or user.is_superuser):
            return True, None

        # check the cache to see if the po/user pair was validated recently (CACHE_DURATION)
        key = f"{PO_ACCESS_CACHE_PREFIX}_{user_pk}_{order.pk}"
        if cache.has_key(key):
            cached_data = cache.get(key)
            has_access = cached_data["has_access"]
            # if the user has no access, raise an error
            if not has_access:
                return False, cached_data["error_message"]
            # if the user has acess, return True
            return True, None

        # check if the user and order are from the same team
        if order.team != user_team:
            error_message = "Access Denied: The order does not belong to your team."
            save_po_user_access_to_cache(key, False, error_message)
            return False, error_message

        save_po_user_access_to_cache(key, True)
        return True, None

    @staticmethod
    def archive_pos_for_team(team, archive_after_days):
        # get and archive all matching POs
        # need to use apps.get_model to avoid circular dependencies
        pos = apps.get_model("orders", "PurchaseOrder").objects.filter(
            team=team,
            # look for orders that are RECEIVED, CANCELED or SUPPLIER_REJECTED
            order_status__in=[
                PurchaseOrderStatus.RECEIVED,
                PurchaseOrderStatus.CANCELED,
                PurchaseOrderStatus.SUPPLIER_REJECTED,
            ],
            # that haven't been updated in the the last archive_after_days
            modified_at__lt=timezone.now() - timedelta(days=archive_after_days),
        )

        logger.info(
            f"Archiving the {len(pos)} POs older than {archive_after_days} days for team {team.pk}."
        )
        pos.update(archived_at=timezone.now())


def money_to_string(decimal, currency_symbol="$"):
    # return a string version of the given number
    return f"{currency_symbol}{float(decimal):,.2f}"


def log_and_respond(message: str, error=False):
    if error:
        logger.error(message)
        return HttpResponse(message, status=400)
    logger.info(message)
    return HttpResponse(message)


def save_po_user_access_to_cache(key: str, has_access: bool, error_message: str = None):
    # save a map of the access status and the error code we should throw if the user has no access
    # Using cache.set instead of cache.add to ensure cache updates even if key exists
    cache.set(
        key, {"has_access": has_access, "error_message": error_message}, CACHE_DURATION
    )


# Don't add trailing slash to prefix passed.
def generate_random_po_number(prefix: str = None):
    rand_number = randint(1, 1_000_000)
    return f"PO-{rand_number}" if prefix is None else f"{prefix}-{rand_number}"
