import random
from datetime import date, datetime
from io import BytesIO

import structlog
from django.conf import settings
from jinja2 import Environment, FileSystemLoader
from pdfkit import from_string
from PyPDF2 import PdfReader, PdfWriter
from rest_framework.exceptions import NotFound

from didero.addresses.models import Address
from didero.suppliers.models import Supplier
from didero.users.models.team_models import Team
from didero.users.models.user_models import User
from didero.utils.utils import (
    format_currency_with_commas,
    format_number_string_with_commas,
    format_phone_number_string,
    format_quantity,
)

logger = structlog.get_logger()

PO_TO_PDF_TEMPLATE_NAME = "po_to_pdf_template.html"


def po_to_pdf(data):
    data = prepare_po_data_for_pdf(data)
    if "placed_by" not in data:
        placed_by_user = User.objects.filter(id=data["placed_by_id"]).get()
        data["placed_by"] = {
            "display_name": placed_by_user.display_name,
            "email": placed_by_user.email,
        }
    data["format_currency_with_commas"] = format_currency_with_commas
    data["format_number_string_with_commas"] = format_number_string_with_commas
    data["format_quantity"] = format_quantity
    data["format_phone_number_string"] = format_phone_number_string
    data["max"] = max
    env = Environment(loader=FileSystemLoader(settings.TEMPLATES[0]["DIRS"]))
    template = env.get_template(PO_TO_PDF_TEMPLATE_NAME)
    html_content = template.render(**data)

    options = {
        "page-size": "A4",
        "margin-top": "10mm",
        "margin-right": "10mm",
        "margin-bottom": "10mm",
        "margin-left": "10mm",
        "enable-local-file-access": "",
    }

    pdf = from_string(html_content, options=options)

    if not pdf:
        return None, "Error generating PDF"

    # Check if team has T&C to append
    team = Team.objects.filter(pk=data["team"]).first()
    if team and team.terms_and_conditions:
        try:
            # Create PDF writer and add PO pages
            writer = PdfWriter()
            po_reader = PdfReader(BytesIO(pdf))

            # Add all pages from the PO PDF
            for page in po_reader.pages:
                writer.add_page(page)

            # Add all pages from the T&C PDF
            tc_reader = PdfReader(team.terms_and_conditions.file)
            for page in tc_reader.pages:
                writer.add_page(page)

            # Write merged PDF to BytesIO
            output = BytesIO()
            writer.write(output)
            output.seek(0)
            return output, None
        except Exception:
            # If merging fails, just return the original PO PDF
            return BytesIO(pdf), None

    return BytesIO(pdf), None


def prepare_po_data_for_pdf(data):
    """
    Prepare purchase order data for PDF generation.
    """
    supplier_id = data.get("supplier_id")
    if not supplier_id:
        # Try to get supplier_id from nested supplier object
        supplier_data = data.get("supplier", {})
        if isinstance(supplier_data, dict):
            supplier_id = supplier_data.get("id")

    if not supplier_id:
        raise NotFound("No supplier ID found in data")

    team = Team.objects.filter(pk=data["team"]).first()
    supplier = Supplier.objects.filter(id=supplier_id, team=team).first()

    if not supplier:
        raise NotFound("Your team has no matching supplier")

    data["supplier_name"] = supplier.name
    logger.info(
        "prepare_po_data_for_pdf",
        supplier_id=supplier_id,
        supplier_name=supplier.name,
        has_sender_address=bool(data.get("sender_address")),
    )

    items = data.get("items", [])
    total_amount = 0
    processed_items = []

    for item in items:
        item_data = item.get("item", {})
        quantity = float(item.get("quantity", "0"))
        price = float(item.get("price", "0"))
        unit_of_measure = item.get("unit_of_measure", "Each")
        # Use new requested_date field
        requested_date = item.get("requested_date", "")
        item_total = quantity * price
        total_amount += item_total

        processed_items.append(
            {
                "id": item_data.get("id"),
                "description": item_data.get("description"),
                "item_number": item_data.get("item_number"),
                "price": price,
                "price_currency": item_data.get("price_currency"),
                "quantity": quantity,
                "item_total": item_total,
                "unit_of_measure": unit_of_measure,
                "requested_date": requested_date,
            }
        )

    total_amount = round(total_amount, 2)
    try:
        order_date = datetime.strptime(
            data.get("placement_time", date.today().isoformat()),
            "%Y-%m-%dT%H:%M:%S.%fZ",
        ).strftime("%B %d, %Y")
    except ValueError:
        # default to today's date if parsing fails
        order_date = date.today().strftime("%B %d, %Y")

    data.update(
        {
            "total_amount": total_amount,
            "items": processed_items,
            "po_number": data.get(
                "po_number", f"PO-{random.randint(1, 1_000_000_000_000)}"
            ),
            "order_date": order_date,
            "company_logo": team.logo.url if team.logo else None,
            "legal_disclaimer": team.legal_disclaimer,
        }
    )

    # Handle new unified requested_date field
    if "requested_date" in data and data["requested_date"] is not None:
        date_obj = datetime.strptime(data["requested_date"], "%Y-%m-%d")
        # Use appropriate label based on shipping terms
        from didero.orders.schemas import get_date_field_label

        shipping_terms = data.get("shipping_terms")
        data["date_field_label"] = get_date_field_label(shipping_terms)

        if data.get("shipping_terms") in [
            "DAP",
            "DDP",
            "DAT",
            "CIP",
            "CPT",
            "CFR",
            "CIF",
        ]:
            data["readable_delivery_date"] = date_obj.strftime("%B %d, %Y")
        else:
            data["readable_ship_date"] = date_obj.strftime("%B %d, %Y")
        data["requested_date"] = date_obj.strftime("%Y-%m-%d")

    # If the team has a default address, get it
    # otherwise, use the given shipping_address
    team_default_address = Address.objects.filter(
        team=team, supplier=None, is_default=True
    ).first()
    if team_default_address:
        data["team_address"] = team_default_address
    else:
        data["team_address"] = data["shipping_address"]

    if team.default_po_email_credential:
        data["default_po_email_address"] = (
            team.default_po_email_credential.email_address
        )
        data["default_po_name"] = team.name

    # If sender_address is not provided or is empty, try to get supplier's default address
    if not data.get("sender_address"):
        supplier_default_address = Address.objects.filter(
            team=team, supplier=supplier, is_default=True
        ).first()
        if supplier_default_address:
            from didero.addresses.serializers import AddressSerializer

            data["sender_address"] = AddressSerializer(supplier_default_address).data
            logger.info(
                "Using supplier default address", address_id=supplier_default_address.id
            )
        else:
            # If no default address, try to get any address for the supplier
            supplier_address = Address.objects.filter(
                team=team, supplier=supplier
            ).first()
            if supplier_address:
                from didero.addresses.serializers import AddressSerializer

                data["sender_address"] = AddressSerializer(supplier_address).data
                logger.info(
                    "Using first available supplier address",
                    address_id=supplier_address.id,
                )
            else:
                logger.warning("No supplier address found", supplier_id=supplier.id)

    return data
