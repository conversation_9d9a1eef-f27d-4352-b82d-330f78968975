from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.urls import reverse
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode

from didero.users.models.user_models import User

URL_ENCODE_DELIMITER = "---"


class OrderApprovalsHelper:
    def get_next_pending_approval_for_po(po):
        from didero.orders.models import OrderApproval

        # get the next approval for a pending PO
        return (
            OrderApproval.objects.filter(purchase_order=po.pk, approved_at=None)
            .order_by("-approver_number")
            .first()
        )

    # PO approval helper methods
    def generate_approval_url(po, approver: User, decision: bool):
        user_token = default_token_generator.make_token(approver)
        # didero.ai/api/orders/approve?token={}
        endpoint = (
            reverse("purchase-order-approve")
            if decision
            else reverse("purchase-order-reject")
        )
        # generate token of the form "{approver.pk}---{po.pk}---{django_user_token}"
        url_token = urlsafe_base64_encode(
            force_bytes(
                f"{approver.pk}{URL_ENCODE_DELIMITER}{po.pk}{URL_ENCODE_DELIMITER}{user_token}"
            )
        )
        return f"{settings.API_URL}{endpoint}?token={url_token}"
