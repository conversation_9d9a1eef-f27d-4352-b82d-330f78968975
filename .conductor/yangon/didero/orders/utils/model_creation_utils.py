"""
Order-specific utilities for creating Django models from AI-extracted data.
Contains date parsing and address handling utilities specific to order processing.
"""

from datetime import datetime
from typing import Optional

import structlog

from didero.addresses.models import Address
from didero.addresses.schemas import AddressSchema
from didero.users.models import Team

logger = structlog.get_logger(__name__)


def parse_date_string(date_str: str) -> Optional[datetime.date]:
    """
    Parse date string to date object, handling various formats.

    Args:
        date_str: Date string in various formats

    Returns:
        Parsed date or None if parsing fails
    """
    if not date_str:
        return None

    # Common date formats to try
    formats = [
        "%Y-%m-%d",  # 2024-06-15
        "%m/%d/%Y",  # 06/15/2024
        "%m-%d-%Y",  # 06-15-2024
        "%d/%m/%Y",  # 15/06/2024
        "%d-%m-%Y",  # 15-06-2024
        "%B %d, %Y",  # June 15, 2024
        "%b %d, %Y",  # Jun 15, 2024
        "%d %B %Y",  # 15 June 2024
        "%d %b %Y",  # 15 Jun 2024
    ]

    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue

    logger.warning(f"Could not parse date string: {date_str}")
    return None


def handle_address_field(
    pydantic_address: Optional[AddressSchema],
    fallback_address: Optional[Address],
    team: Team,
) -> Optional[Address]:
    """
    Handle address field creation with fallback logic.

    Args:
        pydantic_address: Address from Pydantic extraction
        fallback_address: Fallback address (e.g., from PO)
        team: Team for creating new addresses

    Returns:
        Address instance or None
    """
    # Check if pydantic address has meaningful data
    if pydantic_address and _has_valid_address_data(pydantic_address):
        return _create_address_from_schema(pydantic_address, team)

    # Use fallback address if available
    if fallback_address:
        logger.debug("Using fallback address")
        return fallback_address

    return None


def _has_valid_address_data(address: AddressSchema) -> bool:
    """Check if address schema has meaningful data"""
    return bool(
        address.line_1
        or address.city
        or address.postal_code
        or address.state_or_province
    )


def _create_address_from_schema(address_schema: AddressSchema, team: Team) -> Address:
    """Create Address model from Pydantic schema"""
    return Address.objects.create(
        team=team,
        line_1=address_schema.line_1 or "",
        line_2=address_schema.line_2 or "",
        city=address_schema.city or "",
        state_or_province=address_schema.state_or_province or "",
        postal_code=address_schema.postal_code or "",
        country=address_schema.country or "",
    )
