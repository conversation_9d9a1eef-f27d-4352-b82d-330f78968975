"""
OrderAcknowledgement creation utilities - minimal approach.
Only saves AI extracted fields, particularly promised ship/delivery dates for follow-up workflows.
"""

from typing import Any, Optional

import structlog

from didero.documents.models import DocumentItemMatch
from didero.documents.schemas import DocumentType
from didero.models import create_model_from_extraction
from didero.orders.models import OrderAcknowledgement, PurchaseOrder
from didero.orders.schemas import OrderAcknowledgement as OrderAcknowledgementSchema
from didero.orders.utils.model_creation_utils import handle_address_field
from didero.suppliers.models import Communication

logger = structlog.get_logger(__name__)


def link_oa_to_email_and_documents(
    oa: OrderAcknowledgement,
    source_email: Communication,
) -> None:
    """
    Link OrderAcknowledgement to its source email thread and any associated documents.

    Args:
        oa: The OrderAcknowledgement instance to link
        source_email: The source Communication (email) that contained the OA
    """
    # Link the email thread to the order acknowledgement
    from didero.emails.models import EmailThreadToOrderAcknowledgementLink

    if source_email.email_thread:
        EmailThreadToOrderAcknowledgementLink.objects.get_or_create(
            email_thread=source_email.email_thread,
            order_acknowledgement=oa,
        )
        logger.debug(
            "Linked email thread to OrderAcknowledgement",
            oa_id=oa.pk,
            email_thread_id=source_email.email_thread.id,
            email_id=source_email.pk,
        )

    # Link any documents from the email to the order acknowledgement
    from django.contrib.contenttypes.models import ContentType

    from didero.documents.models import DocumentLink

    # Find documents linked to this email via DocumentLink
    comm_content_type = ContentType.objects.get_for_model(Communication)
    email_doc_links = DocumentLink.objects.filter(
        parent_object_type=comm_content_type, parent_object_id=str(source_email.id)
    )

    if email_doc_links.exists():
        oa_content_type = ContentType.objects.get_for_model(OrderAcknowledgement)
        documents_linked = 0

        for email_link in email_doc_links:
            doc = email_link.document
            link, created = DocumentLink.objects.get_or_create(
                document=doc,
                parent_object_type=oa_content_type,
                parent_object_id=str(oa.pk),
            )

            if created:
                documents_linked += 1
                logger.info(
                    "Linked document to OrderAcknowledgement",
                    oa_id=oa.pk,
                    document_id=doc.id,
                    document_name=doc.name,
                    document_type=doc.doc_type,
                    email_id=source_email.pk,
                )

        logger.info(
            "Linked documents from email to OrderAcknowledgement",
            oa_id=oa.pk,
            total_documents=email_doc_links.count(),
            new_links_created=documents_linked,
            email_id=source_email.pk,
        )


def create_oa_from_extraction(
    oa_data: OrderAcknowledgementSchema,
    purchase_order: PurchaseOrder,
    source_email: Communication,
    oa_status: Optional[str] = None,
) -> OrderAcknowledgement:
    """
    Create OrderAcknowledgement from AI extraction - minimal approach.
    Only saves AI extracted fields, particularly dates needed for follow-up workflows.

    Args:
        oa_data: Pydantic schema with extracted OA data
        purchase_order: PO to associate with the OA
        source_email: Source communication for linking
        oa_status: Optional status to override automatic determination

    Returns:
        Created OrderAcknowledgement instance with only AI extracted fields
    """
    # Define field mappings for AI extracted fields only
    field_mappings = {
        "order_number": "order_number",  # Supplier's order/sales order number
        "shipping_method": "shipping_method",
        "shipping_terms": "shipping_terms",
        "payment_terms": "payment_terms",
        "notes": "notes",
        "special_instructions": "special_instructions",
        "global_promised_ship_date": "global_promised_ship_date",
        "global_promised_delivery_date": "global_promised_delivery_date",
        # Note: po_number from oa_data is used for matching, not stored in OA model
        # Note: promised dates moved to item level for better granularity
    }

    # Handle shipping address only if AI extracted meaningful data
    shipping_address = None
    if oa_data.shipping_address and _has_valid_address_data(oa_data.shipping_address):
        try:
            shipping_address = handle_address_field(
                pydantic_address=oa_data.shipping_address,
                fallback_address=None,  # No fallback - only save if AI found it
                team=purchase_order.team,
            )
        except Exception as e:
            logger.warning(
                "Failed to create shipping address from AI extraction - skipping address",
                oa_po_number=oa_data.po_number,
                address_data=oa_data.shipping_address,
                error=str(e),
            )
            shipping_address = None

    # Use provided status or determine from AI extraction
    if oa_status is None:
        oa_status = _determine_oa_status(oa_data)

    # Create the OA with only AI extracted fields
    oa = create_model_from_extraction(
        pydantic_data=oa_data,
        django_model_class=OrderAcknowledgement,
        field_mappings=field_mappings,
        fallback_source=None,  # No fallbacks - only save what AI found
        purchase_order=purchase_order,
        shipping_address=shipping_address,
    )

    # Set the status after creation
    oa.oa_status = oa_status
    oa.save(update_fields=["oa_status"])

    # Create order items if present in AI extraction
    items_created = 0
    if hasattr(oa_data, "order_items") and oa_data.order_items:
        items_created = _create_oa_items(oa, oa_data.order_items)

    # Apply global dates to items that don't have specific dates
    if oa.global_promised_ship_date or oa.global_promised_delivery_date:
        _apply_global_dates_to_items(oa)

    # Create line items from special charges if present
    line_items_created = 0
    line_items_created = _create_oa_line_items_from_charges(oa, oa_data)

    # Link the OA to email thread and any documents
    try:
        link_oa_to_email_and_documents(oa, source_email)
    except Exception as e:
        logger.error(
            "Failed to link OA to email and documents",
            oa_id=oa.pk,
            email_id=source_email.pk,
            error=str(e),
            exc_info=True,
        )

    logger.info(
        "Created OrderAcknowledgement from AI extraction (minimal approach)",
        oa_id=oa.pk,
        po_id=purchase_order.pk,
        po_number=purchase_order.po_number,
        oa_status=oa_status,
        items_with_ship_dates=sum(
            1 for item in oa.items.all() if item.promised_ship_date
        ),
        items_with_delivery_dates=sum(
            1 for item in oa.items.all() if item.promised_delivery_date
        ),
        has_shipping_address=bool(oa.shipping_address),
        supplier_order_number=oa.order_number,
        extracted_po_number=oa_data.po_number,  # For verification
        items_created=items_created,
        line_items_created=line_items_created,
        email_linked=bool(source_email.email_thread),
    )

    return oa


def _determine_oa_status(oa_data: OrderAcknowledgementSchema) -> str:
    """
    Determine the OA status based on what AI extracted.

    Args:
        oa_data: Extracted OA data

    Returns:
        OA status string
    """
    # Check if ALL items have promised ship dates (key for follow-up workflows)
    has_all_ship_dates = False
    if hasattr(oa_data, "order_items") and oa_data.order_items:
        has_all_ship_dates = all(
            item.promised_ship_date for item in oa_data.order_items
        )

    # Check if we have supplier's order number
    has_order_number = bool(oa_data.order_number)

    # Check if PO number was present (for matching verification)
    has_po_number = bool(oa_data.po_number)

    if has_all_ship_dates and has_order_number and has_po_number:
        return "COMPLETE"
    elif has_order_number and has_po_number:
        return "INCOMPLETE"  # Has acknowledgment but missing item ship dates
    else:
        return "PENDING_CLARIFICATION"  # Minimal info extracted


def _create_oa_items(oa: OrderAcknowledgement, extracted_items: list) -> int:
    """
    Create OrderAcknowledgementItems from AI extracted data (minimal approach).
    Only saves what AI found, no fallbacks from PO.
    Links to OrderItem when possible for proper traceability.

    Args:
        oa: OrderAcknowledgement instance
        extracted_items: List of extracted item data from AI (Pydantic objects)

    Returns:
        Number of items created
    """
    from didero.orders.models import OrderAcknowledgementItem, OrderItem
    from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.validation import (
        convert_to_decimal,
    )

    # Pre-fetch all OrderItems for this PO for efficient matching
    order_items_by_item_number = {}
    for order_item in OrderItem.objects.filter(
        purchase_order=oa.purchase_order
    ).select_related("item"):
        item_number = order_item.item.item_number.upper()
        # Handle multiple OrderItems with same item number
        if item_number not in order_items_by_item_number:
            order_items_by_item_number[item_number] = []
        order_items_by_item_number[item_number].append(order_item)

    items_created = 0
    for item_data in extracted_items:
        try:
            # Handle Pydantic objects - use attribute access, not .get()
            unit_price = (
                convert_to_decimal(item_data.unit_price) if item_data.unit_price else 0
            )
            total_price = (
                convert_to_decimal(item_data.total_price)
                if item_data.total_price
                else 0
            )
            quantity = _safe_float(item_data.quantity) or 0.0

            # Parse promised dates if available
            from didero.orders.utils.model_creation_utils import parse_date_string

            promised_ship_date = (
                parse_date_string(item_data.promised_ship_date)
                if item_data.promised_ship_date
                else None
            )
            promised_delivery_date = (
                parse_date_string(item_data.promised_delivery_date)
                if item_data.promised_delivery_date
                else None
            )

            # Try to match to an OrderItem using DocumentItemMatch table to handle different item numbers
            order_item = None
            if item_data.item_number:
                # First check DocumentItemMatch table for mapped PO item number
                # Look for matches where this OA item maps to a PO item
                matched_items = DocumentItemMatch.find_matches_for_item(
                    doc_type=DocumentType.ORDER_ACKNOWLEDGEMENT.value,
                    item_number=item_data.item_number,
                    team_id=oa.purchase_order.team.id,
                )

                # Extract the mapped PO item number
                mapped_po_item_number = None
                for match in matched_items:
                    if match.first_doc_type == DocumentType.PO.value:
                        mapped_po_item_number = match.first_item_number
                        break
                    elif match.second_doc_type == DocumentType.PO.value:
                        mapped_po_item_number = match.second_item_number
                        break

                # Use mapped PO item number if found, otherwise use OA item number
                po_item_number_to_search = (
                    mapped_po_item_number or item_data.item_number
                )

                matching_order_items = order_items_by_item_number.get(
                    po_item_number_to_search.upper(), []
                )

                if mapped_po_item_number:
                    logger.debug(
                        "Found mapped PO item number for OA item",
                        oa_item_number=item_data.item_number,
                        mapped_po_item_number=mapped_po_item_number,
                        team_id=oa.purchase_order.team.id,
                    )
                if matching_order_items:
                    if len(matching_order_items) == 1:
                        # Simple case: only one OrderItem with this item number
                        order_item = matching_order_items[0]
                        logger.debug(
                            "Matched OA item to single OrderItem",
                            oa_id=oa.pk,
                            item_number=item_data.item_number,
                            order_item_id=order_item.pk,
                        )
                    else:
                        # Multiple OrderItems - match by quantity and price
                        oa_quantity = _safe_float(item_data.quantity) or 0.0
                        oa_unit_price = (
                            convert_to_decimal(item_data.unit_price)
                            if item_data.unit_price
                            else 0
                        )

                        # Find OrderItem with matching quantity AND price
                        exact_match = None
                        for oi in matching_order_items:
                            quantity_match = abs(oi.quantity - oa_quantity) < 0.01
                            price_match = (
                                abs(float(oi.price.amount) - float(oa_unit_price))
                                < 0.01
                            )

                            if quantity_match and price_match:
                                exact_match = oi
                                break

                        if exact_match:
                            order_item = exact_match
                            logger.debug(
                                "Matched OA item to OrderItem by quantity and price",
                                oa_id=oa.pk,
                                item_number=item_data.item_number,
                                order_item_id=order_item.pk,
                                oa_quantity=oa_quantity,
                                oa_price=str(oa_unit_price),
                                order_item_quantity=order_item.quantity,
                                order_item_price=str(order_item.price.amount),
                            )
                        else:
                            # No exact match - leave order_item as None for data integrity
                            logger.warning(
                                "Multiple OrderItems found with no quantity+price match - leaving order_item null",
                                oa_id=oa.pk,
                                item_number=item_data.item_number,
                                oa_quantity=oa_quantity,
                                oa_price=str(oa_unit_price),
                                total_candidates=len(matching_order_items),
                                candidate_details=[
                                    {
                                        "id": oi.id,
                                        "quantity": oi.quantity,
                                        "price": str(oi.price.amount),
                                    }
                                    for oi in matching_order_items
                                ],
                            )

            OrderAcknowledgementItem.objects.create(
                order_acknowledgement=oa,
                order_item=order_item,  # Link to OrderItem when found
                item=order_item.item
                if order_item
                else None,  # Keep for backward compatibility
                item_number=item_data.item_number or "",
                item_description=item_data.item_description or "",
                quantity=quantity,
                unit_of_measure=item_data.unit_of_measure or "Each",
                unit_price=unit_price,
                total_price=total_price,
                promised_ship_date=promised_ship_date,
                promised_delivery_date=promised_delivery_date,
            )
            items_created += 1
            logger.info(
                "Created OrderAcknowledgementItem",
                oa_id=oa.pk,
                item_number=item_data.item_number or "",
                quantity=quantity,
                unit_price=str(unit_price),
                order_item_id=order_item.pk if order_item else None,
                matched_to_po_item=bool(order_item),
                promised_ship_date=str(promised_ship_date)
                if promised_ship_date
                else None,
                promised_delivery_date=str(promised_delivery_date)
                if promised_delivery_date
                else None,
            )
        except Exception as e:
            logger.warning(
                "Failed to create OrderAcknowledgementItem",
                oa_id=oa.pk,
                item_data=item_data,
                error=str(e),
            )

    return items_created


def _create_oa_line_items_from_charges(
    oa: OrderAcknowledgement, oa_data: OrderAcknowledgementSchema
) -> int:
    """
    Create OrderAcknowledgmentLineItems from charges in the OA schema.

    Args:
        oa: OrderAcknowledgement instance
        oa_data: Pydantic schema with extracted OA data

    Returns:
        Number of line items created
    """
    from didero.orders.models import OrderAcknowledgmentLineItem
    from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.validation import (
        convert_to_decimal,
    )

    line_items_created = 0

    # Create line item for shipping/freight charges
    if oa_data.shipping_or_freight_charges:
        try:
            amount = convert_to_decimal(oa_data.shipping_or_freight_charges)
            OrderAcknowledgmentLineItem.objects.create(
                order_acknowledgement=oa,
                category="SHIPPING",
                description="Shipping/Freight Charges",
                amount=amount,
            )
            line_items_created += 1
            logger.debug(
                "Created OrderAcknowledgmentLineItem for shipping",
                oa_id=oa.pk,
                amount=str(amount),
            )
        except Exception as e:
            logger.warning(
                "Failed to create shipping line item", oa_id=oa.pk, error=str(e)
            )

    # Create line item for other special charges
    if oa_data.other_special_charges:
        try:
            amount = convert_to_decimal(oa_data.other_special_charges)
            OrderAcknowledgmentLineItem.objects.create(
                order_acknowledgement=oa,
                category="CUSTOM",
                description="Other Special Charges",
                amount=amount,
            )
            line_items_created += 1
            logger.debug(
                "Created OrderAcknowledgmentLineItem for special charges",
                oa_id=oa.pk,
                amount=str(amount),
            )
        except Exception as e:
            logger.warning(
                "Failed to create special charges line item", oa_id=oa.pk, error=str(e)
            )

    return line_items_created


def _create_oa_line_items(oa: OrderAcknowledgement, line_items_data: list) -> int:
    """
    Create OrderAcknowledgmentLineItems from AI extracted data.

    Args:
        oa: OrderAcknowledgement instance
        line_items_data: List of line item data from AI (could be dicts or Pydantic objects)

    Returns:
        Number of line items created
    """
    from didero.orders.models import OrderAcknowledgmentLineItem
    from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.validation import (
        convert_to_decimal,
    )

    line_items_created = 0
    for line_item_data in line_items_data:
        try:
            # Handle both dict and Pydantic object access
            if hasattr(line_item_data, "amount"):
                # Pydantic object
                amount = (
                    convert_to_decimal(line_item_data.amount)
                    if line_item_data.amount
                    else 0
                )
                category = line_item_data.category or "CUSTOM"
                description = line_item_data.description or ""
            else:
                # Dict
                amount = (
                    convert_to_decimal(line_item_data.get("amount", "0"))
                    if line_item_data.get("amount")
                    else 0
                )
                category = line_item_data.get("category", "CUSTOM")
                description = line_item_data.get("description", "")

            OrderAcknowledgmentLineItem.objects.create(
                order_acknowledgement=oa,
                category=category,
                description=description,
                amount=amount,
            )
            line_items_created += 1
            logger.debug(
                "Created OrderAcknowledgmentLineItem",
                oa_id=oa.pk,
                category=category,
                description=description,
                amount=str(amount),
            )
        except Exception as e:
            logger.warning(
                "Failed to create OrderAcknowledgmentLineItem",
                oa_id=oa.pk,
                line_item_data=line_item_data,
                error=str(e),
            )

    return line_items_created


def _safe_float(value) -> Optional[float]:
    """Safely convert value to float."""
    if value is None:
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None


def _apply_global_dates_to_items(oa: OrderAcknowledgement) -> None:
    """Apply global promised dates to items that don't have specific dates."""
    from didero.orders.utils.model_creation_utils import parse_date_string

    # Parse global dates if they exist
    global_ship_date = None
    global_delivery_date = None

    if oa.global_promised_ship_date:
        global_ship_date = parse_date_string(oa.global_promised_ship_date)

    if oa.global_promised_delivery_date:
        global_delivery_date = parse_date_string(oa.global_promised_delivery_date)

    # Apply to items missing specific dates
    items_updated = 0
    for item in oa.items.all():
        updated = False

        if global_ship_date and not item.promised_ship_date:
            item.promised_ship_date = global_ship_date
            updated = True

        if global_delivery_date and not item.promised_delivery_date:
            item.promised_delivery_date = global_delivery_date
            updated = True

        if updated:
            item.save()
            items_updated += 1

    if items_updated > 0:
        logger.info(
            "Applied global dates to OA items",
            oa_id=oa.pk,
            items_updated=items_updated,
            global_ship_date=str(global_ship_date) if global_ship_date else None,
            global_delivery_date=str(global_delivery_date)
            if global_delivery_date
            else None,
        )


def _has_valid_address_data(address: Any) -> bool:
    """Check if address has meaningful data to justify saving it."""
    if not address:
        return False
    # Country is required for address validation, so require it along with other fields
    return bool(
        address.country
        and (
            address.line_1
            or address.city
            or address.postal_code
            or address.state_or_province
        )
    )
