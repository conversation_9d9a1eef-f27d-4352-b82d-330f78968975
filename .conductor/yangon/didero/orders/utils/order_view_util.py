from django.db.models import Q
from django_filters import rest_framework as filters
from django_filters.rest_framework import BaseInFilter

from didero.filters import (
    ArchiveStatus,
    BaseFilter,
    ChoiceInFilter,
    NumberInFilter,
    get_enum_choices,
)
from didero.orders.models import PurchaseOrder
from didero.orders.schemas import PaymentStatus, PurchaseOrderStatus, SourceChoices


class PurchaseOrderFilter(BaseFilter):
    po_number_contains = filters.CharFilter(
        field_name="po_number", lookup_expr="icontains"
    )
    ids = NumberInFilter(field_name="id")
    supplier_ids = NumberInFilter(field_name="supplier__id")
    supplier_name_contains = filters.CharFilter(
        field_name="supplier__name", lookup_expr="icontains"
    )
    item_description_contains = filters.CharFilter(
        field_name="items__item__description", lookup_expr="icontains"
    )
    tags = BaseInFilter(field_name="tags__name")
    order_status = ChoiceInFilter(choices=get_enum_choices(PurchaseOrderStatus))
    payment_status = filters.ChoiceFilter(choices=get_enum_choices(PaymentStatus))
    source = filters.ChoiceFilter(choices=get_enum_choices(SourceChoices))
    approver = filters.NumberFilter(field_name="approvals__approver", distinct=True)
    item_id = filters.UUIDFilter(field_name="items__item", distinct=True)
    placement_time_after = filters.DateFilter(
        field_name="placement_time", lookup_expr="gte"
    )
    placement_time_before = filters.DateFilter(
        field_name="placement_time", lookup_expr="lte"
    )
    requested_date_after = filters.DateFilter(
        field_name="requested_date", lookup_expr="gte"
    )
    requested_date_before = filters.DateFilter(
        field_name="requested_date", lookup_expr="lte"
    )
    estimated_delivery_date_after = filters.DateFilter(
        method="filter_shipment_estimated_after_date",
        field_name="shipments__estimated_delivery_date",
        lookup_expr="gte",
    )
    estimated_delivery_date_before = filters.DateFilter(
        method="filter_shipment_estimated_before_date",
        field_name="shipments__estimated_delivery_date",
        lookup_expr="lte",
    )
    actual_delivery_date_after = filters.DateFilter(
        method="filter_shipment_actual_after_date",
        field_name="shipments__actual_delivery_date",
        lookup_expr="gte",
    )
    actual_delivery_date_before = filters.DateFilter(
        method="filter_shipment_actual_before_date",
        field_name="shipments__actual_delivery_date",
        lookup_expr="lte",
    )
    total_cost_min = filters.NumberFilter(field_name="total_cost", lookup_expr="gte")
    total_cost_max = filters.NumberFilter(field_name="total_cost", lookup_expr="lte")
    archived = ChoiceInFilter(
        method="filter_archived", choices=get_enum_choices(ArchiveStatus)
    )

    class Meta:
        model = PurchaseOrder
        fields = {
            "po_number": ["exact"],
            "supplier": ["exact"],
            "team": ["exact"],
            "placed_by": ["exact"],
            "sender_address": ["exact"],
            "shipping_address": ["exact"],
            "internal_notes": ["icontains"],
            "vendor_notes": ["icontains"],
            "placement_time": ["exact"],
            "requested_date": ["exact"],
            "shipping_terms": ["exact"],
            "id": ["exact"],
        }

    def filter_shipment_estimated_after_date(self, queryset, name, value):
        return queryset.filter(shipments__estimated_delivery_date__gt=value).distinct()

    def filter_shipment_estimated_before_date(self, queryset, name, value):
        return queryset.filter(shipments__estimated_delivery_date__lt=value).distinct()

    def filter_shipment_actual_after_date(self, queryset, name, value):
        return queryset.filter(shipments__actual_delivery_date__gt=value).distinct()

    def filter_shipment_actual_before_date(self, queryset, name, value):
        return queryset.filter(shipments__actual_delivery_date__lt=value).distinct()

    # Search across multiple fields that make sense for search within /orders
    # This does not remove non-alphanumeric characters, so if e.g. a user searcher
    # for "$800", that will not be a match - but "800" would be
    def multi_field_search(self, queryset, name, value):
        return queryset.filter(
            Q(po_number__icontains=value)
            | Q(supplier__name__icontains=value)
            | Q(items__item__description__icontains=value)
            | Q(total_cost__icontains=value)
        ).distinct()

    def filter_queryset(self, queryset):
        # If request specifies archived, then we take that into account
        # Otherwise by default, we do not show orders that are archived
        if "archived" not in self.data:
            queryset = queryset.filter(archived_at__isnull=True)
        return super().filter_queryset(queryset)

    @property
    def qs(self):
        """
        This property ensures that the queryset returned by the filter is distinct,
        solving the problem of duplicate entries when filtering across many-to-many joins,
        e.g. fields within Items or Approvals.
        """
        queryset = super().qs
        return queryset.distinct()


def get_total_cost_from_request_data(data):
    items = data.get("items", [])
    line_items = data.get("lineItems", [])
    total = sum(
        (float(item["price"]) * float(item["quantity"])) for item in items
    ) + sum(float(line_item["price"]) for line_item in line_items)

    return round(total, 2)


def get_total_cost_currency_from_request_data(data):
    # check the order items; they should all be using the same
    items = data.get("items")
    if items:
        return items[0]["price_currency"]
    return "USD"
