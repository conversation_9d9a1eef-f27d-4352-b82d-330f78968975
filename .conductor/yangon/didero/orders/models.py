from datetime import date
from decimal import Decimal
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, TypeVar

import structlog
from actstream import action
from auditlog.registry import auditlog
from django.contrib.contenttypes.models import ContentType
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import IntegrityError, models, transaction
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from djmoney.contrib.exchange.exceptions import MissingRate
from djmoney.contrib.exchange.models import convert_money
from djmoney.models.fields import MoneyField
from djmoney.models.validators import MinMoneyValidator
from djmoney.money import Currency
from taggit.managers import TaggableManager
from transitions import EventData, Machine, Transition

from didero.activity_log.schemas import SystemActor
from didero.addresses.models import Address
from didero.common.models import TaggedItem
from didero.documents.models import Document, DocumentLink
from didero.documents.schemas import DocumentType
from didero.emails.postmark_service import PostmarkEmailService
from didero.items.models import Item
from didero.models import BaseComment, BaseModelWithWebsocketPublishing
from didero.notifications.helper import NotificationHelper
from didero.orders.schemas import (
    ORDER_STATUS_TRANSITIONS,
    PAYMENT_STATUS_TRANSITIONS,
    SHIPPING_TERMS_CHOICES,
    STATUSES_POST_APPROVAL,
    STATUSES_USER_SETTABLE,
    CarrierType,
    DeliveryStatus,
    LineItemCategoryChoices,
    PaymentStatus,
    PurchaseOrderStatus,
    ShipmentStatus,
    ShipmentSyncStatus,
    SourceChoices,
    get_date_field_label,
    get_date_help_text,
)
from didero.orders.utils.order_pdf_util import po_to_pdf
from didero.suppliers.models import Supplier
from didero.tasks.models import Task, TaskStatus
from didero.tasks.schemas import TaskType
from didero.tasks.utils import (
    create_task,
)
from didero.users.models.team_models import Team
from didero.users.models.user_models import User
from didero.utils.utils import (
    get_didero_ai_user,
)
from didero.workflows.models import WorkflowRun

logger = structlog.get_logger(__name__)


class OrderApproval(models.Model):
    purchase_order = models.ForeignKey(
        "PurchaseOrder", on_delete=models.CASCADE, related_name="approvals"
    )
    approver = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="approvals", null=True, blank=True
    )
    approver_group = models.ForeignKey(
        "users.DideroUserGroup",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="order_approvals",
    )
    requested_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    denied_at = models.DateTimeField(null=True, blank=True)
    associated_workflow_id = models.CharField(max_length=255, null=True, blank=True)
    associated_workflow_node_id = models.CharField(
        max_length=255, null=True, blank=True
    )

    class Meta:
        permissions = [("approve_purchase_order", "Can approve purchase orders")]

    def is_requested(self):
        return self.requested_at is not None

    def is_denied(self):
        return self.denied_at is not None

    def is_approved(self):
        return self.approved_at is not None

    def request_approval(self):
        if self.approver and self.purchase_order.placed_by == self.approver:
            logger.info(
                "PO approval request skipped because placed_by is the same as the approver",
                po_id=self.purchase_order.id,
                approver_id=self.approver.id,
            )
            self.approve()
            return

        if not self.purchase_order.placed_by:
            logger.info(
                "PO approval request skipped because placed_by is not set",
                po_id=self.purchase_order.id,
                approver_id=self.approver.id if self.approver else None,
                approver_group_id=self.approver_group.id
                if self.approver_group
                else None,
            )
            self.approve()
            return

        self.requested_at = timezone.now()

        # Send emails based on approval type
        if self.approver_group:
            # Send email to all group members
            for member in self.approver_group.users.all():
                try:
                    PostmarkEmailService.send_po_approval_request_email(
                        requestor=self.purchase_order.placed_by,
                        approver=member,
                        po=self.purchase_order,
                    )
                except Exception as e:
                    logger.error(
                        "Failed to send PO approval email to group member",
                        error=str(e),
                        member_id=member.id,
                        group_id=self.approver_group.id,
                        po_id=self.purchase_order.id,
                    )
        elif self.approver:
            # Send email to single approver
            try:
                PostmarkEmailService.send_po_approval_request_email(
                    requestor=self.purchase_order.placed_by,
                    approver=self.approver,
                    po=self.purchase_order,
                )
            except Exception as e:
                logger.error(
                    "Failed to send PO approval email to single approver",
                    error=str(e),
                    approver_id=self.approver.id,
                    po_id=self.purchase_order.id,
                )

        self.save()

    def approve(self):
        self.approved_at = timezone.now()
        self.save()

    def deny(self, user_actor=None, system_actor=None):
        self.denied_at = timezone.now()
        self.save()


auditlog.register(OrderApproval)


class PurchaseOrder(BaseModelWithWebsocketPublishing):
    po_number = models.CharField(max_length=255, db_index=True, null=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, null=True)
    team = models.ForeignKey(
        Team, on_delete=models.CASCADE, related_name="purchase_orders"
    )

    """
    placed_by is allowed to be null. The motivation behind this is:
    If a user places through our platform, we know who they are, and can save User.
    If we've scraped from an ERP or email, perhaps we can infer it. But for now, just
    save as null.
    If a user is deleted, placed_by becomes null, instead of the entire order being deleted.
    """
    placed_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True
    )

    # External requestor email - used when requestor is not a user in the system
    external_requestor_email = models.EmailField(
        null=True,
        blank=True,
        help_text="Email of the person who requested this PO when they are not a system user",
    )

    tags = TaggableManager(through=TaggedItem, blank=True)
    sender_address = models.ForeignKey["Address"](
        Address,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="purchase_order_sent_from",
    )
    shipping_address = models.ForeignKey["Address"](
        Address,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="purchase_order_shipped_to",
    )
    internal_notes = models.TextField(null=True, blank=True)
    vendor_notes = models.TextField(null=True, blank=True)
    payment_terms = models.TextField(null=True, blank=True)
    shipping_method = models.TextField(null=True, blank=True)
    order_status = models.CharField(
        max_length=20,
        choices=[(tag.value, tag.name) for tag in PurchaseOrderStatus],
        default=PurchaseOrderStatus.DRAFT.value,
    )
    payment_status = models.CharField(
        max_length=20,
        choices=[(tag.value, tag.name) for tag in PaymentStatus],
        default=PaymentStatus.UNPAID.value,
    )
    source = models.CharField(
        max_length=20, choices=[(tag.value, tag.name) for tag in SourceChoices]
    )
    placement_time = models.DateTimeField(default=timezone.now)

    # New consolidated fields following industry standards
    requested_date = models.DateField(
        null=True,
        blank=True,
        help_text="Interpretation depends on shipping_terms: EXW='ready for pickup', DAP='delivery date', etc.",
    )
    shipping_terms = models.CharField(
        max_length=50,  # Increased length to accommodate custom terms
        null=True,
        blank=True,
        help_text="Defines shipping responsibility and date interpretation (supports custom terms)",
    )

    archived_at = models.DateTimeField(
        null=True, default=None, db_index=True, blank=True
    )
    total_cost = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="USD",
        default=Decimal("0.00"),
        validators=[MinMoneyValidator(0)],
    )
    # The primary PO PDF for this PO.
    document = models.OneToOneField(
        Document,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="This is the primary PDF (of type PO) that was generated for this PO in particular. Other documents should be connected as normal, with a DocumentLink.",
    )
    # Used for Async Order Creation so order creation status can be tracked (generally AI workflows will utilize this flag)
    # AI workflows will set this to true to start and move to false once the workflow has populated the order.
    # TODO: Modify this to is_async_po_processing to note the reusability of this flag.
    is_external_po_processing = models.BooleanField(null=True, blank=True)

    # Reverse relationships with models that have a FK relationship with PurchaseOrder
    items: models.QuerySet["OrderItem"]
    shipments: models.QuerySet["Shipment"]
    is_po_editable = models.BooleanField(null=True, blank=True, default=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["po_number", "team"],
                name="unique_po_number_team",
                condition=models.Q(po_number__isnull=False, team__isnull=False),
            )
        ]
        # index for all fields we want to filter by (eg: statuses, source, event times)
        indexes = [
            models.Index(fields=["order_status"]),
            models.Index(fields=["payment_status"]),
            models.Index(fields=["source"]),
            models.Index(fields=["placement_time"]),
            models.Index(fields=["requested_date"]),
            models.Index(fields=["po_number"]),
            models.Index(fields=["total_cost"]),
            # Any time we GET orders, we check both the team and if the order is archived.
            models.Index(fields=["team", "archived_at"]),
        ]

    def get_date_field_label(self):
        """Returns appropriate label based on shipping terms"""
        if self.shipping_terms:
            return get_date_field_label(self.shipping_terms)
        return "Requested Date"

    def get_date_help_text(self):
        """Returns help text explaining the date field based on shipping terms"""
        if self.shipping_terms:
            return get_date_help_text(self.shipping_terms)
        return ""

    def save(self, *args, **kwargs):
        # Check if the instance already exists in the database. (Otherwise this is a new create)
        is_new_object = self.pk is None
        if not is_new_object:
            self._pre_save_handle_cancelled_orders()

        # Handle the dual field logic for requestor
        # If we have external_requestor_email but no placed_by, try to find the user
        if self.external_requestor_email and not self.placed_by:
            try:
                self.placed_by = User.objects.filter(
                    plain_email__iexact=self.external_requestor_email, teams=self.team
                ).first()
                if self.placed_by:
                    logger.info(
                        f"PO {self.pk or 'new'}: Found user for external_requestor_email {self.external_requestor_email}"
                    )
            except Exception as e:
                logger.warning(
                    f"PO {self.pk or 'new'}: Error finding user for email {self.external_requestor_email}: {e}"
                )

        # if (the placed_by is not set, or is the didero admin) and (team has a default_requestor)
        # then set the placed_by to the default_requestor
        if (
            not self.placed_by or self.placed_by == get_didero_ai_user(self.team)
        ) and self.team.default_requestor:
            self.placed_by = self.team.default_requestor
            # If we're using default requestor and no external email was provided,
            # set the external_requestor_email to the default requestor's email
            if not self.external_requestor_email:
                self.external_requestor_email = self.team.default_requestor.email
            logger.info(
                f"PO {self.pk or 'new'} has no placed_by setting placed by to default requestor"
            )

        # Handle delivery instructions from shipping address
        self._add_delivery_instructions_to_vendor_notes()

        # Call the original save method
        super().save(*args, **kwargs)

    def _add_delivery_instructions_to_vendor_notes(self):
        """Add delivery instructions from shipping address to vendor notes."""
        if not self.shipping_address:
            return

        delivery_instructions = getattr(
            self.shipping_address, "delivery_instructions", None
        )

        if delivery_instructions and delivery_instructions.strip():
            current_vendor_notes = self.vendor_notes or ""
            delivery_text = f"Delivery Instructions:\n{delivery_instructions}"

            # Only add if not already present
            if delivery_text not in current_vendor_notes:
                if current_vendor_notes:
                    self.vendor_notes = f"{current_vendor_notes}\n\n{delivery_text}"
                else:
                    self.vendor_notes = delivery_text

    def _pre_save_handle_cancelled_orders(self):
        # If an order's status is changed to CANCELED (e.g. when it is archived),
        # we should mark all of its pending tasks as completed
        old_instance = PurchaseOrder.objects.get(pk=self.pk)
        old_status = old_instance.order_status
        new_status = self.order_status
        if (
            old_status != PurchaseOrderStatus.CANCELED
            and new_status == PurchaseOrderStatus.CANCELED
        ):
            # Find all pending tasks for this order. the status can either be PENDING or ON_HOLD
            non_complete_tasks = Task.objects.filter(
                status__in=[TaskStatus.PENDING.value, TaskStatus.ON_HOLD.value],
                model_type=ContentType.objects.get_for_model(self),
                model_id=self.id,
            )
            # Mark all pending tasks as completed
            for task in non_complete_tasks:
                task.status = TaskStatus.COMPLETED.value
                task.save()

    @property
    def requestor_email(self):
        """
        Get the requestor's email address.
        Returns external_requestor_email if exists (the actual extracted/determined email),
        otherwise placed_by user's email if exists.
        """
        if self.external_requestor_email:
            return self.external_requestor_email
        if self.placed_by:
            return self.placed_by.email
        return None

    @property
    def requestor_display_name(self):
        """
        Get the requestor's display name.
        If external_requestor_email exists and matches placed_by, show the user's name.
        Otherwise show the email address, or None if nothing is available.
        """
        # If we have external_requestor_email
        if self.external_requestor_email:
            # If placed_by exists and matches the email, show the user's name
            if (
                self.placed_by
                and self.placed_by.email.lower()
                == self.external_requestor_email.lower()
            ):
                return self.placed_by.display_name
            # Otherwise show the email
            return self.external_requestor_email

        # If no external_requestor_email but we have placed_by, show their name
        if self.placed_by:
            return self.placed_by.display_name

        return None

    """
    Because POs have addresses associated with them, and we want to make sure
    they're not orphaned upon PO delete, we manually delete them here -
    but only if they're a one-time address (meaning team and supplier are null).
    """

    def delete(self, *args, **kwargs):
        sender_address = self.sender_address
        if sender_address and not sender_address.team and not sender_address.supplier:
            sender_address.delete()
        shipping_address = self.shipping_address
        if (
            shipping_address
            and not shipping_address.team
            and not shipping_address.supplier
        ):
            shipping_address.delete()

        docs = [
            link.document
            for link in DocumentLink.objects.filter(
                parent_object_type=ContentType.objects.get_for_model(self),
                parent_object_id=self.id,
            )
        ]
        for doc in docs:
            # Only delete the doc if it has no other links
            if len(doc.links.all()) <= 1:
                doc.delete()

        super().delete(*args, **kwargs)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Get the field values directly from __dict__ to avoid triggering refresh_from_db.
        order_status = self.__dict__.get("order_status")
        payment_status = self.__dict__.get("payment_status")

        # Initialize the state machine for order_status using the value from __dict__.
        self.status_machine = Machine(
            model=self,
            model_attribute="order_status",
            states=[status.value for status in PurchaseOrderStatus],
            transitions=ORDER_STATUS_TRANSITIONS,
            initial=order_status,  # Use the value from __dict__
            send_event=True,
            after_state_change=self._after_state_change,
        )

        # Initialize the state machine for payment_status using the value from __dict__.
        self.payment_status_machine = Machine(
            model=self,
            model_attribute="payment_status",
            states=[status.value for status in PaymentStatus],
            transitions=PAYMENT_STATUS_TRANSITIONS,
            initial=payment_status,  # Use the value from __dict__
            send_event=True,
            after_state_change=self._after_state_change,
        )

        self._non_transition_actions = [
            "approve_by_user",
            "deny_by_user",
            "set_order_status_by_user",
            "update_netsuite_shipment_workflow_completed",
        ]

        self.all_actions = set(
            [transition["trigger"] for transition in ORDER_STATUS_TRANSITIONS]
            + [transition["trigger"] for transition in PAYMENT_STATUS_TRANSITIONS]
            + self._non_transition_actions
        )

    def _after_state_change(self, event: EventData):
        from didero.activity_log.models import PurchaseOrderStatusUpdate

        # Save the state transition
        self.save()

        old_status = event.transition.source
        new_status = event.transition.dest

        PurchaseOrderStatusUpdate.objects.create(
            purchase_order=self,
            user_actor=event.kwargs.get("user_actor"),
            system_actor=event.kwargs.get("system_actor"),
            status_changed=event.machine.model_attribute,
            old_status=old_status,
            new_status=new_status,
        ).save()

        # Notifications are being phased out, but leaving this here for now.
        # do not alert on draft to approval_pending since this is the same as
        # telling the user they just created a PO
        if not (
            old_status == PurchaseOrderStatus.DRAFT
            and new_status == PurchaseOrderStatus.APPROVAL_PENDING
        ):
            NotificationHelper.create_notification_for_po_status_update(
                po=self,
                actor=event.kwargs.get("user_actor"),
                old_status=event.transition.source,
                new_status=event.transition.dest,
            )
        # We create tasks / updates for the user, based on certain new statuses
        # Ideally these would be include both old and new statuses - to show the full
        # transition. However, we don't yet have an exhaustive combination of ways
        # POs can get to each status, so we just do catch-alls of the final status
        actor_name = (
            f"{event.kwargs.get('user_actor').display_name}"
            if event.kwargs.get("user_actor")
            else "An automatic process"
        )

        last_shipment = self.shipments.order_by("estimated_delivery_date").last()

        NEW_STATUSES_TO_TASK_TYPES: Dict[
            PurchaseOrderStatus, Tuple[TaskType, Dict[str, Any]]
        ] = {
            PurchaseOrderStatus.APPROVAL_DENIED: (
                TaskType.PO_STATUS_UPDATE_APPROVAL_DENIED,
                {
                    "approver_name": actor_name,
                    "po_number": self.po_number,
                },
            ),
            PurchaseOrderStatus.APPROVED: (
                TaskType.PO_STATUS_UPDATE_APPROVED,
                {
                    "approver_name": actor_name,
                    "po_number": self.po_number,
                    "supplier_name": self.supplier.name,
                },
            ),
            PurchaseOrderStatus.SUPPLIER_REJECTED: (
                TaskType.PO_STATUS_UPDATE_SUPPLIER_REJECTED,
                {"supplier_name": self.supplier.name, "po_number": self.po_number},
            ),
            PurchaseOrderStatus.SHIPPED: (
                TaskType.PO_STATUS_UPDATE_SHIPPED,
                {
                    "supplier_name": self.supplier.name,
                    "po_number": self.po_number,
                    "eta_string": (
                        f" Products are expected to arrive by {last_shipment.estimated_delivery_date}."
                        if (
                            last_shipment
                            and last_shipment.estimated_delivery_date
                            and last_shipment.estimated_delivery_date >= date.today()
                        )
                        else ""
                    ),
                },
            ),
            PurchaseOrderStatus.RECEIVED: (
                TaskType.PO_STATUS_UPDATE_RECEIVED,
                {
                    "po_number": self.po_number,
                },
            ),
            PurchaseOrderStatus.CANCELED: (
                TaskType.PO_STATUS_UPDATE_CANCELED,
                {
                    "po_number": self.po_number,
                    "canceler_user_name": actor_name,
                },
            ),
        }
        if new_status in NEW_STATUSES_TO_TASK_TYPES:
            task_type, extra_kwargs = NEW_STATUSES_TO_TASK_TYPES[new_status]
            create_task(
                task_type=task_type,
                user=self.placed_by,
                status=TaskStatus.PENDING.value,
                model_type=ContentType.objects.get_for_model(self),
                model_id=self.id,
                **extra_kwargs,
            )

    def handle_revert_to_draft(self, *_):
        """
        If a PO is sent for approval, but then reverted to a draft, we should:
        1. Delete all approvals
        2. Delete all PO_APPROVAL_REQUEST tasks for this PO
        3. Delete all PO_STATUS_UPDATE tasks for this PO that are still pending
        """

        self.approvals.all().delete()
        po_approval_request_tasks = Task.objects.filter(
            user=self.placed_by,
            task_type__name="PO_APPROVAL_REQUEST",
            model_type=ContentType.objects.get_for_model(self),
            model_id=self.id,
        )
        po_approval_request_tasks.delete()
        # delete all tasks that start with PO_STATUS_UPDATE that are still pending
        pending_po_status_update_tasks = Task.objects.filter(
            user=self.placed_by,
            task_type__name__startswith="PO_STATUS_UPDATE",
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self),
            model_id=self.id,
        )
        pending_po_status_update_tasks.delete()

        if self.document:
            self.document.delete()
        self.document_id = None

    def save_as_pdf(self):
        # Don't generate PDF for external po imports as we want to use the original PO as source of truth.
        # The external PO Document is linked to the PurchaseOrder at creation.
        if self.source != SourceChoices.EXTERNAL_PO_IMPORT:
            fileobj = self.generate_pdf()
            doc = Document.get_or_create_from_file(fileobj, self.team)
            doc.doc_type = DocumentType.PO
            doc.save(update_fields=["doc_type"])
            self.document = doc
            self.save(update_fields=["document"])
            DocumentLink.objects.create(
                document=doc,
                parent_object=self,
            )

    def generate_pdf(self):
        if self.source == SourceChoices.EXTERNAL_PO_IMPORT:
            return InMemoryUploadedFile(
                file=self.document.document,
                name=self.document.document.name,
                field_name=None,
                content_type="application/pdf",
                size=self.document.document.size,
                charset=None,
            )

        from didero.orders.serializers import PurchaseOrderSerializer

        data = PurchaseOrderSerializer(self).data
        data["supplier_id"] = self.supplier.id
        pdfResult, error = po_to_pdf(data)
        if error:
            raise Exception(f"Unable to parse PDF: {error}")

        po_number = data["po_number"]

        return InMemoryUploadedFile(
            pdfResult,
            name=f"{po_number}.pdf",
            field_name=None,
            content_type="application/pdf",
            size=len(pdfResult.getvalue()),
            charset=None,
        )

    def approve_by_user(
        self, user_actor: User, system_actor: Optional[SystemActor] = None, **kwargs
    ):
        from didero.workflows.utils import send_signal_to_workflow

        approval = OrderApproval.objects.get(pk=kwargs["approval_id"])

        # Validate user can approve
        if approval.approver_group:
            # Check if user is member of the group
            if not approval.approver_group.users.filter(id=user_actor.id).exists():
                raise ValueError(
                    f"User {user_actor.email} is not a member of the assigned approval group"
                )
            # Set the actual approver since it was created with approver=None for groups
            approval.approver = user_actor
        else:
            # For direct user assignment, verify the user is the assigned approver
            if approval.approver != user_actor:
                raise ValueError(
                    f"User {user_actor.email} is not the assigned approver for this purchase order"
                )

        approval.approve()

        # Search Tasks for the pending task - need to handle both user and group assigned tasks
        if approval.approver_group:
            # For group-assigned tasks, look for tasks assigned to the group
            task = Task.objects.filter(
                user_group=approval.approver_group,
                task_type_v2__name="PO_APPROVAL_REQUEST",
                status=TaskStatus.PENDING.value,
                model_type=ContentType.objects.get_for_model(self),
                model_id=self.id,
            )
        else:
            # For user-assigned tasks
            task = Task.objects.filter(
                user=user_actor,
                task_type_v2__name="PO_APPROVAL_REQUEST",
                status=TaskStatus.PENDING.value,
                model_type=ContentType.objects.get_for_model(self),
                model_id=self.id,
            )

        if task.exists():
            task.update(status=TaskStatus.COMPLETED.value)
        else:
            logger.warning(
                f"No task found for PO_APPROVAL_REQUEST for user {user_actor}, PO {self.pk}."
            )

        send_signal_to_workflow(
            approval.associated_workflow_id,
            approval.associated_workflow_node_id,
            "approved_or_denied",
            "approved",
        )

    def deny_by_user(
        self, user_actor: User, system_actor: Optional[SystemActor] = None, **kwargs
    ):
        from didero.workflows.utils import send_signal_to_workflow

        approval = OrderApproval.objects.get(pk=kwargs["approval_id"])

        # Validate user can deny
        if approval.approver_group:
            # Check if user is member of the group
            if not approval.approver_group.users.filter(id=user_actor.id).exists():
                raise ValueError(
                    f"User {user_actor.email} is not a member of the assigned approval group"
                )
            # Set the actual approver since it was created with approver=None for groups
            approval.approver = user_actor
        else:
            # For direct user assignment, verify the user is the assigned approver
            if approval.approver != user_actor:
                raise ValueError(
                    f"User {user_actor.email} is not the assigned approver for this purchase order"
                )

        approval.deny()

        self.deny_approval(user_actor=user_actor, system_actor=system_actor)
        PostmarkEmailService.send_po_status_update_email(
            to=self.placed_by.email,
            po=self,
            old_status=PurchaseOrderStatus.APPROVAL_PENDING,
            new_status=PurchaseOrderStatus.APPROVAL_DENIED,
        )
        NotificationHelper.create_deny_po_notification(
            po=self,
            approver=user_actor,  # Use user_actor instead of approval.approver
        )

        # Search Tasks for the pending task - need to handle both user and group assigned tasks
        if approval.approver_group:
            # For group-assigned tasks, look for tasks assigned to the group
            task = Task.objects.filter(
                user_group=approval.approver_group,
                task_type_v2__name="PO_APPROVAL_REQUEST",
                status=TaskStatus.PENDING.value,
                model_type=ContentType.objects.get_for_model(self),
                model_id=self.id,
            )
        else:
            # For user-assigned tasks
            task = Task.objects.filter(
                user=user_actor,
                task_type_v2__name="PO_APPROVAL_REQUEST",
                status=TaskStatus.PENDING.value,
                model_type=ContentType.objects.get_for_model(self),
                model_id=self.id,
            )

        if task.exists():
            task.update(status=TaskStatus.COMPLETED.value)
        else:
            logger.warning(
                f"No task found for PO_APPROVAL_REQUEST for user {user_actor}, PO {self.pk}."
            )

        send_signal_to_workflow(
            approval.associated_workflow_id,
            approval.associated_workflow_node_id,
            "approved_or_denied",
            "denied",
        )

    def set_order_status_by_user(self, **kwargs):
        """
        We have a 'fully connected' portion of the state machine,
        where users can manually set any any transition that is past the approval stages.
        Instead of defining transition for every single state, we set it directly.
        """
        new_status = kwargs["new_status"]
        if self.order_status not in STATUSES_POST_APPROVAL:
            raise ValueError("Cannot set status for an unapproved PO")

        if new_status not in STATUSES_USER_SETTABLE:
            raise ValueError(f"Status {new_status} cannot be set by user")

        # manually create eventData since we are faking a state transition
        event = EventData(
            state=self.order_status,
            event=None,
            machine=self.status_machine,
            model=self,
            args=None,
            kwargs=kwargs,
        )
        event.transition = Transition(self.order_status, new_status)

        self.order_status = new_status
        self._after_state_change(event)

    def update_netsuite_shipment_workflow_completed(self, **kwargs) -> Optional[str]:
        """
        BE CAREFUL AROUND CALLING THIS FUNCTION. WORKS FOR TOTAL HOME SUPPLY IN PRODUCTION ONLY RIGHT NOW.
        Scaffolding function to manually trigger the netsuite shipment workflow for THS via task.
        Remove this once we are stable enough to run the job directly.
        """
        import os

        from didero.integrations.stagehand.stagehand import submit_stagehand_job_sync

        logger.info(
            "Starting update_netsuite_shipment_workflow_completed",
            order_id=self.id,
            po_number=self.po_number,
            kwargs=kwargs,
        )

        if os.environ.get("DIDERO_ENVIRONMENT") != "prod":
            logger.error(
                "update_netsuite_shipment_workflow_completed called in non-production environment, please only user for total_home_supply in production"
            )
            return
        shipment_id = kwargs["shipment_id"]
        logger.info(
            "Extracted shipment_id",
            order_id=self.id,
            po_number=self.po_number,
            shipment_id=shipment_id,
        )

        try:
            shipment = Shipment.objects.get(id=shipment_id)
            logger.info(
                "Found shipment record",
                order_id=self.id,
                po_number=self.po_number,
                shipment_id=shipment.id,
                tracking_number=shipment.tracking_number,
            )
        except Shipment.DoesNotExist:
            logger.warning(
                "Shipment does not exist, cannot proceed.",
                order_id=self.id,
                po_number=self.po_number,
                shipment_id=shipment_id,
            )
            return

        rpa_payload = {
            "po_number": self.po_number,
            "shipped_quantities": [
                {item.order_item.item.item_number: item.shipped_quantity}
                for item in shipment.line_items.select_related("order_item__item").all()
            ],
            "carrier": shipment.carrier,
            "tracking_number": shipment.tracking_number,
        }
        logger.info(
            "Prepared RPA payload",
            order_id=self.id,
            po_number=self.po_number,
            shipment_id=shipment.id,
            rpa_payload=rpa_payload,
        )

        try:
            result = submit_stagehand_job_sync(
                customer="total_home_supply",  # Default customer for shipment updates
                job="enter_shipment_in_netsuite",
                params=rpa_payload,
            )
            logger.info(
                "Stagehand job submitted successfully",
                order_id=self.id,
                po_number=self.po_number,
                shipment_id=shipment.id,
                stagehand_response=result,  # Logging the entire response object
            )
            job_id = result.job_id
        except Exception:
            logger.exception(
                "Failed to submit Stagehand job",
                order_id=self.id,
                po_number=self.po_number,
                shipment_id=shipment.id,
                stagehand_payload=rpa_payload,
            )
            # Depending on requirements, we might later want to raise the exception
            # or handle it differently (e.g., set a task status to failed)
            return

        # confirming the action was triggered and job_id extracted
        logger.info(
            "update_netsuite_shipment_workflow_completed order action triggered",
            order_id=self.id,
            po_number=self.po_number,
            shipment_id=shipment_id,
            shipment_status=shipment.status,
            shipment_carrier=shipment.carrier,
            shipment_tracking_number=shipment.tracking_number,
            job_id=job_id,
        )
        return job_id

    def run_action(self, transition_name, *args, **kwargs):
        """
        Run an action on the purchase order. Most of these will end up being
        transitions, but we can also run other methods on the purchase order.
        """
        assert transition_name in self.all_actions
        getattr(self, transition_name)(*args, **kwargs)

    def __str__(self):
        return f"PO {self.po_number} - {self.supplier} - {self.id}"

    def get_total_cost_usd(self):
        try:
            return sum(
                [
                    convert_money(item.price * item.quantity, "USD")
                    for item in self.items.all()
                ]
            ) + sum(
                [
                    convert_money(line_item.amount, "USD")
                    for line_item in self.line_items.all()
                ]
            )
        except MissingRate as e:
            # for some reason, i was getting errors on EUR->USD conversions locally
            # catching the error and failing gracefully
            logger.warning(
                f"PurchaseOrderModelError: {e}",
                exec=e,
                name="orders.models.get_total_cost_usd",
                po_pk=self.pk,
                user_id=self.placed_by,
            )
            return 0

    def get_total_cost(self):
        return sum((item.price * item.quantity) for item in self.items.all()) + sum(
            line_item.amount for line_item in self.line_items.all()
        )

    def get_total_cost_as_string(self) -> str:
        return f"{float(self.get_total_cost().amount):.2f}"

    def get_total_cost_currency(self):
        # check the order items; they should all be using the same
        random_order_item = self.items.all().first()
        if random_order_item:
            return random_order_item.price_currency
        # If the order has no items attached (e.g. is a draft), default to USD
        return "USD"

    def any_approvals_needed(self):
        return self.approvals.filter(approved_at__isnull=True).exists()

    def approve_if_no_approvals_needed(self, *_):
        if not self.any_approvals_needed():
            self.approve_by_all(system_actor=SystemActor.SYSTEM_LOGIC.value)
            self.save_as_pdf()

    def approve_by_all(self):
        self.order_status = PurchaseOrderStatus.APPROVED
        self.save()
        self.save_as_pdf()

    def trigger_approval_flows(self, *_):
        from didero.workflows.schemas import WorkflowTrigger, WorkflowType
        from didero.workflows.utils import trigger_workflow_if_exists

        workflow_run = trigger_workflow_if_exists(
            WorkflowType.PURCHASE_ORDER_APPROVAL_FLOW,
            WorkflowTrigger.ON_PURCHASE_ORDER_CREATED,
            self.team,
            {
                "default": {
                    "purchase_order_id": self.id,
                }
            },
        )

        if not workflow_run:
            self.approve_by_all()

    # set the total_cost values; does not save the model since it's used in multiple places
    # you will need to decide if you want to save the data or not after using this helper method.
    def set_total_cost_data(self):
        self.total_cost = self.get_total_cost()
        self.total_cost_currency = self.get_total_cost_currency()


auditlog.register(PurchaseOrder)


@receiver(post_save, sender=PurchaseOrder)
def post_purchase_order_creation(sender, instance, created, **kwargs):
    if created:
        # Post to activity stream
        action.send(
            instance.placed_by or instance.team, verb="created", action_object=instance
        )


@receiver(pre_save, sender=PurchaseOrder)
def on_change(sender, instance: PurchaseOrder, **kwargs):
    if instance.pk:
        # this is an update, we can calculate cost with the instance
        instance.set_total_cost_data()


class OrderItem(models.Model):
    purchase_order = models.ForeignKey(
        PurchaseOrder, on_delete=models.CASCADE, related_name="items"
    )
    item = models.ForeignKey(Item, on_delete=models.CASCADE)
    quantity = models.FloatField(default=1.00)
    price = MoneyField(
        max_digits=16,
        decimal_places=8,
        default_currency="USD",
        default=Decimal("0.00000000"),
        validators=[MinMoneyValidator(Decimal(0.00000000))],
    )
    unit_of_measure = models.CharField(
        max_length=30, default="Each"
    )  # e.g. "case". There are too many choices to have a strict list
    units_per_measure = models.FloatField(default=1)  # e.g. 5

    # New consolidated field - inherits interpretation from PurchaseOrder's shipping_terms
    requested_date = models.DateField(
        null=True,
        blank=True,
        help_text="Interpretation inherits from PurchaseOrder's shipping_terms",
    )

    def __str__(self):
        return f"{self.item} x {self.quantity}"


auditlog.register(OrderItem)


class LineItem(models.Model):
    purchase_order = models.ForeignKey(
        PurchaseOrder, on_delete=models.CASCADE, related_name="line_items"
    )
    category = models.CharField(
        max_length=20,
        choices=[(choice.value, choice.name) for choice in LineItemCategoryChoices],
        default=LineItemCategoryChoices.CUSTOM.value,
    )
    description = models.CharField(max_length=255, blank=True, null=True)
    amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="USD",
        default=Decimal("0.00"),
    )

    def __str__(self):
        if self.category == LineItemCategoryChoices.CUSTOM:
            return f"Custom: {self.description}"
        return self.category


auditlog.register(LineItem)


T = TypeVar("T", bound="Shipment")


class ShipmentManager(models.Manager[T]):
    def update_purchase_order_status(self, purchase_order: PurchaseOrder):
        """
        Update the purchase order status based on the status of all shipments.
        This method should be called whenever a shipment is created, updated, or deleted.
        """
        shipments = purchase_order.shipments.all()

        if not shipments.exists():
            logger.info(
                "No shipments found for purchase order",
                purchase_order_id=purchase_order.id,
                po_number=purchase_order.po_number,
            )
            return

        # Count shipments by status
        ship_status_counts = {
            ShipmentStatus.AWAITING_SHIPMENT.value: 0,
            ShipmentStatus.READY_FOR_PICKUP.value: 0,
            ShipmentStatus.SHIPPED.value: 0,
            ShipmentStatus.RECEIVED.value: 0,
            ShipmentStatus.CANCELED.value: 0,
        }

        for shipment in shipments:
            ship_status_counts[shipment.status] += 1

        len_shipments = shipments.count()
        num_received = ship_status_counts[ShipmentStatus.RECEIVED.value]
        num_shipped = ship_status_counts[ShipmentStatus.SHIPPED.value]

        logger.info(
            "Shipment status counts calculated",
            purchase_order_id=purchase_order.id,
            po_number=purchase_order.po_number,
            total_shipments=len_shipments,
            status_counts=ship_status_counts,
            num_received=num_received,
            num_shipped=num_shipped,
        )

        # Calculate if all items are in shipments
        order_item_counts = {
            order_item.id: order_item.quantity
            for order_item in purchase_order.items.all()
        }

        logger.info(
            "Initial order item quantities",
            purchase_order_id=purchase_order.id,
            po_number=purchase_order.po_number,
            order_item_counts=order_item_counts,
        )

        for shipment in shipments:
            for line_item in shipment.line_items.all():
                order_item_counts[line_item.order_item.id] -= line_item.shipped_quantity

        are_all_items_in_shipment = all(cnt <= 0 for cnt in order_item_counts.values())

        logger.info(
            "Order item quantities after shipment deductions",
            purchase_order_id=purchase_order.id,
            po_number=purchase_order.po_number,
            remaining_quantities=order_item_counts,
            are_all_items_in_shipment=are_all_items_in_shipment,
        )

        # Determine the new order status based on shipment statuses
        new_status = None

        # All shipments are RECEIVED
        if num_received == len_shipments and len_shipments > 0:
            # All shipments are received and all items are accounted for
            if are_all_items_in_shipment:
                new_status = PurchaseOrderStatus.RECEIVED.value
                logger.info(
                    "All shipments received and all items accounted for",
                    purchase_order_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                )
            else:
                new_status = PurchaseOrderStatus.PARTIALLY_RECEIVED.value
                logger.info(
                    "All shipments received but not all items accounted for",
                    purchase_order_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                )
        # Some shipments are RECEIVED
        elif num_received > 0:
            new_status = PurchaseOrderStatus.PARTIALLY_RECEIVED.value
            logger.info(
                "Some shipments received",
                purchase_order_id=purchase_order.id,
                po_number=purchase_order.po_number,
                num_received=num_received,
                total_shipments=len_shipments,
            )
        # All shipments are SHIPPED
        elif num_shipped == len_shipments and len_shipments > 0:
            if are_all_items_in_shipment:
                new_status = PurchaseOrderStatus.SHIPPED.value
                logger.info(
                    "All shipments shipped and all items accounted for",
                    purchase_order_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                )
            else:
                new_status = PurchaseOrderStatus.PARTIALLY_SHIPPED.value
                logger.info(
                    "All shipments shipped but not all items accounted for",
                    purchase_order_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                )
        # Some shipments are SHIPPED
        elif num_shipped > 0:
            new_status = PurchaseOrderStatus.PARTIALLY_SHIPPED.value
            logger.info(
                "Some shipments shipped",
                purchase_order_id=purchase_order.id,
                po_number=purchase_order.po_number,
                num_shipped=num_shipped,
                total_shipments=len_shipments,
            )

        # Only update if we have a valid status to transition to and it's different
        if new_status and purchase_order.order_status != new_status:
            logger.info(
                "Updating purchase order status",
                purchase_order_id=purchase_order.id,
                po_number=purchase_order.po_number,
                old_status=purchase_order.order_status,
                new_status=new_status,
            )
            purchase_order.order_status = new_status
            purchase_order.save()
        else:
            logger.info(
                "No status update needed",
                purchase_order_id=purchase_order.id,
                po_number=purchase_order.po_number,
                current_status=purchase_order.order_status,
                calculated_status=new_status,
            )

    def get_shipped_items(
        self, purchase_order: PurchaseOrder
    ) -> Dict[OrderItem, float]:
        """
        Get a mapping of order_item to total shipped quantity for a purchase order.

        Args:
            purchase_order: The purchase order to get shipped items for

        Returns:
            A dictionary mapping OrderItem to total shipped quantity (float)
            where total shipped quantity is the sum of all shipped quantities for that item across all shipments.
        """
        # Get all shipments for the purchase order
        shipments = self.filter(purchase_order=purchase_order)

        # Initialize the result dictionary
        items_shipped: Dict[OrderItem, float] = {}

        # Get all order items for the purchase order to initialize the dictionary
        order_items = purchase_order.items.all()
        for order_item in order_items:
            items_shipped[order_item] = 0.0

        # For each shipment, add up the shipped quantities
        for shipment in shipments:
            for line_item in shipment.line_items.all():
                order_item = line_item.order_item
                items_shipped[order_item] += line_item.shipped_quantity

        return items_shipped

    def get_unshipped_items(
        self, purchase_order: PurchaseOrder
    ) -> list[tuple[str, str, float, float]]:
        logger.info(
            "getting unshipped items",
            purchase_order_id=purchase_order.id,
        )
        shipped_items: Dict[OrderItem, float] = self.get_shipped_items(  # pyright: ignore
            purchase_order
        )
        unshipped_items = []
        for order_item, shipped_quantity in shipped_items.items():
            if shipped_quantity < order_item.quantity:
                unshipped_items.append(
                    (
                        order_item.item.item_number,
                        order_item.item.description,
                        order_item.quantity,
                        shipped_quantity,
                    )
                )
        logger.info(
            "unshipped items retrieved",
            purchase_order_id=purchase_order.id,
            num_unshipped_items=len(unshipped_items),
        )
        return unshipped_items

    # a shipment must have a purchase order, carrier name, shipment date. We create a task to notify the ops team if these are not provided in email
    def create_shipment(
        self,
        purchase_order: Optional[PurchaseOrder] = None,
        line_items: Optional[dict[OrderItem, float]] = None,
        **kwargs,
    ):
        """Create a shipment and update the PO status"""
        try:
            with transaction.atomic():
                shipment = self.create(
                    purchase_order=purchase_order,
                    **kwargs,
                )

                if line_items:
                    for order_item, quantity in line_items.items():
                        ShipmentLineItem.objects.create(
                            shipment=shipment,
                            order_item=order_item,
                            shipped_quantity=quantity,
                        )

                if purchase_order:
                    self.update_purchase_order_status(purchase_order)
                logger.info(f"Shipment created: {shipment.id}")
                return shipment
        except IntegrityError as e:
            logger.error(f"Error creating shipment: {e}")
            raise e

    def bulk_create_shipments(self, purchase_order, shipments_data):
        """
        Bulk create shipments for a purchase order and update the PO status.
        Each shipment_data should contain the shipment fields and line_items data.
        """
        # Delete existing shipments if requested (optional, can be removed if not needed)
        if purchase_order.shipments.exists():
            purchase_order.shipments.all().delete()

        created_shipments = []
        for shipment_data in shipments_data:
            # Remove purchase_order_id if present in serialized data to avoid duplicate purchase_order assignment
            if "purchase_order" in shipment_data:
                shipment_data.pop("purchase_order")
            if "purchase_order_id" in shipment_data:
                shipment_data.pop("purchase_order_id")

            line_items_data = shipment_data.pop("line_items", [])
            shipment = self.create(purchase_order=purchase_order, **shipment_data)

            # Create line items for this shipment
            for line_item_data in line_items_data:
                ShipmentLineItem.objects.create(shipment=shipment, **line_item_data)

            created_shipments.append(shipment)

        # Update PO status based on new shipments
        self.update_purchase_order_status(purchase_order)
        return created_shipments

    def update_shipment(self, shipment_id, **kwargs):
        """Update a shipment and update the PO status"""
        shipment = self.get(pk=shipment_id)
        line_items_data = kwargs.pop("line_items", None)

        # Update the shipment fields
        for key, value in kwargs.items():
            setattr(shipment, key, value)
        shipment.save()

        # Update line items if provided
        if line_items_data is not None:
            # Remove existing line items
            shipment.line_items.all().delete()

            # Create new line items
            for line_item_data in line_items_data:
                ShipmentLineItem.objects.create(shipment=shipment, **line_item_data)

        # Update PO status
        self.update_purchase_order_status(shipment.purchase_order)
        return shipment


class Shipment(BaseModelWithWebsocketPublishing):
    if TYPE_CHECKING:
        from django.db.models import QuerySet

        sync_attempts: QuerySet["ShipmentSync"]

    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name="shipments",
        null=True,
        blank=True,
    )
    carrier = models.CharField(null=True, blank=True)
    carrier_type = models.CharField(
        choices=[(choice.value, choice.name) for choice in CarrierType],
        null=True,
        blank=True,
    )
    tracking_number = models.CharField(null=True, blank=True)
    container_number = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Container number for the shipment",
    )
    bol_number = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Bill of Lading (BOL) number for the shipment",
    )
    shipment_date = models.DateField(null=True, blank=True)
    estimated_delivery_date = models.DateField(null=True, blank=True)
    actual_delivery_date = models.DateField(null=True, blank=True)
    status = models.CharField(
        choices=[(status.value, status.name) for status in ShipmentStatus],
        default=ShipmentStatus.AWAITING_SHIPMENT.value,
    )
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional shipment data like port of departure, port of arrival, etc.",
    )
    team = models.ForeignKey(
        Team,
        on_delete=models.CASCADE,
        related_name="shipments",
        null=True,
        blank=True,
    )

    # ERP sync tracking moved to didero.integrations.models.ShipmentErpSync

    objects: ShipmentManager["Shipment"] = ShipmentManager()

    @property
    def last_successful_sync_at(self):
        """Get the datetime of the last successful sync, or None if no successful sync exists."""
        last_sync = (
            self.sync_attempts.filter(status=ShipmentSyncStatus.SUCCESS.value)
            .order_by("-completed_at")
            .first()
        )
        return last_sync.completed_at if last_sync else None


auditlog.register(Shipment)


@receiver(models.signals.post_delete, sender=Shipment)
def update_po_status_after_shipment_delete(sender, instance, **kwargs):
    """
    When a shipment is deleted, update the purchase order status.
    """
    if instance.purchase_order:
        Shipment.objects.update_purchase_order_status(instance.purchase_order)


class ShipmentLineItem(BaseModelWithWebsocketPublishing):
    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name="line_items",
    )
    order_item = models.ForeignKey(
        OrderItem,
        on_delete=models.CASCADE,
        related_name="shipment_line_items",
    )
    shipped_quantity = models.FloatField()


auditlog.register(ShipmentLineItem)


class ShipmentSync(BaseModelWithWebsocketPublishing):
    """Track individual shipment sync operations for async processing."""

    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name="sync_attempts",
        help_text="The shipment being synced",
    )
    status = models.CharField(
        max_length=20,
        choices=[(choice.value, choice.name) for choice in ShipmentSyncStatus],
        default=ShipmentSyncStatus.IN_PROGRESS.value,
        help_text="Current status of the sync operation",
    )
    started_at = models.DateTimeField(
        default=timezone.now, help_text="When the sync operation started"
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the sync operation completed (success or failure)",
    )
    error_message = models.TextField(
        null=True, blank=True, help_text="Error details if sync failed"
    )
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional data from the sync operation (API responses, etc.)",
    )
    stagehand_job_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Stagehand job ID for async tracking",
    )

    @property
    def team(self):
        """Get team from shipment for websocket publishing."""
        return self.shipment.team

    def mark_success(self, metadata: dict | None = None) -> None:
        """Mark sync as successful with optional metadata."""
        self.status = ShipmentSyncStatus.SUCCESS.value
        self.completed_at = timezone.now()
        if metadata:
            self.metadata.update(metadata)
        self.save()

    def mark_failed(self, error_message: str, metadata: dict | None = None) -> None:
        """Mark sync as failed with error message."""
        self.status = ShipmentSyncStatus.FAILED.value
        self.completed_at = timezone.now()
        self.error_message = error_message
        if metadata:
            self.metadata.update(metadata)
        self.save()

    @property
    def duration_seconds(self):
        """Calculate sync duration in seconds."""
        if self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    @property
    def age_in_hours(self):
        """Calculate how long the sync has been running in hours."""
        now = timezone.now()
        return (now - self.started_at).total_seconds() / 3600

    def is_stale(self, timeout_hours=48):
        """Check if sync has been running longer than the timeout."""
        if self.status != ShipmentSyncStatus.IN_PROGRESS.value:
            return False
        return self.age_in_hours > timeout_hours

    class Meta:
        db_table = "shipment_sync"
        verbose_name = "Shipment Sync"
        verbose_name_plural = "Shipment Syncs"
        ordering = ["-started_at"]
        indexes = [
            models.Index(fields=["shipment", "-started_at"]),
            models.Index(fields=["status"]),
            models.Index(fields=["status", "stagehand_job_id"]),
        ]


# Register with auditlog for tracking
auditlog.register(ShipmentSync)


class Delivery(BaseModelWithWebsocketPublishing):
    purchase_order = models.ForeignKey(
        PurchaseOrder, on_delete=models.CASCADE, related_name="deliveries"
    )
    delivery_status = models.CharField(
        max_length=20,
        choices=[(status.value, status.name) for status in DeliveryStatus],
    )
    received_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True
    )
    receipt_document = models.ForeignKey(
        Document,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="delivery_receipts",
    )
    received_date = models.DateField(default=date.today)

    @property
    def team(self):
        if self._team_cache is None:
            self._team_cache = self.purchase_order.team
        return self._team_cache

    class Meta:
        verbose_name_plural = "Deliveries"

    def __str__(self):
        return (
            f"Delivery for PO {self.purchase_order.po_number} on {self.received_date}"
        )


auditlog.register(Delivery)


class PurchaseOrderComment(BaseComment):
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name="comments",
    )


auditlog.register(PurchaseOrderComment)


class PurchaseOrderWorkflowMilestoneLog(models.Model):
    workflow_run = models.ForeignKey(
        WorkflowRun,
        null=True,
        on_delete=models.SET_NULL,
        related_name="milestone_logs",
    )
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name="workflow_milestone_logs",
    )
    milestone_name = models.CharField(max_length=64)
    timestamp = models.DateTimeField(auto_now_add=True)


auditlog.register(PurchaseOrderWorkflowMilestoneLog)


OAType = TypeVar("OAType", bound="OrderAcknowledgement")


class OAManager(models.Manager[OAType]):
    """
    Manager for Order Acknowledgement operations following ShipmentManager pattern.
    Centralizes all OA business logic for consistency, testability, and reusability.
    """

    def get_items_needing_dates(
        self, oa: "OrderAcknowledgement"
    ) -> models.QuerySet["OrderAcknowledgementItem"]:
        """
        Get OA items that are missing promised ship dates.

        Args:
            oa: The OrderAcknowledgement instance

        Returns:
            QuerySet of OrderAcknowledgementItem objects missing promised_ship_date
        """
        return oa.items.filter(promised_ship_date__isnull=True)

    def create_from_extraction(
        self,
        extraction_data: Any,  # OrderAcknowledgementSchema
        purchase_order: PurchaseOrder,
        source_email: Any,  # Communication
        comparison_result: Any = None,  # DocumentComparisonResult, optional for backward compatibility
    ) -> "OrderAcknowledgement":
        """
        Create OrderAcknowledgement with smart extraction-based logic.

        Scenarios handled:
        1. Store extracted data as-is (no PO items merged until AWAITING_SHIPMENT)
        2. Status = EXTRACTED initially, then PENDING_CLARIFICATION if mismatches
        3. Global dates applied to items missing specific dates
        4. PO items merged later when PO status → AWAITING_SHIPMENT (status becomes COMPLETE/INCOMPLETE)

        Args:
            extraction_data: Pydantic schema with extracted OA data
            purchase_order: The related PurchaseOrder
            source_email: The source Communication for linking
            comparison_result: Optional DocumentComparisonResult for mismatch detection

        Returns:
            Created OrderAcknowledgement instance with extracted data only
        """
        with transaction.atomic():
            # Determine status based on comparison results
            if comparison_result and comparison_result.requires_manual_review:
                # Has mismatches - needs manual review
                oa_status = "PENDING_CLARIFICATION"
            else:
                # No mismatches - start as extracted
                oa_status = "EXTRACTED"

            # Create the OA using minimal approach - store extracted data as-is
            from didero.orders.utils.oa_creation_utils import create_oa_from_extraction

            oa = create_oa_from_extraction(
                oa_data=extraction_data,
                purchase_order=purchase_order,
                source_email=source_email,
                oa_status=oa_status,
            )

            logger.info(
                "Created OA with extracted data only",
                oa_id=oa.pk,
                po_id=purchase_order.pk,
                po_number=purchase_order.po_number,
                oa_status=oa.oa_status,
                total_items=oa.items.count(),
                items_from_extraction=len(extraction_data.order_items)
                if hasattr(extraction_data, "order_items")
                and extraction_data.order_items
                else 0,
                has_comparison_result=comparison_result is not None,
                has_mismatches=comparison_result.requires_manual_review
                if comparison_result
                else False,
            )

            return oa

    def update_with_follow_up_dates(
        self, oa: "OrderAcknowledgement", extracted_dates: Any
    ) -> bool:
        """
        Update incomplete OA with dates from follow-up email.

        Args:
            oa: The OrderAcknowledgement to update
            extracted_dates: FollowUpDateExtraction with new dates

        Returns:
            True if any updates were made, False otherwise
        """
        with transaction.atomic():
            # Track initial status
            initial_status = oa.oa_status

            # Apply the date updates
            items_updated = self._apply_extracted_dates(oa, extracted_dates)

            # Update status based on new data
            if items_updated > 0:
                self.update_oa_status(oa)

            # Log the update
            if oa.oa_status != initial_status:
                logger.info(
                    "OA status changed after follow-up date extraction",
                    oa_id=oa.pk,
                    po_number=oa.purchase_order.po_number,
                    old_status=initial_status,
                    new_status=oa.oa_status,
                )

            # Return True if any meaningful update occurred
            return oa.oa_status == "COMPLETE" or oa.oa_status != initial_status

    def _apply_extracted_dates(
        self, oa: "OrderAcknowledgement", extracted_dates: Any
    ) -> int:
        """
        Apply extracted dates to OA items.

        Args:
            oa: The OrderAcknowledgement to update
            extracted_dates: FollowUpDateExtraction with new dates

        Returns:
            Number of items updated
        """
        from didero.orders.utils.model_creation_utils import parse_date_string

        items_updated = 0
        default_ship_date = None
        default_delivery_date = None

        # Parse default dates if provided
        if extracted_dates.default_ship_date:
            default_ship_date = parse_date_string(extracted_dates.default_ship_date)
        if extracted_dates.default_delivery_date:
            default_delivery_date = parse_date_string(
                extracted_dates.default_delivery_date
            )

        # Update items
        for item in oa.items.all():
            updated = False

            # Use item_number for matching
            item_number = item.item_number

            # Check for item-specific dates first
            item_dates = None
            if item_number and item_number in extracted_dates.item_dates:
                item_dates = extracted_dates.item_dates[item_number]

            # Update ship date
            if not item.promised_ship_date:
                if item_dates and item_dates.promised_ship_date:
                    ship_date = parse_date_string(item_dates.promised_ship_date)
                    if ship_date:
                        item.promised_ship_date = ship_date
                        updated = True
                elif default_ship_date:
                    item.promised_ship_date = default_ship_date
                    updated = True

            # Update delivery date
            if not item.promised_delivery_date:
                if item_dates and item_dates.promised_delivery_date:
                    delivery_date = parse_date_string(item_dates.promised_delivery_date)
                    if delivery_date:
                        item.promised_delivery_date = delivery_date
                        updated = True
                elif default_delivery_date:
                    item.promised_delivery_date = default_delivery_date
                    updated = True

            if updated:
                item.save()
                items_updated += 1

        if items_updated > 0:
            logger.info(
                "Updated OA with dates from follow-up email",
                oa_id=oa.pk,
                po_number=oa.purchase_order.po_number,
                items_updated=items_updated,
                new_status=oa.oa_status,
            )

        return items_updated

    def _ensure_all_po_items_exist(
        self, oa: "OrderAcknowledgement", po: PurchaseOrder
    ) -> None:
        """
        Ensure OA has all items from PO - core data integrity principle.
        This guarantees follow-up workflows always have complete item data.

        Args:
            oa: The OrderAcknowledgement to ensure completeness
            po: The source PurchaseOrder
        """
        # Get existing OA items by item_number
        existing_items = {
            item.item_number: item for item in oa.items.select_related("item").all()
        }

        # Create missing items from PO
        items_to_create = []
        for po_item in po.items.select_related("item").all():
            item_number = po_item.item.item_number if po_item.item else ""

            if item_number and item_number not in existing_items:
                items_to_create.append(
                    OrderAcknowledgementItem(
                        order_acknowledgement=oa,
                        item=po_item.item,
                        item_number=item_number,
                        item_description=po_item.item.description
                        if po_item.item
                        else "",
                        quantity=po_item.quantity,
                        unit_of_measure=po_item.unit_of_measure,
                        unit_price=po_item.price,
                        total_price=Decimal(str(po_item.price))
                        * Decimal(str(po_item.quantity)),
                        # Dates remain null - will be filled by extraction/updates
                        promised_ship_date=None,
                        promised_delivery_date=None,
                    )
                )

        if items_to_create:
            OrderAcknowledgementItem.objects.bulk_create(items_to_create)
            logger.info(
                "Created missing OA items from PO",
                oa_id=oa.pk,
                po_id=po.pk,
                items_created=len(items_to_create),
            )

    def update_oa_status(self, oa: "OrderAcknowledgement") -> None:
        """
        Update OA status based on current state of items.
        Centralizes status determination logic.

        Args:
            oa: The OrderAcknowledgement to update
        """
        logger.info(
            "Starting update_oa_status",
            oa_id=oa.pk,
            current_status=oa.oa_status,
            current_items_count=oa.items.count(),
        )

        # Check if ALL items have promised ship dates
        all_items_have_ship_dates = True
        items_count = 0

        for item in oa.items.all():
            items_count += 1
            if not item.promised_ship_date:
                all_items_have_ship_dates = False
                break

        logger.info(
            "OA status calculation results",
            oa_id=oa.pk,
            items_count=items_count,
            all_items_have_ship_dates=all_items_have_ship_dates,
            has_order_number=bool(oa.order_number),
            order_number=oa.order_number or "None",
        )

        # Determine new status
        old_status = oa.oa_status

        if all_items_have_ship_dates and oa.order_number and items_count > 0:
            oa.oa_status = "COMPLETE"
        elif oa.order_number and items_count > 0:
            oa.oa_status = "INCOMPLETE"
        else:
            oa.oa_status = "PENDING_CLARIFICATION"

        logger.info(
            "OA status determination",
            oa_id=oa.pk,
            old_status=old_status,
            new_status=oa.oa_status,
            status_changed=old_status != oa.oa_status,
        )

        if old_status != oa.oa_status:
            logger.info(
                "Saving OA with new status",
                oa_id=oa.pk,
                old_status=old_status,
                new_status=oa.oa_status,
            )
            oa.save(update_fields=["oa_status"])
            logger.info(
                "OA status updated",
                oa_id=oa.pk,
                po_number=oa.purchase_order.po_number,
                old_status=old_status,
                new_status=oa.oa_status,
                items_with_dates=sum(
                    1 for item in oa.items.all() if item.promised_ship_date
                ),
                total_items=items_count,
            )

    def get_earliest_promised_dates(
        self, oa: "OrderAcknowledgement"
    ) -> Tuple[Optional[date], Optional[date]]:
        """
        Get the earliest promised ship and delivery dates across all items.
        Useful for PO-level tracking and reporting.

        Args:
            oa: The OrderAcknowledgement

        Returns:
            Tuple of (earliest_ship_date, earliest_delivery_date), either can be None
        """
        earliest_ship_date = None
        earliest_delivery_date = None

        for item in oa.items.exclude(
            promised_ship_date__isnull=True, promised_delivery_date__isnull=True
        ):
            if item.promised_ship_date:
                if (
                    not earliest_ship_date
                    or item.promised_ship_date < earliest_ship_date
                ):
                    earliest_ship_date = item.promised_ship_date

            if item.promised_delivery_date:
                if (
                    not earliest_delivery_date
                    or item.promised_delivery_date < earliest_delivery_date
                ):
                    earliest_delivery_date = item.promised_delivery_date

        return earliest_ship_date, earliest_delivery_date

    def create_oa_items_from_po_for_extraction(
        self, purchase_order: PurchaseOrder, oa_data: Any
    ) -> list[Any]:
        """
        Create OrderAcknowledgementSchema items from PO items for extraction.
        Used when dates are found but items weren't extracted.

        Args:
            purchase_order: The PurchaseOrder to get items from
            oa_data: The extracted OA data that has dates but no items

        Returns:
            List of OrderAcknowledgementOrderItem schema objects
        """
        from didero.orders.schemas import OrderAcknowledgementOrderItem

        oa_items = []

        # Get the PO items (using correct related_name)
        po_items = purchase_order.items.all()

        for po_item in po_items:
            # Calculate total price
            total_price = None
            if po_item.price and po_item.quantity:
                total = po_item.price.amount * Decimal(str(po_item.quantity))
                total_price = str(total)

            # Create OA item from PO item with correct field access
            oa_item = OrderAcknowledgementOrderItem(
                item_number=po_item.item.item_number if po_item.item else "",
                item_description=po_item.item.description if po_item.item else "",
                quantity=float(po_item.quantity) if po_item.quantity else 0.0,
                unit_of_measure=po_item.unit_of_measure or "Each",
                unit_price=str(po_item.price.amount) if po_item.price else "0.00",
                total_price=total_price or "0.00",
                is_backorder=False,  # Default to not backorder for PO items
                # Dates will be applied from oa_data during create_oa_from_extraction
                promised_ship_date=None,
                promised_delivery_date=None,
            )
            oa_items.append(oa_item)

        logger.info(
            "Created OA items from PO for extraction",
            po_id=purchase_order.pk,
            po_number=purchase_order.po_number,
            items_created=len(oa_items),
        )

        return oa_items

    def check_oa_exists_for_po(self, purchase_order_id: str) -> bool:
        """
        Check if an Order Acknowledgement exists for the given purchase order.

        Args:
            purchase_order_id: UUID of the purchase order

        Returns:
            bool: True if OA exists, False otherwise
        """
        try:
            oa_exists = self.filter(purchase_order_id=purchase_order_id).exists()

            logger.info(
                "order acknowledgement existence check complete",
                purchase_order_id=purchase_order_id,
                oa_exists=oa_exists,
            )

            return oa_exists

        except Exception as e:
            logger.error(
                "error checking order acknowledgement existence",
                purchase_order_id=purchase_order_id,
                error=str(e),
            )
            return False

    def get_oa_context(self, purchase_order: PurchaseOrder):
        """
        Get Order Acknowledgement context including ship date completeness.

        Args:
            purchase_order: PurchaseOrder instance

        Returns:
            OAContext: OA-specific context with ship date analysis
        """
        from didero.workflows.core_workflows.follow_up.schemas import OAContext

        # Get existing OAs
        existing_oas = list(
            self.filter(purchase_order=purchase_order).prefetch_related("items__item")
        )

        incomplete_items = []
        has_all_ship_dates = True

        # Check if all items in all OAs have ship dates
        for oa in existing_oas:
            for oa_item in oa.items.all():
                if not oa_item.promised_ship_date:
                    has_all_ship_dates = False
                    item_name = (
                        oa_item.item.description
                        if oa_item.item
                        else oa_item.item_description or "Unknown"
                    )
                    incomplete_items.append(item_name)

        return OAContext(
            existing_oas=[
                {
                    "oa_status": oa.oa_status,
                    "created_at": oa.created_at,
                    "items_with_ship_dates": [
                        {
                            "item_name": oa_item.item.description
                            if oa_item.item
                            else oa_item.item_description or "Unknown",
                            "promised_ship_date": oa_item.promised_ship_date,
                            "promised_delivery_date": oa_item.promised_delivery_date,
                            "has_ship_date": bool(oa_item.promised_ship_date),
                        }
                        for oa_item in oa.items.all()
                    ],
                }
                for oa in existing_oas
            ],
            incomplete_items=incomplete_items,
            has_all_ship_dates=has_all_ship_dates,
        )

    def check_oa_ship_dates_complete(self, purchase_order_id: str) -> bool:
        """
        Check if all items in existing OAs have ship dates.

        Args:
            purchase_order_id: UUID of the purchase order

        Returns:
            bool: True if all items have ship dates, False otherwise
        """
        try:
            purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)
            oa_context = self.get_oa_context(purchase_order)
            return oa_context.has_all_ship_dates

        except Exception as e:
            logger.error(
                "error checking OA ship dates completeness",
                purchase_order_id=purchase_order_id,
                error=str(e),
            )
            return False

    def get_unshipped_oa_items_with_ship_dates(
        self,
        purchase_order: PurchaseOrder,
    ) -> List[Tuple["OrderAcknowledgementItem", date, int]]:
        """
        Get OA items that haven't fully shipped yet and have ship dates.

        Args:
            purchase_order: PurchaseOrder instance

        Returns:
            List of tuples: (oa_item, promised_ship_date, unshipped_quantity)
            Only includes items where unshipped_quantity > 0 and promised_ship_date exists.
        """

        unshipped_items = []

        # Get all OA items for this PO
        oa_items = OrderAcknowledgementItem.objects.filter(
            order_acknowledgement__purchase_order=purchase_order,
            promised_ship_date__isnull=False,  # Only items with ship dates
        ).select_related("item", "order_item")

        for oa_item in oa_items:
            # Calculate shipped quantity for this OA item
            shipped_quantity = 0
            if oa_item.order_item:
                # NEW: Direct relationship through order_item
                shipped_quantity = (
                    ShipmentLineItem.objects.filter(
                        order_item=oa_item.order_item,
                    )
                    .aggregate(total_shipped=models.Sum("shipped_quantity"))
                    .get("total_shipped", 0)
                    or 0
                )
            elif oa_item.item:
                # FALLBACK: For existing data without order_item link
                shipped_quantity = (
                    ShipmentLineItem.objects.filter(
                        order_item__item=oa_item.item,
                        order_item__purchase_order=purchase_order,
                    )
                    .aggregate(total_shipped=models.Sum("shipped_quantity"))
                    .get("total_shipped", 0)
                    or 0
                )

            unshipped_quantity = oa_item.quantity - shipped_quantity
            if unshipped_quantity > 0:
                unshipped_items.append(
                    (oa_item, oa_item.promised_ship_date, unshipped_quantity)
                )

        return unshipped_items

    def complete_acknowledgements_for_po(self, purchase_order: PurchaseOrder) -> None:
        """
        Complete order acknowledgements when PO transitions to AWAITING_SHIPMENT.

        Implements the "acknowledge by omission" logic for existing OAs:
        - Copies all PO data to the OA (supplier acknowledges PO as-is)
        - Preserves promised ship/delivery dates from the supplier's acknowledgement
        - Adds any missing PO items to the OA with PO data
        - Updates OA status to COMPLETE or INCOMPLETE based on promised dates

        If no OAs exist, this is normal - not all suppliers send acknowledgements.

        Args:
            purchase_order: The PurchaseOrder transitioning to AWAITING_SHIPMENT
        """
        logger.info(
            "Completing order acknowledgements for PO",
            po_id=purchase_order.id,
            po_number=purchase_order.po_number,
        )

        # Find all OAs for this PO that are still in extracted/pending status
        oas_to_complete = self.filter(
            purchase_order=purchase_order,
            oa_status__in=["EXTRACTED", "PENDING_CLARIFICATION"],
        )

        if not oas_to_complete.exists():
            logger.info(
                "No order acknowledgements to complete - supplier may not have sent acknowledgement",
                po_id=purchase_order.id,
                po_number=purchase_order.po_number,
            )
            return

        for oa in oas_to_complete:
            try:
                with transaction.atomic():
                    # Step 1: Copy PO data to OA (acknowledge by omission)
                    self._copy_po_data_to_oa(oa, purchase_order)

                    # Step 2: Ensure all PO items are represented in the OA
                    self._ensure_all_po_items_in_oa_completion(oa, purchase_order)

                    # Step 3: Update status based on promised date completeness
                    self.update_oa_status(oa)

                    logger.info(
                        "Completed OA with acknowledge-by-omission logic",
                        oa_id=oa.id,
                        po_id=purchase_order.id,
                        po_number=purchase_order.po_number,
                        new_oa_status=oa.oa_status,
                        total_items=oa.items.count(),
                        items_with_promised_dates=oa.items.filter(
                            promised_ship_date__isnull=False
                        ).count(),
                    )

            except Exception as e:
                logger.error(
                    "Failed to complete order acknowledgement",
                    oa_id=oa.id,
                    po_id=purchase_order.id,
                    po_number=purchase_order.po_number,
                    error=str(e),
                )

        completed_count = oas_to_complete.count()
        logger.info(
            "Completed order acknowledgement processing",
            po_id=purchase_order.id,
            po_number=purchase_order.po_number,
            oas_processed=completed_count,
        )

    def _copy_po_data_to_oa(
        self, oa: "OrderAcknowledgement", purchase_order: PurchaseOrder
    ):
        """
        Copy PO data to OA (acknowledge by omission).
        Preserves any existing OA data that was extracted from supplier acknowledgement.
        """
        logger.info(
            "Starting _copy_po_data_to_oa",
            oa_id=oa.pk,
            po_id=purchase_order.pk,
            oa_status=oa.oa_status,
        )

        # Copy PO-level data to OA (only if OA doesn't have it)
        updated_fields = []

        if not oa.shipping_address and purchase_order.shipping_address:
            oa.shipping_address = purchase_order.shipping_address
            updated_fields.append("shipping_address")

        if not oa.shipping_method and purchase_order.shipping_method:
            oa.shipping_method = purchase_order.shipping_method
            updated_fields.append("shipping_method")

        if not oa.shipping_terms and purchase_order.shipping_terms:
            oa.shipping_terms = purchase_order.shipping_terms
            updated_fields.append("shipping_terms")

        if not oa.payment_terms and purchase_order.payment_terms:
            oa.payment_terms = purchase_order.payment_terms
            updated_fields.append("payment_terms")

        # Save OA with copied data if any fields were updated
        if updated_fields:
            logger.info(
                "Saving OA with copied PO data",
                oa_id=oa.pk,
                updated_fields=updated_fields,
            )
            oa.save(update_fields=updated_fields)
        else:
            logger.info(
                "No PO fields to copy to OA",
                oa_id=oa.pk,
            )

        logger.debug(
            "Copied PO data to OA",
            oa_id=oa.id,
            po_id=purchase_order.id,
            updated_fields=updated_fields,
        )

    def _ensure_all_po_items_in_oa_completion(
        self, oa: "OrderAcknowledgement", purchase_order: PurchaseOrder
    ):
        """
        Ensure all PO items are represented in the OA during completion.
        Adds missing PO items with PO data, preserves existing OA items with promised dates.
        Uses clearer variable names: po_item instead of confusing order_item.
        """
        logger.info(
            "Starting _ensure_all_po_items_in_oa_completion",
            oa_id=oa.pk,
            po_id=purchase_order.pk,
            current_oa_items=oa.items.count(),
        )

        # Get existing OA items mapped by their linked PO item (order_item FK)
        existing_oa_items = {}
        for oa_item in oa.items.filter(order_item__isnull=False):
            # NOTE: oa_item.order_item actually refers to a PO item
            existing_oa_items[oa_item.order_item.id] = oa_item

        logger.info(
            "Found existing OA items",
            oa_id=oa.pk,
            existing_oa_items_count=len(existing_oa_items),
            existing_oa_item_ids=list(existing_oa_items.keys()),
        )

        # Get all PO items
        po_items = purchase_order.items.all()
        logger.info(
            "Found PO items to check",
            po_id=purchase_order.pk,
            po_items_count=po_items.count(),
            po_item_ids=[po_item.id for po_item in po_items],
        )

        items_to_create = []
        for po_item in po_items:  # Clear naming: po_item is from PurchaseOrder
            if po_item.id not in existing_oa_items:
                logger.info(
                    "Creating missing OA item from PO item",
                    oa_id=oa.pk,
                    po_item_id=po_item.id,
                    item_number=po_item.item.item_number if po_item.item else "None",
                    quantity=po_item.quantity,
                    price=po_item.price.amount if po_item.price else "None",
                )
                # Add missing PO item to OA with PO data
                items_to_create.append(
                    OrderAcknowledgementItem(
                        order_acknowledgement=oa,
                        order_item=po_item,  # Links back to PO item for traceability
                        item=po_item.item,
                        item_number=po_item.item.item_number if po_item.item else "",
                        item_description=po_item.item.description
                        if po_item.item
                        else "",
                        quantity=po_item.quantity,
                        unit_of_measure=po_item.unit_of_measure,
                        unit_price=po_item.price.amount,
                        total_price=Decimal(str(po_item.quantity))
                        * po_item.price.amount,
                        # Leave promised dates as None - supplier didn't provide them
                        promised_ship_date=None,
                        promised_delivery_date=None,
                    )
                )
            else:
                logger.info(
                    "PO item already exists in OA",
                    oa_id=oa.pk,
                    po_item_id=po_item.id,
                    item_number=po_item.item.item_number if po_item.item else "None",
                )

        if items_to_create:
            logger.info(
                "Creating missing OA items",
                oa_id=oa.pk,
                items_to_create_count=len(items_to_create),
            )
            try:
                OrderAcknowledgementItem.objects.bulk_create(items_to_create)
                logger.info(
                    "Successfully created missing OA items",
                    oa_id=oa.pk,
                    items_created=len(items_to_create),
                )
            except Exception as e:
                logger.error(
                    "Failed to bulk create OA items",
                    oa_id=oa.pk,
                    error=str(e),
                    items_count=len(items_to_create),
                )
                raise
        else:
            logger.info(
                "No missing OA items to create",
                oa_id=oa.pk,
            )

        # Apply global promised dates to items that don't have specific dates
        # Reuse existing function from oa_creation_utils
        from didero.orders.utils.oa_creation_utils import _apply_global_dates_to_items

        logger.info(
            "Applying global dates to OA items",
            oa_id=oa.pk,
        )
        _apply_global_dates_to_items(oa)

        logger.info(
            "Ensured all PO items in OA during completion",
            oa_id=oa.id,
            po_id=purchase_order.id,
            existing_oa_items=len(existing_oa_items),
            total_po_items=po_items.count(),
            items_added=len(items_to_create),
        )


class OrderAcknowledgement(BaseModelWithWebsocketPublishing):
    """
    Represents an order acknowledgement.
    Each order acknowledgement is always related to a purchase order.
    """

    # Core identifiers
    purchase_order = models.ForeignKey["PurchaseOrder"](
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name="order_acknowledgements",
        help_text="The purchase order this acknowledgement is for",
    )
    order_number = models.CharField(
        max_length=255, db_index=True, null=True
    )  # Supplier's order/sales order number

    # Reverse relationships (for type checking)
    items: models.QuerySet["OrderAcknowledgementItem"]
    line_items: models.QuerySet["OrderAcknowledgmentLineItem"]

    # Address information
    shipping_address = models.ForeignKey["Address"](
        Address,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="order_acknowledgements",
    )

    # Shipping and payment details
    shipping_method = models.CharField(max_length=100, null=True, blank=True)
    shipping_terms = models.CharField(max_length=100, null=True, blank=True)
    payment_terms = models.CharField(max_length=100, null=True, blank=True)

    # Additional information
    notes = models.TextField(null=True, blank=True)
    special_instructions = models.TextField(null=True, blank=True)

    # Global promised dates (applied to items that don't have specific dates)
    global_promised_ship_date = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Global ship date promised by supplier for ALL items when no item-specific dates are provided (YYYY-MM-DD)",
    )
    global_promised_delivery_date = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Global delivery date promised by supplier for ALL items when no item-specific dates are provided (YYYY-MM-DD)",
    )

    # Follow-up workflow fields moved to OrderAcknowledgementItem level for better granularity

    # OA Status for workflow control
    oa_status = models.CharField(
        max_length=30,
        choices=[
            ("EXTRACTED", "Extracted - data extracted from supplier response"),
            ("COMPLETE", "Complete - has all needed info"),
            ("INCOMPLETE", "Incomplete - missing item ship dates"),
            (
                "PENDING_CLARIFICATION",
                "Pending clarification - validation issues detected",
            ),
            ("REJECTED", "Rejected - supplier cannot fulfill order"),
        ],
        default="EXTRACTED",
        help_text="Status of this order acknowledgment",
    )

    objects: OAManager["OrderAcknowledgement"] = OAManager()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["purchase_order", "order_number"],
                name="unique_oa_per_po_and_order_number",
                condition=models.Q(order_number__isnull=False),
            )
        ]
        indexes = [
            models.Index(fields=["oa_status"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["purchase_order", "order_number"]),
        ]

    def __str__(self):
        return f"Order Acknowledgement - PO: {self.purchase_order.po_number}, Order: {self.order_number}"


auditlog.register(OrderAcknowledgement)


class OrderAcknowledgmentLineItem(models.Model):
    order_acknowledgement = models.ForeignKey(
        OrderAcknowledgement, on_delete=models.CASCADE, related_name="line_items"
    )
    category = models.CharField(
        max_length=20,
        choices=[(choice.value, choice.name) for choice in LineItemCategoryChoices],
        default=LineItemCategoryChoices.CUSTOM.value,
    )
    description = models.CharField(max_length=255, blank=True, null=True)
    amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="USD",
        default=Decimal("0.00"),
    )

    def __str__(self):
        if self.category == LineItemCategoryChoices.CUSTOM:
            return f"Custom: {self.description}"
        return self.category


auditlog.register(OrderAcknowledgmentLineItem)


class OrderAcknowledgementItem(models.Model):
    # Core relationships
    order_acknowledgement = models.ForeignKey(
        OrderAcknowledgement, on_delete=models.CASCADE, related_name="items"
    )

    # NEW: Direct link to OrderItem for proper traceability
    # NOTE: This field actually refers to a PO Order item (OrderItem from PurchaseOrder)
    order_item = models.ForeignKey(
        "OrderItem",
        on_delete=models.CASCADE,
        related_name="acknowledgement_items",
        null=True,  # Initially nullable for migration
        blank=True,
        help_text="The PO line item this acknowledgement is for",
    )

    # DEPRECATED: Will be removed after migration to order_item
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name="order_acknowledgement_items",
        null=True,
        blank=True,
    )

    # NOTE: We store supplier-provided data even though we have order_item relationship because:
    # 1. Supplier might use different item numbers/descriptions than our catalog
    # 2. Need to capture exactly what supplier acknowledged for audit trail
    # 3. Acknowledged quantity/price might differ from what was ordered
    # 4. Supplier might acknowledge items not in our original PO
    item_number = models.CharField(max_length=100)  # Supplier's item number
    item_description = models.TextField()  # Supplier's description
    quantity = models.FloatField()  # Acknowledged quantity (may differ from ordered)
    unit_of_measure = models.CharField(max_length=50)  # Supplier's UOM

    # Pricing information - supplier's confirmed prices (may differ from PO)
    unit_price = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
    )
    total_price = MoneyField(
        max_digits=14,
        decimal_places=8,
        default_currency=Currency("USD"),
        default=Decimal("0.00"),
    )

    # Follow-up workflow fields - critical for tracking shipments
    promised_ship_date = models.DateField(
        null=True,
        blank=True,
        help_text="Ship date promised by supplier for this specific item",
    )

    promised_delivery_date = models.DateField(
        null=True,
        blank=True,
        help_text="Delivery date promised by supplier for this specific item",
    )

    class Meta:
        indexes = [
            models.Index(fields=["promised_ship_date"]),
            models.Index(fields=["promised_delivery_date"]),
            models.Index(fields=["order_acknowledgement", "item_number"]),
        ]

    def __str__(self):
        return f"{self.item_number} - {self.item_description} x {self.quantity}"


auditlog.register(OrderAcknowledgementItem)


class POEditReason(models.Model):
    """
    Represents the reasons for editing a Purchase Order.
    Linked to a Purchase Order and stores details about the edit.
    """

    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name="edit_reasons",
        help_text="The purchase order this edit reason is associated with",
    )
    reason = models.TextField(help_text="The reason for editing the purchase order")
    created_at = models.DateTimeField(auto_now_add=True, help_text="Date of creation")
    updated_at = models.DateTimeField(auto_now=True, help_text="Last updated date")
    requested_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,  # Keeps the record even if the user is deleted
        null=True,
        blank=True,
        help_text="The user who requested the edit",
    )

    def __str__(self):
        return f"Edit Reason for PO {self.purchase_order.po_number}: {self.reason}"


auditlog.register(POEditReason)
