"""
Utility functions for notifications in workflows
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from didero.orders.models import PurchaseOrder
    from didero.users.models.team_models import Team
    from didero.users.models.user_models import User

from didero.utils.utils import get_didero_ai_user


def get_notification_recipient(
    purchase_order: "PurchaseOrder", team: Optional["Team"] = None
) -> Optional["User"]:
    """
    Determine the appropriate recipient for workflow notifications based on priority.

    Priority order:
    1. Purchase order's placed_by user (if available)
    2. Team's default_requestor (if available)
    3. Team's AI user as a fallback

    Args:
        purchase_order: The PurchaseOrder instance
        team: Optional Team instance (will use purchase_order.team if not provided)

    Returns:
        User instance to receive the notification, or None if no appropriate user found
    """
    # Use purchase_order's team if team not provided
    team = team or purchase_order.team

    # Priority 1: Purchase order's placed_by
    if purchase_order.placed_by:
        return purchase_order.placed_by

    # Priority 2: Team's default_requestor
    if team and hasattr(team, "default_requestor") and team.default_requestor:
        return team.default_requestor

    # Priority 3: AI user as fallback
    return get_didero_ai_user(team=team)
