"""
Temporal workflow error handling utilities.

This module provides error classification and handling patterns for Temporal workflows,
enabling proper retry behavior and error propagation.
"""

from enum import Enum
from typing import Optional

from temporalio.exceptions import ApplicationError


class ErrorCategory(Enum):
    """Categories of errors for proper retry behavior."""

    # Non-retryable business logic errors
    VALIDATION_FAILED = "validation_failed"
    DUPLICATE_RESOURCE = "duplicate_resource"
    BUSINESS_RULE_VIOLATION = "business_rule_violation"
    INSUFFICIENT_PERMISSIONS = "insufficient_permissions"

    # Non-retryable resource errors
    RESOURCE_NOT_FOUND = "resource_not_found"
    INVALID_STATE = "invalid_state"
    CONFIGURATION_ERROR = "configuration_error"

    # Retryable transient errors (should be raised as regular exceptions)
    NETWORK_ERROR = "network_error"
    DATABASE_TIMEOUT = "database_timeout"
    EXTERNAL_SERVICE_ERROR = "external_service_error"
    TEMPORARY_UNAVAILABLE = "temporary_unavailable"


def raise_non_retryable_error(
    message: str,
    category: Error<PERSON>ategory,
    details: Optional[dict] = None,
) -> None:
    """
    Raise a non-retryable ApplicationError with proper categorization.

    Args:
        message: Human-readable error message
        category: The error category for classification
        details: Optional additional error details

    Raises:
        ApplicationError: Always raises this error type
    """
    error_details = {
        "category": category.value,
        "message": message,
    }
    if details:
        error_details.update(details)

    raise ApplicationError(
        message,
        error_details,
        non_retryable=True,
        type=category.value,
    )


def is_retryable_error(error: Exception) -> bool:
    """
    Determine if an error should be retried by Temporal.

    Args:
        error: The exception to check

    Returns:
        bool: True if the error should be retried, False otherwise
    """
    # ApplicationErrors are non-retryable by definition when non_retryable=True
    if isinstance(error, ApplicationError):
        return False

    # Common retryable error types
    retryable_error_names = {
        "ConnectionError",
        "TimeoutError",
        "OperationalError",  # Django DB errors
        "InterfaceError",  # Django DB connection errors
        "RequestException",  # Requests library errors
        "HTTPError",  # HTTP 5xx errors might be retryable
    }

    error_type_name = type(error).__name__
    return error_type_name in retryable_error_names


# Specific error classes for common scenarios
class WorkflowBusinessError(ApplicationError):
    """Base class for non-retryable business logic errors in workflows."""

    def __init__(self, message: str, category: ErrorCategory, **details):
        super().__init__(
            message,
            {"category": category.value, "message": message, **details},
            non_retryable=True,
            type=category.value,
        )


class ValidationError(WorkflowBusinessError):
    """Raised when input validation fails."""

    def __init__(self, message: str, field: Optional[str] = None, **details):
        extra_details = {"field": field} if field else {}
        extra_details.update(details)
        super().__init__(
            message,
            ErrorCategory.VALIDATION_FAILED,
            **extra_details,
        )


class ResourceNotFoundError(WorkflowBusinessError):
    """Raised when a required resource cannot be found."""

    def __init__(self, resource_type: str, resource_id: str, **details):
        # Allow custom message override
        custom_message = details.pop("message", None)
        message = custom_message or f"{resource_type} with id '{resource_id}' not found"
        super().__init__(
            message,
            ErrorCategory.RESOURCE_NOT_FOUND,
            resource_type=resource_type,
            resource_id=resource_id,
            **details,
        )


class DuplicateResourceError(WorkflowBusinessError):
    """Raised when attempting to create a resource that already exists."""

    def __init__(self, resource_type: str, identifier: str, **details):
        message = f"{resource_type} with identifier '{identifier}' already exists"
        super().__init__(
            message,
            ErrorCategory.DUPLICATE_RESOURCE,
            resource_type=resource_type,
            identifier=identifier,
            **details,
        )


class BusinessRuleViolationError(WorkflowBusinessError):
    """Raised when a business rule is violated."""

    def __init__(self, rule: str, message: str, **details):
        super().__init__(
            message,
            ErrorCategory.BUSINESS_RULE_VIOLATION,
            rule=rule,
            **details,
        )
