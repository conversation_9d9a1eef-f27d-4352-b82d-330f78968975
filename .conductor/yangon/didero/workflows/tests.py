from didero.testing.cases import TestCase
from didero.workflows.core.user_workflow_definitions import WorkflowGraph
from didero.workflows.schemas import WorkflowTrigger, WorkflowType
from didero.workflows.utils import create_workflow


class TestCreateWorkflows(TestCase):
    def setUp(self):
        super().setUp()
        user = self.create_user()
        self.auth_as_user(user)
        self.team = user.teams.first()

    def testCreateWorkflow(self):
        workflow = create_workflow(
            WorkflowType.PURCHASE_ORDER_APPROVAL_FLOW.value,
            WorkflowTrigger.NULL_TRIGGER.value,
            self.team.id,
            {},
            {},
        )
        self.assertEqual(
            workflow.workflow_type, WorkflowType.PURCHASE_ORDER_APPROVAL_FLOW.value
        )
        self.assertEqual(workflow.current_snapshot.workflow, workflow)


class TestWorkflowGraph(TestCase):
    def testCreateWorkflowGraph(self):
        schema = {
            "nodes": [
                {
                    "id": "A",
                    "node_class": "noop",
                    "type": "action",
                    "params": {},
                    "successors": ["B"],
                },
                {
                    "id": "B",
                    "node_class": "noop",
                    "type": "action",
                    "params": {},
                },
            ]
        }
        graph = WorkflowGraph(schema, {})
        self.assertEqual(graph.nodes[0].id, "A")
        self.assertEqual(graph.nodes[1].id, "B")
        self.assertEqual(graph.nodes[1].dep_count, 1)
