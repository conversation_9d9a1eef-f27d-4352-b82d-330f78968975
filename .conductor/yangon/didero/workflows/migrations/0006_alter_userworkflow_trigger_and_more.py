# Generated by Django 4.2.7 on 2024-11-21 22:22

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflows", "0005_alter_userworkflow_trigger"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="userworkflow",
            name="trigger",
            field=models.CharField(
                choices=[
                    ("", "NULL_TRIGGER"),
                    (
                        "on_purchase_order_email_received",
                        "ON_PURCHASE_ORDER_EMAIL_RECEIVED",
                    ),
                    (
                        "on_send_purchase_order_to_supplier",
                        "ON_SEND_PURCHASE_ORDER_TO_SUPPLIER",
                    ),
                ],
                max_length=64,
            ),
        ),
        migrations.AlterField(
            model_name="userworkflow",
            name="workflow_type",
            field=models.CharField(
                choices=[
                    ("purchase_order_approval_flow", "PURCHASE_ORDER_APPROVAL_FLOW"),
                    ("purchase_order_to_shipped", "PURCHASE_ORDER_TO_SHIPPED"),
                    (
                        "purchase_order_supplier_acknowledgement",
                        "PURCHASE_ORDER_SUPPLIER_ACKNOWLEDGEMENT",
                    ),
                ],
                max_length=64,
            ),
        ),
    ]
