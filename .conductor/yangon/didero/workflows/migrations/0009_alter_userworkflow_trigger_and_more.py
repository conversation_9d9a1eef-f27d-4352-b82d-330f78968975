# Generated by Django 4.2.7 on 2025-01-21 19:55

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflows", "0008_alter_workflowrun_temporal_id"),
    ]

    operations = [
        migrations.AlterField(
            model_name="userworkflow",
            name="trigger",
            field=models.CharField(
                choices=[
                    ("", "NULL_TRIGGER"),
                    ("on_purchase_order_created", "ON_PURCHASE_ORDER_CREATED"),
                    (
                        "on_order_shipped_email_received",
                        "ON_ORDER_SHIPPED_EMAIL_RECEIVED",
                    ),
                    (
                        "on_send_purchase_order_to_supplier",
                        "ON_SEND_PURCHASE_ORDER_TO_SUPPLIER",
                    ),
                ],
                max_length=64,
            ),
        ),
        migrations.AlterField(
            model_name="userworkflow",
            name="workflow_type",
            field=models.CharField(
                choices=[
                    ("purchase_order_approval_flow", "PURCHASE_ORDER_APPROVAL_FLOW"),
                    ("purchase_order_shipped", "PURCHASE_ORDER_SHIPPED"),
                    (
                        "purchase_order_supplier_acknowledgement",
                        "PURCHASE_ORDER_SUPPLIER_ACKNOWLEDGEMENT",
                    ),
                ],
                max_length=64,
            ),
        ),
    ]
