# Generated by Django 4.2.7 on 2025-06-24 14:10

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflows", "0014_alter_userworkflow_trigger"),
    ]

    operations = [
        migrations.AlterField(
            model_name="userworkflow",
            name="trigger",
            field=models.CharField(
                choices=[
                    ("", "NULL_TRIGGER"),
                    ("on_purchase_order_created", "ON_PURCHASE_ORDER_CREATED"),
                    (
                        "on_order_shipped_email_received",
                        "ON_ORDER_SHIPPED_EMAIL_RECEIVED",
                    ),
                    (
                        "on_send_purchase_order_to_supplier",
                        "ON_SEND_PURCHASE_ORDER_TO_SUPPLIER",
                    ),
                    (
                        "on_purchase_order_acknowledgement_email_received",
                        "ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED",
                    ),
                    (
                        "on_purchase_order_email_received",
                        "ON_PURCHASE_ORDER_EMAIL_RECEIVED",
                    ),
                    ("on_purchase_order_follow_up", "ON_PURCHASE_ORDER_FOLLOW_UP"),
                    ("on_followup_workflow_trigger", "ON_FOLLOWUP_WORKFLOW_TRIGGER"),
                    ("on_invoice_email_received", "ON_INVOICE_EMAIL_RECEIVED"),
                ],
                help_text="DEPRECATED: This field is redundant as each workflow_type has a 1:1 mapping with a trigger. Will be removed in future versions.",
                max_length=64,
            ),
        ),
        migrations.AlterField(
            model_name="userworkflow",
            name="workflow_type",
            field=models.CharField(
                choices=[
                    ("purchase_order_approval_flow", "PURCHASE_ORDER_APPROVAL_FLOW"),
                    ("purchase_order_shipped", "PURCHASE_ORDER_SHIPPED"),
                    (
                        "purchase_order_supplier_acknowledgement",
                        "PURCHASE_ORDER_SUPPLIER_ACKNOWLEDGEMENT",
                    ),
                    (
                        "purchase_order_acknowledgement",
                        "PURCHASE_ORDER_ACKNOWLEDGEMENT",
                    ),
                    ("purchase_order_creation", "PURCHASE_ORDER_CREATION"),
                    ("purchase_order_follow_up", "PURCHASE_ORDER_FOLLOW_UP"),
                    ("followup_workflow", "FOLLOWUP_WORKFLOW"),
                    ("invoice_processing", "INVOICE_PROCESSING"),
                ],
                max_length=64,
            ),
        ),
    ]
