# Generated by Django 4.2.7 on 2024-11-05 18:30

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("users", "0031_alter_teamsetting_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserWorkflow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("color", models.CharField(max_length=6, null=True)),
                (
                    "workflow_type",
                    models.CharField(
                        choices=[
                            (
                                "purchase_order_approval_flow",
                                "PURCHASE_ORDER_APPROVAL_FLOW",
                            )
                        ],
                        max_length=64,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="WorkflowRun",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uuid",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                ("temporal_id", models.CharField(max_length=255)),
                ("state", models.JSONField()),
                ("params", models.JSONField()),
                ("start_time", models.DateTimeField(null=True)),
                ("end_time", models.DateTimeField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name="WorkflowSnapshot",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uuid",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                ("graph", models.JSONField()),
                ("params", models.JSONField()),
                (
                    "workflow",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="workflows.userworkflow",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="userworkflow",
            name="current_snapshot",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="stages_current_to",
                to="workflows.workflowsnapshot",
            ),
        ),
        migrations.AddField(
            model_name="userworkflow",
            name="team",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="users.team"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userworkflow",
            unique_together={("workflow_type", "team")},
        ),
    ]
