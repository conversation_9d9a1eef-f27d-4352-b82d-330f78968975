# Generated by Django 4.2.7 on 2024-11-05 20:18

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflows", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="userworkflow",
            name="color",
        ),
        migrations.RemoveField(
            model_name="userworkflow",
            name="name",
        ),
        migrations.AddField(
            model_name="workflowsnapshot",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddIndex(
            model_name="userworkflow",
            index=models.Index(
                fields=["workflow_type", "team"], name="workflows_u_workflo_b47cc8_idx"
            ),
        ),
    ]
