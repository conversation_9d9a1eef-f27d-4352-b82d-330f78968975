# Generated by Django 4.2.7 on 2024-11-12 22:17

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0032_team_is_demo_team_user_is_demo_user"),
        ("workflows", "0003_workflowrun_snapshot"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="userworkflow",
            name="workflows_u_workflo_b47cc8_idx",
        ),
        migrations.AlterUniqueTogether(
            name="userworkflow",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="userworkflow",
            name="trigger",
            field=models.CharField(
                choices=[
                    (
                        "on_purchase_order_email_received",
                        "ON_PURCHASE_ORDER_EMAIL_RECEIVED",
                    )
                ],
                default="",
                max_length=64,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="userworkflow",
            name="workflow_type",
            field=models.Char<PERSON>ield(
                choices=[
                    ("purchase_order_approval_flow", "PURCHASE_ORDER_APPROVAL_FLOW"),
                    ("purchase_order_to_shipped", "PURCHASE_ORDER_TO_SHIPPED"),
                ],
                max_length=64,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userworkflow",
            unique_together={("workflow_type", "trigger", "team")},
        ),
        migrations.AddIndex(
            model_name="userworkflow",
            index=models.Index(
                fields=["workflow_type", "trigger", "team"],
                name="workflows_u_workflo_e5693d_idx",
            ),
        ),
    ]
