from datetime import date, datetime, timedelta
from unittest.mock import patch

from django.test import TestCase

from didero.workflows.core_workflows.follow_up.utils import (
    calculate_shipment_followup_dates,
    group_items_by_ship_date,
)


class TestFollowUpUtils(TestCase):
    """Test cases for follow-up utility functions."""

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_aggressive_strategy(self, mock_date):
        """Test follow-up dates with aggressive strategy."""
        # Mock today to be before our test dates
        mock_date.today.return_value = date(2024, 1, 5)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2024, 1, 20)
        oa_received = datetime(2024, 1, 5, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="aggressive",
            start_date=oa_received,
            business_hours_only=False,  # Disable for predictable testing
        )

        # With aggressive strategy and 15-day window, should get multiple follow-ups
        self.assertGreater(len(dates), 2)
        self.assertLessEqual(len(dates), 5)  # Max 5 for aggressive

        # All dates should be between start and end
        for d in dates:
            self.assertGreaterEqual(d, date(2024, 1, 6))  # After OA + 24h
            self.assertLess(d, ship_date)

        # Dates should be sorted
        self.assertEqual(dates, sorted(dates))

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_balanced_strategy(self, mock_date):
        """Test follow-up dates with balanced strategy."""
        # Mock today to be before our test dates
        mock_date.today.return_value = date(2024, 1, 5)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2024, 1, 20)
        oa_received = datetime(2024, 1, 5, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="balanced",
            start_date=oa_received,
            business_hours_only=False,
        )

        # Balanced strategy should have moderate number of follow-ups
        self.assertGreaterEqual(len(dates), 2)
        self.assertLessEqual(len(dates), 4)

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_conservative_strategy(self, mock_date):
        """Test follow-up dates with conservative strategy."""
        # Mock today to be before our test dates
        mock_date.today.return_value = date(2024, 1, 5)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2024, 1, 20)
        oa_received = datetime(2024, 1, 5, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="conservative",
            start_date=oa_received,
            business_hours_only=False,
        )

        # Conservative strategy should have fewer follow-ups
        self.assertGreaterEqual(len(dates), 2)
        self.assertLessEqual(len(dates), 3)

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_unknown_strategy(self, mock_date):
        """Test that unknown strategy defaults to balanced."""
        # Mock today to be before our test dates
        mock_date.today.return_value = date(2024, 1, 5)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2024, 1, 20)
        oa_received = datetime(2024, 1, 5, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="unknown_strategy",
            start_date=oa_received,
            business_hours_only=False,
        )

        # Should still return valid dates (using balanced as default)
        self.assertGreater(len(dates), 0)

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_short_window(self, mock_date):
        """Test follow-up dates with very short window."""
        # Mock today to be before our test dates
        mock_date.today.return_value = date(2024, 1, 5)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2024, 1, 8)
        oa_received = datetime(2024, 1, 5, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="aggressive",
            start_date=oa_received,
            business_hours_only=False,
        )

        # With only 2-3 days window, should get limited follow-ups
        self.assertGreater(len(dates), 0)
        self.assertLessEqual(len(dates), 2)

        # Last date should be day before ship date
        if dates:
            self.assertEqual(dates[-1], date(2024, 1, 7))

    def test_calculate_followup_dates_minimal_window(self):
        """Test follow-up dates when there's exactly one day available."""
        # Ship date gives exactly 1 day window after 24h wait
        ship_date = date(2024, 1, 7)
        oa_received = datetime(2024, 1, 5, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="aggressive",
            start_date=oa_received,
            business_hours_only=False,
        )

        # Should get exactly one follow-up on the available day
        self.assertEqual(len(dates), 1)
        self.assertEqual(dates[0], date(2024, 1, 6))

    def test_calculate_followup_dates_no_window(self):
        """Test follow-up dates when ship date is truly too close."""
        # Ship date is before the wait period ends
        ship_date = date(2024, 1, 6)
        oa_received = datetime(2024, 1, 5, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="aggressive",
            start_date=oa_received,
            initial_wait_hours=24,
            business_hours_only=False,
        )

        # start_day would be 2024-01-06, end_day would be 2024-01-05
        # Since start_day > end_day, no follow-ups possible
        self.assertEqual(len(dates), 0)

    def test_calculate_followup_dates_past_ship_date(self):
        """Test follow-up dates when ship date is in the past."""
        ship_date = date(2024, 1, 5)
        oa_received = datetime(2024, 1, 10, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="aggressive",
            start_date=oa_received,
            business_hours_only=False,
        )

        # No follow-ups possible for past ship dates
        self.assertEqual(len(dates), 0)

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_initial_wait(self, mock_date):
        """Test that initial wait hours are respected."""
        mock_date.today.return_value = date(2024, 1, 5)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2024, 1, 20)
        oa_received = datetime(2024, 1, 5, 10, 0)

        # Test with 48 hour initial wait
        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="aggressive",
            start_date=oa_received,
            initial_wait_hours=48,
            business_hours_only=False,
        )

        # First follow-up should be at least 48 hours after OA
        if dates:
            self.assertGreaterEqual(dates[0], date(2024, 1, 7))

    def test_calculate_followup_dates_business_hours_enabled(self):
        """Test follow-up dates with business hours enabled."""
        ship_date = date(2024, 1, 20)  # Saturday
        oa_received = datetime(2024, 1, 5, 10, 0)  # Friday

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="aggressive",
            start_date=oa_received,
            business_hours_only=True,
        )

        # All dates should be business days
        for d in dates:
            # Check it's not a weekend
            self.assertLess(d.weekday(), 5, f"{d} is a weekend day")

    def test_calculate_followup_dates_immediate_followup(self):
        """Test when immediate follow-up is enabled (no initial wait)."""
        ship_date = date(2024, 1, 8)
        oa_received = datetime(2024, 1, 6, 10, 0)

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="conservative",
            start_date=oa_received,
            business_hours_only=False,
            initial_wait_hours=0,  # No wait means immediate follow-up
        )

        # Should get follow-ups starting immediately (same day as OA) through day before ship
        if dates:
            self.assertEqual(len(dates), 2)
            self.assertEqual(dates[0], date(2024, 1, 6))  # Immediate follow-up
            self.assertEqual(dates[1], date(2024, 1, 7))  # Day before ship

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_ship_date_tomorrow(self, mock_date):
        """Test follow-up when ship date is tomorrow (our real-world scenario)."""
        # Mock today to be 2025-08-01
        mock_date.today.return_value = date(2025, 8, 1)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2025, 8, 2)  # Tomorrow
        oa_received = datetime(2025, 7, 21, 20, 15)  # 2 weeks ago

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="balanced",
            start_date=oa_received,
            initial_wait_hours=24,
            business_hours_only=False,
        )

        # Should get follow-up dates including today (1 day before ship date)
        self.assertGreater(len(dates), 0)
        self.assertIn(date(2025, 8, 1), dates)  # Today should be included

        # All dates should be before ship date
        for d in dates:
            self.assertLess(d, ship_date)

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_oa_today_ship_tomorrow(self, mock_date):
        """Test follow-up when OA received today and ship date is tomorrow."""
        # Mock today to be 2025-08-01
        mock_date.today.return_value = date(2025, 8, 1)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2025, 8, 2)  # Tomorrow
        oa_received = datetime(2025, 8, 1, 10, 0)  # Today

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="balanced",
            start_date=oa_received,
            initial_wait_hours=24,  # Standard 24-hour wait
            business_hours_only=False,
        )

        # With 24h wait from today, we can start tomorrow (2025-08-02)
        # But end_day is also tomorrow (1 day before ship date 2025-08-03)
        # So start_day == end_day, giving us exactly 1 follow-up day

        # if ship_date is 2025-08-02, end_day is 2025-08-01 (today)
        # And start_day after 24h wait is 2025-08-02 (tomorrow)
        # Since start_day > end_day, no follow-ups are possible
        self.assertEqual(len(dates), 0)  # No follow-ups possible with 24h wait

    @patch("didero.workflows.core_workflows.follow_up.utils.date")
    def test_calculate_followup_dates_oa_today_ship_tomorrow_no_wait(self, mock_date):
        """Test immediate follow-up when OA received today, ship tomorrow, no wait."""
        # Mock today to be 2025-08-01
        mock_date.today.return_value = date(2025, 8, 1)
        mock_date.side_effect = lambda *args, **kwargs: date(*args, **kwargs)

        ship_date = date(2025, 8, 2)  # Tomorrow
        oa_received = datetime(2025, 8, 1, 10, 0)  # Today

        dates = calculate_shipment_followup_dates(
            ship_date=ship_date,
            strategy="balanced",
            start_date=oa_received,
            initial_wait_hours=0,  # No wait - immediate follow-up
            business_hours_only=False,
        )

        # Should get follow-up today (immediate)
        self.assertGreater(len(dates), 0)
        self.assertIn(date(2025, 8, 1), dates)  # Today should be included

        # All dates should be before ship date
        for d in dates:
            self.assertLess(d, ship_date)

    def test_group_items_by_ship_date_single_group(self):
        """Test grouping items when all have same ship date."""
        # Mock OA items with same ship date
        items = [
            (MockOAItem(1), date(2024, 1, 15), 10),
            (MockOAItem(2), date(2024, 1, 15), 20),
            (MockOAItem(3), date(2024, 1, 15), 30),
        ]

        groups = group_items_by_ship_date(items)

        self.assertEqual(len(groups), 1)
        self.assertEqual(len(groups[0]), 3)

    def test_group_items_by_ship_date_within_tolerance(self):
        """Test grouping items within tolerance window."""
        # Items within 3 days of each other
        items = [
            (MockOAItem(1), date(2024, 1, 15), 10),
            (MockOAItem(2), date(2024, 1, 16), 20),
            (MockOAItem(3), date(2024, 1, 17), 30),
        ]

        groups = group_items_by_ship_date(items, tolerance_days=3)

        # Should be grouped together
        self.assertEqual(len(groups), 1)
        self.assertEqual(len(groups[0]), 3)

    def test_group_items_by_ship_date_beyond_tolerance(self):
        """Test grouping items beyond tolerance window."""
        # Items more than 3 days apart
        items = [
            (MockOAItem(1), date(2024, 1, 15), 10),
            (MockOAItem(2), date(2024, 1, 20), 20),
            (MockOAItem(3), date(2024, 1, 25), 30),
        ]

        groups = group_items_by_ship_date(items, tolerance_days=3)

        # Should be separate groups
        self.assertEqual(len(groups), 3)
        for group in groups:
            self.assertEqual(len(group), 1)

    def test_group_items_by_ship_date_mixed(self):
        """Test grouping with mixed dates."""
        items = [
            (MockOAItem(1), date(2024, 1, 15), 10),
            (MockOAItem(2), date(2024, 1, 16), 20),
            (MockOAItem(3), date(2024, 1, 20), 30),
            (MockOAItem(4), date(2024, 1, 21), 40),
        ]

        groups = group_items_by_ship_date(items, tolerance_days=2)

        # Should have 2 groups
        self.assertEqual(len(groups), 2)
        self.assertEqual(len(groups[0]), 2)  # Jan 15-16
        self.assertEqual(len(groups[1]), 2)  # Jan 20-21

    def test_group_items_by_ship_date_empty(self):
        """Test grouping with empty list."""
        groups = group_items_by_ship_date([])
        self.assertEqual(len(groups), 0)

    def test_group_items_by_ship_date_sorted(self):
        """Test that groups are sorted by earliest date."""
        # Items in random order
        items = [
            (MockOAItem(1), date(2024, 1, 25), 10),
            (MockOAItem(2), date(2024, 1, 15), 20),
            (MockOAItem(3), date(2024, 1, 20), 30),
        ]

        groups = group_items_by_ship_date(items, tolerance_days=3)

        # Groups should be sorted by earliest date
        self.assertEqual(len(groups), 3)
        self.assertEqual(groups[0][0][1], date(2024, 1, 15))
        self.assertEqual(groups[1][0][1], date(2024, 1, 20))
        self.assertEqual(groups[2][0][1], date(2024, 1, 25))


class MockOAItem:
    """Mock OrderAcknowledgementItem for testing."""

    def __init__(self, item_id):
        self.id = item_id
        self.pk = item_id
