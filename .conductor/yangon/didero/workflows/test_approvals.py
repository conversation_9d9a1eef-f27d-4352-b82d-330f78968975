from unittest.mock import patch

from django.contrib.contenttypes.models import ContentType

from didero.orders.models import OrderA<PERSON><PERSON>al, PurchaseOrder
from didero.tasks.schemas import TaskActionType as TaskActionTypeName
from didero.testing.cases import TestCase
from didero.workflows.core.nodes.purchase_orders.approvals import (
    get_purchase_order_approval,
)


class TestPurchaseOrderApprovalWorkflow(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user()
        self.approver = self.create_user(email="<EMAIL>")
        self.team = self.user.teams.first()
        self.supplier = self.create_supplier(team=self.team)
        self.order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )

    @patch(
        "didero.workflows.core.nodes.purchase_orders.approvals.create_po_approval_request_task_v2"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.approvals.create_po_approval_request_task"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.approvals.PostmarkEmailService.send_po_approval_request_email"
    )
    def test_create_task_v2_called_with_correct_parameters(
        self, mock_send_email, mock_create_task, mock_create_task_v2
    ):
        """Test that create_task_v2 is called with the correct parameters in the approval workflow."""

        # Set up parameters for the activity function
        params = {
            "user_id": self.approver.id,
            "purchase_order_id": self.order.id,
            "node_id": "test-node-id",
        }

        # Mock activity.info()
        with patch("temporalio.activity.info") as mock_activity_info:
            mock_activity_info.return_value.workflow_id = "test-workflow-id"

            # Call the activity function
            get_purchase_order_approval(params)

            # Verify that create_po_approval_request_task_v2 was called
            mock_create_task_v2.assert_called_once()

            # Get the approval object that was created
            approval = OrderApproval.objects.get(purchase_order=self.order)

            # Verify call parameters for create_task_v2
            call_args = mock_create_task_v2.call_args[1]

            # Check basic parameters
            self.assertEqual(call_args["user"], self.approver)
            self.assertEqual(
                call_args["model_type"], ContentType.objects.get_for_model(self.order)
            )
            self.assertEqual(call_args["model_id"], self.order.id)

            # Check task type parameters
            task_type_params = call_args["task_type_params"]
            self.assertEqual(task_type_params["supplier_name"], self.supplier.name)
            self.assertEqual(task_type_params["po_number"], self.order.po_number)
            self.assertEqual(
                task_type_params["total_cost"],
                str(self.order.total_cost) if self.order.total_cost else "N/A",
            )
            self.assertEqual(task_type_params["placed_by"], self.user.get_full_name())

            # Check actions
            actions = call_args["actions"]
            self.assertEqual(len(actions), 2)

            # Check first action (Approve)
            self.assertEqual(
                actions[0]["action_type"],
                TaskActionTypeName.APPROVE_PURCHASE_ORDER,
            )
            self.assertEqual(
                actions[0]["action_execution_params"]["order_id"], self.order.id
            )
            self.assertEqual(
                actions[0]["action_execution_params"]["approval_id"], approval.id
            )

            # Check second action (Deny)
            self.assertEqual(
                actions[1]["action_type"],
                TaskActionTypeName.DENY_PURCHASE_ORDER,
            )
            self.assertEqual(
                actions[1]["action_execution_params"]["order_id"], self.order.id
            )
            self.assertEqual(
                actions[1]["action_execution_params"]["approval_id"], approval.id
            )

    @patch(
        "didero.workflows.core.nodes.purchase_orders.approvals.create_po_approval_request_task_v2"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.approvals.create_po_approval_request_task"
    )
    @patch(
        "didero.emails.postmark_service.PostmarkEmailService.send_po_approval_request_email"
    )
    def test_email_error_handling(
        self, mock_send_email, mock_create_task, mock_create_task_v2
    ):
        """Test that the workflow continues even if email sending fails."""
        # Setup email to raise an exception
        mock_send_email.side_effect = Exception("Test email failure")

        # Set up parameters for the activity function
        params = {
            "user_id": self.approver.id,
            "purchase_order_id": self.order.id,
            "node_id": "test-node-id",
        }

        # Mock activity.info()
        with patch("temporalio.activity.info") as mock_activity_info:
            mock_activity_info.return_value.workflow_id = "test-workflow-id"

            # Call the activity function - should not raise exception
            get_purchase_order_approval(params)

            # Verify that the email was attempted
            mock_send_email.assert_called_once()

            # Verify that task creation still occurred despite email failure
            mock_create_task.assert_called_once()
            mock_create_task_v2.assert_called_once()
