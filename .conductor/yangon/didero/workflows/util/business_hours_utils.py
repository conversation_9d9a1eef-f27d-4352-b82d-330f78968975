from datetime import datetime, timedelta

import holidays
import structlog
from zoneinfo import ZoneInfo

logger = structlog.get_logger(__name__)


def is_business_day(dt: datetime, timezone: str = "America/New_York") -> bool:
    """
    Check if a given datetime falls on a business day.

    Business days are Monday-Friday, excluding US holidays.
    """
    # Convert to timezone-aware
    tz = ZoneInfo(timezone)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=tz)
    else:
        dt = dt.astimezone(tz)

    # Check if it's a weekend (5=Saturday, 6=Sunday)
    if dt.weekday() >= 5:
        return False

    # Check if it's a US holiday
    us_holidays = holidays.US()
    if dt.date() in us_holidays:
        return False

    return True


def is_business_hour(
    dt: datetime,
    timezone: str = "America/New_York",
    start_hour: int = 9,
    end_hour: int = 17,
) -> bool:
    """
    Check if a given datetime falls within business hours.

    Default business hours are 9 AM - 5 PM in the specified timezone.
    """
    if not is_business_day(dt, timezone):
        return False

    # Convert to timezone-aware
    tz = ZoneInfo(timezone)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=tz)
    else:
        dt = dt.astimezone(tz)

    current_hour = dt.hour
    return start_hour <= current_hour < end_hour


def next_business_hour(dt: datetime, timezone: str = "America/New_York") -> datetime:
    """
    Find the next business hour from a given datetime.

    If current time is within business hours, return it.
    Otherwise, find the next business hour.
    """
    tz = ZoneInfo(timezone)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=tz)
    else:
        dt = dt.astimezone(tz)

    # If already in business hours, return as-is
    if is_business_hour(dt, timezone):
        return dt

    # Start checking from current datetime
    current_dt = dt

    # Maximum 30 days to prevent infinite loop
    for _ in range(30):
        # If it's a business day
        if is_business_day(current_dt, timezone):
            # If before business hours, set to 9 AM
            if current_dt.hour < 9:
                return current_dt.replace(hour=9, minute=0, second=0, microsecond=0)
            # If after business hours, move to next day at 9 AM
            elif current_dt.hour >= 17:
                current_dt = (current_dt + timedelta(days=1)).replace(
                    hour=9, minute=0, second=0, microsecond=0
                )
            else:
                # Within business hours but not detected - return as-is
                return current_dt
        else:
            # Not a business day, move to next day at 9 AM
            current_dt = (current_dt + timedelta(days=1)).replace(
                hour=9, minute=0, second=0, microsecond=0
            )

    # Fallback
    logger.warning(
        "Could not find next business hour within 30 days", start_datetime=dt
    )
    return dt + timedelta(days=1)


def calculate_business_aware_delay(
    hours_to_wait: float,
    timezone: str = "America/New_York",
    business_hours_only: bool = True,
) -> float:
    """
    Calculate the actual delay in hours, accounting for business hours if enabled.

    Simple approach: Add the hours, then ensure we land on a business hour.

    Args:
        hours_to_wait: Number of hours to wait
        timezone: Timezone for business hours calculation
        business_hours_only: Whether to count only business hours

    Returns:
        Actual delay in hours
    """
    if not business_hours_only:
        # Simple calendar-based delay
        return hours_to_wait

    # Calculate business hours delay
    tz = ZoneInfo(timezone)
    now = datetime.now(tz=tz)

    # Add the requested hours
    target_dt = now + timedelta(hours=hours_to_wait)

    # Ensure target lands on a business hour
    target_dt = next_business_hour(target_dt, timezone)

    # Calculate actual hours to sleep
    delay_seconds = (target_dt - now).total_seconds()
    delay_hours = delay_seconds / 3600.0

    logger.info(
        "calculated business hours delay",
        requested_hours=hours_to_wait,
        actual_delay_hours=delay_hours,
        current_time=now.isoformat(),
        target_time=target_dt.isoformat(),
        timezone=timezone,
    )

    return max(0, delay_hours)
