"""
Schemas for shared workflow activities.
"""

from typing import Any, Dict

from pydantic import BaseModel


# Document matching activity schemas
class DocumentInfo(BaseModel):
    """Information about a single document"""

    doc_type: str
    reference: str
    doc_id: str  # MANDATORY - required for actions to work
    data: Dict[str, Any]


class DocumentMatchContext(BaseModel):
    """Context for document matching task"""

    model_id: str
    model_type_name: str
    team_id: int
    document1: DocumentInfo
    document2: DocumentInfo


class MatchingResult(BaseModel):
    """Results from the matching process"""

    match_result: str
    matching_score: float
    comparison_summary: str
    comparison_data: Dict[str, Any]


class TaskMetadata(BaseModel):
    """Additional metadata for the task"""

    supplier_name: str


class TaskAssignment(BaseModel):
    """Who the task should be assigned to"""

    user_ids: list[int]
    user_group_names: list[str] | None = None
