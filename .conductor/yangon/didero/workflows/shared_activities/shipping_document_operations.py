"""
Shared shipping document operations activities that can be used across multiple workflows.
"""

from enum import StrEnum
from typing import Any, Dict, Optional, Tuple

import structlog
from temporalio import activity, workflow

# Import Django models using unsafe context to avoid sandbox restrictions
with workflow.unsafe.imports_passed_through():
    from django.contrib.contenttypes.models import ContentType
    from django.utils.dateparse import parse_date

    from didero.ai.shipping_document.extraction import (
        ai_extract_shipping_document_details,
    )
    from didero.documents.models import Document
    from didero.emails.models import EmailThreadToPurchaseOrderLink
    from didero.orders.models import OrderItem, PurchaseOrder
    from didero.shipping_documents.models import ShippingDocument, ShippingItem
    from didero.shipping_documents.schemas import ShippingDocumentStatus
    from didero.suppliers.models import Communication
    from didero.users.models import Team
    from didero.workflows.core.nodes.purchase_orders.shipments import (
        get_purchase_order_from_po_number,
    )
    from didero.workflows.core_workflows.shipping_document_processing.schemas import (
        ExtractShippingDocumentDetailsParams,
        StoreShippingDocumentFromEmailParams,
    )

logger = structlog.get_logger(__name__)


# Define error types locally to avoid circular import
class ShippingDocumentWorkflowErrorType(StrEnum):
    COMMUNICATION_NOT_FOUND = "communication_not_found"
    PURCHASE_ORDER_NOT_FOUND = "purchase_order_not_found"
    SHIPPING_DOCUMENT_EXTRACTION_FAILED = "shipping_document_extraction_failed"


@activity.defn
def extract_shipping_document_details(
    params: ExtractShippingDocumentDetailsParams,
) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
    """
    Extract shipping document details from an email communication.

    This activity:
    1. Retrieves the email communication
    2. Extracts shipping document details using AI
    3. Looks up the matching purchase order
    4. Links the email thread to the PO if found

    Args:
        params: Dictionary containing email_id

    Returns:
        Tuple of (error_type, result_dict) where result_dict contains:
        - shipping_document_data: The extracted shipping document schema
        - purchase_order_id: The matching PO ID
        - email_id: The original email ID
        - supplier_info: Supplier details
    """
    email_id = params["email_id"]
    logger.info(
        "Shipping Document Workflow: Extracting shipping document details",
        email_id=email_id,
    )

    try:
        email = Communication.objects.get(id=email_id)
        logger.info(
            "Shipping Document Workflow: Found communication",
            email_id=email_id,
        )
    except Communication.DoesNotExist:
        logger.error(
            "Shipping Document Workflow: Communication not found",
            email_id=email_id,
        )
        return ShippingDocumentWorkflowErrorType.COMMUNICATION_NOT_FOUND.value, None

    # Extract shipping document details
    logger.info(
        "Shipping Document Workflow: Extracting shipping document from email",
        email_id=email_id,
    )
    shipping_document = ai_extract_shipping_document_details(email)

    if not shipping_document:
        logger.error(
            "Shipping Document Workflow: Failed to extract shipping document details",
            email_id=email_id,
        )
        return (
            ShippingDocumentWorkflowErrorType.SHIPPING_DOCUMENT_EXTRACTION_FAILED.value,
            None,
        )

    logger.info(
        "Shipping Document Workflow: Successfully extracted shipping document details",
        email_id=email_id,
        document_type=shipping_document.document_type,
        reference_number=shipping_document.reference_number,
        po_number=shipping_document.po_number,
    )

    # Retrieve purchase order if PO number exists
    purchase_order = None
    if shipping_document.po_number:
        purchase_order = get_purchase_order_from_po_number(
            shipping_document.po_number, email.team.id
        )

        if purchase_order:
            logger.info(
                "Shipping Document Workflow: Purchase order found",
                email_id=email_id,
                purchase_order_id=purchase_order.pk,
                po_number=shipping_document.po_number,
            )

            # Link email thread to PO if found
            if email.thread:
                EmailThreadToPurchaseOrderLink.objects.get_or_create(
                    email_thread=email.thread,
                    purchase_order=purchase_order,
                )
                logger.info(
                    "Shipping Document Workflow: Linked email thread to purchase order",
                    email_id=email_id,
                    thread_id=email.thread.id,
                    purchase_order_id=purchase_order.pk,
                )
        else:
            logger.info(
                "Shipping Document Workflow: Purchase order not found",
                email_id=email_id,
                po_number=shipping_document.po_number,
            )

    # Get supplier info from email
    supplier_info = {
        "supplier_name": shipping_document.supplier_name or email.email_from,
        "supplier_id": getattr(email, "supplier_id", None),
    }

    # Prepare the shipping document data with type-specific fields extracted
    shipping_data = shipping_document.model_dump()
    shipping_data["type_specific_data"] = shipping_document.get_type_specific_data()

    return None, {
        "shipping_document_data": shipping_data,
        "purchase_order_id": purchase_order.id if purchase_order else None,
        "email_id": email_id,
        "supplier_info": supplier_info,
    }


@activity.defn
def store_shipping_document_from_email_activity(
    params: StoreShippingDocumentFromEmailParams,
) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
    """
    Store shipping document from email extraction (follows invoice pattern).
    Creates ShippingDocument record and basic ShippingItems (no fuzzy matching for PR1).

    Args:
        params: Dictionary containing:
            - shipping_document_data: The extracted shipping document data
            - email_id: Email ID
            - team_id: Team ID
            - purchase_order_id: PO ID (optional)
            - supplier_info: Supplier information

    Returns:
        Tuple of (error_type, result_dict) where result_dict contains:
        - success: Boolean indicating success
        - shipping_document_id: ID of created shipping document
        - has_purchase_order: Whether PO was linked
    """
    try:
        # Extract parameters
        shipping_data = params["shipping_document_data"]
        email_id = params["email_id"]
        team_id = params["team_id"]
        purchase_order_id = params.get("purchase_order_id")
        supplier_info = params["supplier_info"]

        logger.info(
            "Shipping Document Workflow: Storing shipping document",
            email_id=email_id,
            team_id=team_id,
            document_type=shipping_data.get("document_type"),
            reference_number=shipping_data.get("reference_number"),
        )

        # Get email and first attached document
        email = Communication.objects.get(id=email_id)
        documents = Document.objects.filter(
            links__parent_object_type=ContentType.objects.get_for_model(Communication),
            links__parent_object_id=email.pk,
        )

        # Get team and related objects
        team = Team.objects.get(id=team_id)
        purchase_order = None
        if purchase_order_id:
            purchase_order = PurchaseOrder.objects.get(id=purchase_order_id)

        # Create shipping document
        shipping_doc = ShippingDocument.objects.create(
            document_type=shipping_data["document_type"],
            document=documents.first() if documents.exists() else None,
            reference_number=shipping_data["reference_number"],
            purchase_order=purchase_order,
            supplier_id=supplier_info.get("supplier_id")
            or (purchase_order.supplier_id if purchase_order else None),
            team=team,
            carrier_name=shipping_data.get("carrier_name", ""),
            tracking_number=shipping_data.get("tracking_number", ""),
            document_date=parse_date(shipping_data.get("document_date"))
            if shipping_data.get("document_date")
            else None,
            notes=shipping_data.get("notes") or "",
            extracted_data=shipping_data.get("type_specific_data", {}),
            status=ShippingDocumentStatus.PROCESSING,
        )

        logger.info(
            "Shipping Document Workflow: Created shipping document",
            email_id=email_id,
            shipping_document_id=shipping_doc.id,
            has_purchase_order=bool(purchase_order),
        )

        # Create ShippingItems (order_item can be None if no PO found)
        items_created = 0
        for item_data in shipping_data.get("items", []):
            # Try to match with existing OrderItem if we have a purchase order
            order_item = None
            if purchase_order:
                # Look for matching OrderItem by item number
                item_number = item_data.get("item_number", "")
                if item_number:
                    order_item = OrderItem.objects.filter(
                        purchase_order=purchase_order,
                        item__item_number__iexact=item_number,
                    ).first()

                    if order_item:
                        logger.info(
                            "Shipping Document Workflow: Found matching OrderItem",
                            item_number=item_number,
                            order_item_id=order_item.id,
                            purchase_order_id=purchase_order.id,
                        )

            ShippingItem.objects.create(
                shipping_document=shipping_doc,
                order_item=order_item,  # Can be None if no PO or no match found
                received_quantity=item_data.get("received_quantity", 0),
                condition=item_data.get("condition", "good"),
                notes=f"Item: {item_data.get('item_number', 'Unknown')} - {item_data.get('item_description', '')}",
            )
            items_created += 1

        logger.info(
            "Shipping Document Workflow: Created shipping items",
            email_id=email_id,
            shipping_document_id=shipping_doc.id,
            items_created=items_created,
        )

        return None, {
            "success": True,
            "shipping_document_id": str(shipping_doc.id),
            "has_purchase_order": bool(purchase_order),
        }

    except Exception as e:
        logger.error(
            "Shipping Document Workflow: Failed to store shipping document",
            email_id=params.get("email_id"),
            error=str(e),
        )
        return "storage_failed", {"success": False, "error_message": str(e)}
