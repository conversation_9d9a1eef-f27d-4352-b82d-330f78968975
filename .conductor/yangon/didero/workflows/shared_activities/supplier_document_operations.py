"""
Unified supplier document operations for workflow activities.

This module provides a generic activity for extracting and resolving
supplier documents (Order Acknowledgements, Shipments, etc.) with
optional PO auto-creation support.
"""

from dataclasses import dataclass
from typing import Any, Callable, Dict, Optional, Protocol, TypedDict

import structlog
from django.db import OperationalError
from temporalio import activity
from temporalio.exceptions import ApplicationError

from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication

logger = structlog.get_logger(__name__)


# Type definitions
class DocumentExtractionResult(TypedDict):
    """
    Return type for document extraction without PO resolution.
    """

    document_type: str  # "order_acknowledgement", "shipment", "invoice"
    document_details: Dict[str, Any]  # The extracted document data
    po_number: Optional[str]  # Extracted PO number from document
    email_id: str  # Source email ID
    team_id: int  # Team ID
    source_context: str  # Context for tracking
    extraction_succeeded: bool  # Whether extraction succeeded


class POResolutionResult(TypedDict):
    """
    Return type for PO resolution with optional auto-creation.
    """

    purchase_order_id: str  # Resolved PO ID
    auto_created_po: bool  # Whether PO was auto-created from ERP


class DocumentExtractor(Protocol):
    """Protocol for document extractors."""

    def __call__(self, email: Communication) -> Any: ...


@dataclass
class DocumentTypeConfig:
    """Configuration for each document type."""

    extractor: DocumentExtractor
    po_number_getter: Callable[[Any], str]
    log_prefix: str
    source_context: str  # For auto-creation tracking


# Document type configurations
def _get_document_type_configs() -> Dict[str, DocumentTypeConfig]:
    """
    Get document type configurations.

    Imports are inside function to avoid circular dependencies.
    """
    from didero.ai.email_processing.shipment_info import (
        get_shipment_info_for_email,
    )
    from didero.ai.order_acknowledgment.order_ack import (
        ai_extract_order_acknowledgement_details,
    )

    return {
        "order_acknowledgement": DocumentTypeConfig(
            extractor=ai_extract_order_acknowledgement_details,
            po_number_getter=lambda doc: doc.po_number,
            log_prefix="Supplier Document (OA)",
            source_context="order_acknowledgement",
        ),
        "shipment": DocumentTypeConfig(
            extractor=get_shipment_info_for_email,
            po_number_getter=lambda doc: doc.purchase_order_number,
            log_prefix="Supplier Document (Shipment)",
            source_context="shipment",
        ),
        # Future: Add invoice, etc.
    }


@activity.defn
def extract_supplier_document(params: Dict[str, Any]) -> DocumentExtractionResult:
    """
    Extract supplier document details from email using AI.

    This activity focuses solely on extracting document information from emails
    without any PO resolution logic.
    Args:
        params: Dict containing:
            - document_type: Type of document ("order_acknowledgement", "shipment", etc.)
            - email_id: Email ID to extract from
            - team_id: Team ID for context

    Returns:
        DocumentExtractionResult with extracted document details

    Raises:
        ApplicationError: For non-retryable errors (not found, invalid type)
    """
    # Extract parameters
    document_type = params["document_type"]
    email_id = params["email_id"]
    team_id = params["team_id"]

    # Get configuration for this document type
    configs = _get_document_type_configs()
    if document_type not in configs:
        raise ApplicationError(
            f"Unsupported document type: {document_type}",
            type="ValidationError",
            non_retryable=True,
        )

    config = configs[document_type]

    logger.info(
        f"{config.log_prefix}: Extracting document",
        email_id=email_id,
        team_id=team_id,
        document_type=document_type,
    )

    # Get the email
    try:
        email = Communication.objects.select_related(
            "team", "supplier", "email_thread"
        ).get(id=email_id)
    except Communication.DoesNotExist:
        logger.error(
            f"{config.log_prefix}: Communication not found",
            email_id=email_id,
        )
        raise ApplicationError(
            f"Communication not found: {email_id}",
            type="ResourceNotFoundError",
            non_retryable=True,
        )
    except OperationalError:
        # Database connection errors should be retried
        raise

    # Extract document details using AI
    try:
        document_details = config.extractor(email)

        # Handle case where AI returns None
        if document_details is None:
            logger.warning(
                f"{config.log_prefix}: AI extraction returned None",
                email_id=email_id,
            )
            return {
                "document_type": document_type,
                "document_details": {},
                "po_number": None,
                "email_id": email_id,
                "team_id": team_id,
                "source_context": config.source_context,
                "extraction_succeeded": False,
            }

        po_number = config.po_number_getter(document_details)
        logger.info(
            f"{config.log_prefix}: Extracted document details",
            email_id=email_id,
            po_number=po_number,
        )

        return {
            "document_type": document_type,
            "document_details": document_details.model_dump()
            if hasattr(document_details, "model_dump")
            else vars(document_details),
            "po_number": po_number,
            "email_id": email_id,
            "team_id": team_id,
            "source_context": config.source_context,
            "extraction_succeeded": True,
        }

    except Exception as e:
        logger.error(
            f"AI extraction failed for email {email_id}: {str(e)}",
        )
        # AI errors might be transient, let them retry
        raise


@activity.defn
async def resolve_purchase_order_for_document(
    params: Dict[str, Any],
) -> POResolutionResult:
    """
    Resolve purchase order with optional auto-creation.

    This activity focuses solely on PO resolution and auto-creation logic
    without any document extraction.

    Args:
        params: Dict containing:
            - po_number: PO number to resolve
            - team_id: Team ID
            - source_email_id: Optional email ID that triggered this
            - source_context: Context ("order_acknowledgement", "shipment", etc.)
            - enable_po_auto_creation: Whether to attempt auto-creation

    Returns:
        POResolutionResult with resolved PO ID and auto-creation flag

    Raises:
        ApplicationError: For non-retryable errors (not found after auto-creation)
    """
    from asgiref.sync import sync_to_async

    from didero.workflows.core.nodes.purchase_orders.shipments import (
        get_purchase_order_from_po_number,
    )
    from didero.workflows.shared_activities.purchase_order_operations import (
        resolve_purchase_order_with_auto_creation,
    )

    po_number = params["po_number"]
    team_id = params["team_id"]
    source_email_id = params.get("source_email_id")
    source_context = params.get("source_context", "unknown")
    enable_po_auto_creation = params.get("enable_po_auto_creation", False)

    logger.info(
        f"Resolving PO {po_number}",
        team_id=team_id,
        enable_auto_creation=enable_po_auto_creation,
    )

    # Try standard PO lookup first
    purchase_order = await sync_to_async(get_purchase_order_from_po_number)(
        po_number, team_id
    )

    auto_created_po = False

    # If not found and auto-creation enabled, trigger it
    if not purchase_order and enable_po_auto_creation:
        logger.info(
            "PO not found, attempting auto-creation",
            po_number=po_number,
            team_id=team_id,
        )

        po_resolution = await resolve_purchase_order_with_auto_creation(
            po_number=po_number,
            team_id=team_id,
            source_email_id=source_email_id,
            source_context=source_context,
            enable_auto_creation=True,
        )

        if po_resolution["success"] and po_resolution.get("purchase_order_id"):
            # Get the auto-created PO
            try:
                purchase_order = await sync_to_async(PurchaseOrder.objects.get)(
                    id=po_resolution["purchase_order_id"]
                )
                auto_created_po = po_resolution.get("auto_created", False)
                logger.info(
                    "Successfully auto-created PO",
                    po_id=purchase_order.pk,
                    po_number=purchase_order.po_number,
                )
            except PurchaseOrder.DoesNotExist:
                logger.error(
                    "Auto-created PO not found in database",
                    po_id=po_resolution["purchase_order_id"],
                )
        else:
            logger.warning(
                "Auto-creation failed",
                po_number=po_number,
                error=po_resolution.get("error_message"),
            )

    # If still no PO, raise error
    if not purchase_order:
        logger.error(
            "Purchase order not found and auto-creation failed or disabled",
            po_number=po_number,
            team_id=team_id,
            auto_creation_enabled=enable_po_auto_creation,
        )
        raise ApplicationError(
            f"Purchase order not found: {po_number}",
            type="ResourceNotFoundError",
            non_retryable=True,
        )

    return {
        "purchase_order_id": str(purchase_order.pk),
        "auto_created_po": auto_created_po,
    }
