"""
Shared utilities for document processing workflows.

These functions extract common patterns from invoice and shipping document workflows
to reduce code duplication and ensure consistency.
"""

from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, Optional, Protocol, Union

from temporalio import workflow
from temporalio.common import RetryPolicy

from didero.ai.purchase_order.schemas import PurchaseOrderDetails
from didero.documents.schemas import DocumentType
from didero.invoices.schemas import Invoice as InvoiceSchema
from didero.shipping_documents.schemas import BaseShippingDocumentSchema
from didero.workflows.shared_activities.document_matching import (
    MatchDocumentsActivityResult,
    create_document_match_review_task_activity,
)
from didero.workflows.shared_activities.schemas import (
    DocumentInfo,
    DocumentMatchContext,
    MatchingResult,
    TaskAssignment,
    TaskMetadata,
)


class WorkflowConfig(Protocol):
    """Protocol for workflow configuration objects."""

    require_human_validation: bool
    auto_approve_threshold: Optional[float]
    assigned_user_ids: list[int]
    assigned_user_group_names: list[str]
    tolerance_config: Any


def should_create_review_task(
    matching_result: Union[Dict[str, Any], MatchDocumentsActivityResult],
    config: WorkflowConfig,
) -> bool:
    """
    Determine if a review task should be created based on matching results and configuration.

    This implements the shared decision logic used across all document workflows.

    Args:
        matching_result: Results from document matching activity
        config: Workflow configuration with validation settings

    Returns:
        True if a review task should be created, False if document should auto-approve
    """
    # Always create task if manual validation is required
    if config.require_human_validation:
        return True

    # Create task for critical issues
    match_result = matching_result.get("match_result")
    if match_result == "critical_issues":
        return True

    # Create task if matching score is below auto-approve threshold
    matching_score = matching_result.get("matching_score", 0.0)
    auto_approve_threshold = config.auto_approve_threshold or 0.9

    if matching_score < auto_approve_threshold:
        return True

    # No task needed for high-confidence matches when manual validation is disabled
    return False


def convert_tolerance_config_to_dict(config: WorkflowConfig) -> Dict[str, Any]:
    """
    Convert workflow tolerance config to dictionary format for activities.

    Args:
        config: Workflow configuration object

    Returns:
        Dictionary with tolerance configuration for document matching
    """
    tolerance_config_dict = {}
    if hasattr(config, "tolerance_config") and config.tolerance_config:
        tolerance_config_dict = {
            "type": config.tolerance_config.type.value
            if hasattr(config.tolerance_config, "type")
            else "percentage",
            "value": config.tolerance_config.value
            if hasattr(config.tolerance_config, "value")
            else 0.05,
            "currency": config.tolerance_config.currency
            if hasattr(config.tolerance_config, "currency")
            else "USD",
        }
    else:
        # Default tolerance config
        tolerance_config_dict = {
            "type": "percentage",
            "value": 0.05,
            "currency": "USD",
        }

    return tolerance_config_dict


async def create_document_review_task(
    document1_id: str,
    document1_type: DocumentType,
    document1_data: Union[InvoiceSchema, BaseShippingDocumentSchema, Dict[str, Any]],
    document2_id: str,
    document2_type: DocumentType,
    document2_data: Union[
        InvoiceSchema, BaseShippingDocumentSchema, PurchaseOrderDetails, Dict[str, Any]
    ],
    matching_result: Union[Dict[str, Any], MatchDocumentsActivityResult],
    config: WorkflowConfig,
    team_id: int,
    logger: Any,
    category: Optional[str] = None,
    override_user_ids: Optional[list[int]] = None,
    override_user_group_names: Optional[list[str]] = None,
) -> Dict[str, Any]:
    """
    Create a document match review task for manual validation.

    This function handles the boilerplate of creating review tasks across all document workflows.

    Args:
        document1_id: ID of the first document (e.g., invoice_id, shipping_document_id)
        document1_type: Type of the first document
        document1_data: Data of the first document
        document2_id: ID of the second document (e.g., po_id)
        document2_type: Type of the second document
        document2_data: Data of the second document
        matching_result: Results from document matching activity
        config: Workflow configuration
        team_id: Team ID
        logger: Workflow logger
        category: Optional category for the task
        override_user_ids: Optional list of user IDs to override config.assigned_user_ids
        override_user_group_names: Optional list of group names to override config.assigned_user_group_names

    Returns:
        Dictionary with task creation results
    """
    try:
        logger.info(
            f"Creating document review task for {document1_type.value} {document1_id}"
        )

        # Map document type to model type name for task creation
        model_type_map = {
            DocumentType.INVOICE: "Invoice",
            DocumentType.SHIPPING_DOCUMENT: "ShippingDocument",
            DocumentType.ORDER_ACKNOWLEDGEMENT: "OrderAcknowledgement",
        }
        model_type_name = model_type_map.get(document1_type, "Document")

        # Prepare context for task creation
        context = DocumentMatchContext(
            model_type_name=model_type_name,
            model_id=document1_id,
            team_id=team_id,
            document1=DocumentInfo(
                doc_type=document1_type.value,
                reference=matching_result.get("document1_reference", ""),
                doc_id=document1_id,
                data=document1_data.model_dump()
                if hasattr(document1_data, "model_dump")
                else dict(document1_data)
                if isinstance(document1_data, dict)
                else {},
            ),
            document2=DocumentInfo(
                doc_type=document2_type.value,
                reference=matching_result.get("document2_reference", ""),
                doc_id=document2_id,
                data=document2_data.model_dump()
                if hasattr(document2_data, "model_dump")
                else dict(document2_data)
                if isinstance(document2_data, dict)
                else {},
            ),
        )

        matching_result_obj = MatchingResult(
            match_result=matching_result.get("match_result", "unknown"),
            matching_score=matching_result.get("matching_score", 0.0),
            comparison_summary=matching_result.get("summary", ""),
            comparison_data=dict(
                matching_result
            ),  # Convert TypedDict to Dict if needed
        )

        # Extract supplier name with simple fallback
        supplier_name = "Unknown"
        if isinstance(document2_data, dict):
            supplier_name = document2_data.get("supplier_name") or supplier_name
        elif hasattr(document2_data, "supplier_name"):
            supplier_name = getattr(document2_data, "supplier_name") or supplier_name

        # Also check document1_data for supplier name if not found
        if supplier_name == "Unknown":
            if isinstance(document1_data, dict):
                supplier_name = document1_data.get("supplier_name") or supplier_name
            elif hasattr(document1_data, "supplier_name"):
                supplier_name = (
                    getattr(document1_data, "supplier_name") or supplier_name
                )

        metadata = TaskMetadata(
            supplier_name=supplier_name,
        )

        # Use override parameters if provided, otherwise fall back to config
        final_user_ids = (
            override_user_ids
            if override_user_ids is not None
            else config.assigned_user_ids
        )
        final_user_group_names = (
            override_user_group_names
            if override_user_group_names is not None
            else config.assigned_user_group_names
        )

        assignment = TaskAssignment(
            user_ids=final_user_ids,
            user_group_names=final_user_group_names,
        )

        # Determine task category based on document type if not provided
        if not category:
            category_map = {
                DocumentType.INVOICE: "invoice_review",
                DocumentType.SHIPPING_DOCUMENT: "shipping_document_review",
                DocumentType.ORDER_ACKNOWLEDGEMENT: "order_acknowledgement_review",
            }
            category = category_map.get(document1_type, "document_review")

        # Create the review task
        task_result = await workflow.execute_activity(
            create_document_match_review_task_activity,
            args=[context, matching_result_obj, metadata, assignment, category],
            start_to_close_timeout=timedelta(minutes=2),
            retry_policy=RetryPolicy(
                initial_interval=timedelta(seconds=1),
                maximum_interval=timedelta(seconds=10),
                maximum_attempts=3,
            ),
        )

        if task_result.get("success"):
            logger.info(f"Created task(s): {task_result.get('task_ids')}")
        else:
            logger.error(f"Task creation failed: {task_result.get('error_message')}")

        return task_result

    except Exception as e:
        logger.error(
            f"Exception creating document review task: {str(e)}", exc_info=True
        )
        return {
            "success": False,
            "error_message": f"Failed to create document review task: {str(e)}",
        }
