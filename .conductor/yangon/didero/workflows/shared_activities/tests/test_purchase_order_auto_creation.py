import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from django.test import TestCase
from temporalio.common import RetryPolicy

from didero.workflows.shared_activities.purchase_order_operations import (
    enqueue_po_auto_creation_workflow,
    resolve_purchase_order_with_auto_creation,
    should_auto_create_po_for_team,
)
from didero.workflows.shared_activities.supplier_document_operations import (
    extract_supplier_document,
    resolve_purchase_order_for_document,
)


class TestShouldAutoCreatePOForTeam(TestCase):
    """Test the team configuration check for auto-creation."""

    @patch("didero.integrations.models.ERPIntegrationConfig")
    def test_should_auto_create_po_for_team_enabled(self, mock_config):
        """Test that auto-creation is enabled when ERP config is properly set."""
        # Mock the query to return a configuration
        mock_config.objects.filter.return_value.exists.return_value = True

        result = should_auto_create_po_for_team(
            4, enable_auto_creation=True
        )  # IonQ team ID

        self.assertTrue(result)
        mock_config.objects.filter.assert_called_once_with(team_id=4, enabled=True)

    @patch("didero.integrations.models.ERPIntegrationConfig")
    def test_should_auto_create_po_for_team_disabled(self, mock_config):
        """Test that auto-creation is disabled when ERP config is not set."""
        # Mock the query to return no configuration
        mock_config.objects.filter.return_value.exists.return_value = False

        result = should_auto_create_po_for_team(
            999, enable_auto_creation=True
        )  # Non-IonQ team ID

        self.assertFalse(result)
        mock_config.objects.filter.assert_called_once_with(team_id=999, enabled=True)

    def test_should_auto_create_po_permission_disabled(self):
        """Test that auto-creation is disabled when permission is false."""
        # Even if team has capability, permission overrides it
        result = should_auto_create_po_for_team(4, enable_auto_creation=False)

        self.assertFalse(result)
        # No database query should be made when permission is disabled

    @patch("didero.integrations.models.ERPIntegrationConfig")
    def test_should_auto_create_po_for_team_database_error(self, mock_config):
        """Test that database errors return False (no fallback)."""
        # Mock the query to raise an exception
        mock_config.objects.filter.side_effect = Exception("Database error")

        # All teams should return False when there's a database error
        self.assertFalse(should_auto_create_po_for_team(4, enable_auto_creation=True))
        self.assertFalse(should_auto_create_po_for_team(173, enable_auto_creation=True))
        self.assertFalse(should_auto_create_po_for_team(999, enable_auto_creation=True))

        # Connection errors also return False (no special handling)
        mock_config.objects.filter.side_effect = Exception("connection error")
        self.assertFalse(should_auto_create_po_for_team(4, enable_auto_creation=True))


class TestEnqueuePOAutoCreationWorkflow(TestCase):
    """Test the child workflow orchestration."""

    @patch("didero.workflows.utils.get_temporal_client")
    @patch("didero.workflows.queue_config.get_queue_for_workflow_type")
    def test_enqueue_po_auto_creation_workflow_success(
        self, mock_get_queue, mock_get_client
    ):
        """Test successful PO auto-creation workflow enqueuing."""

        async def async_test():
            # Mock dependencies
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client
            mock_get_queue.return_value = "po_creation_queue"

            # Mock workflow handle
            mock_handle = AsyncMock()
            mock_handle.id = "test-workflow-id"
            mock_handle.result.return_value = {
                "success": True,
                "po_id": "123",
                "po_number": "PO431",
                "created_in_draft": False,
            }
            mock_client.start_workflow.return_value = mock_handle

            # Execute the function
            result = await enqueue_po_auto_creation_workflow(
                team_id=4,
                po_number="PO431",
                source_email_id="email123",
                source_context="order_acknowledgement",
            )

            # Verify results
            self.assertTrue(result["success"])
            self.assertEqual(result["po_id"], "123")
            self.assertEqual(result["po_number"], "PO431")
            self.assertTrue(
                result["workflow_id"].startswith(
                    "auto-po-creation-PO431-4-order_acknowledgement-"
                )
            )
            self.assertEqual(result["temporal_id"], "test-workflow-id")

            # Verify workflow was started correctly
            mock_client.start_workflow.assert_called_once()
            call_args = mock_client.start_workflow.call_args
            self.assertEqual(call_args[1]["task_queue"], "po_creation_queue")
            self.assertIsInstance(call_args[1]["retry_policy"], RetryPolicy)

        asyncio.run(async_test())

    @patch("didero.workflows.utils.get_temporal_client")
    @patch("didero.workflows.queue_config.get_queue_for_workflow_type")
    def test_enqueue_po_auto_creation_workflow_timeout(
        self, mock_get_queue, mock_get_client
    ):
        """Test timeout handling in PO auto-creation workflow."""

        async def async_test():
            # Mock dependencies
            mock_client = AsyncMock()
            mock_get_client.return_value = mock_client
            mock_get_queue.return_value = "po_creation_queue"

            # Mock workflow handle that times out
            mock_handle = AsyncMock()
            mock_handle.id = "test-workflow-id"
            mock_handle.result.side_effect = asyncio.TimeoutError()
            mock_client.start_workflow.return_value = mock_handle

            # Execute the function
            result = await enqueue_po_auto_creation_workflow(
                team_id=4,
                po_number="PO431",
                source_email_id="email123",
                source_context="shipment",
            )

            # Verify timeout handling
            self.assertFalse(result["success"])
            self.assertIn("timed out", result["error_message"])
            self.assertEqual(result["temporal_id"], "test-workflow-id")

        asyncio.run(async_test())


class TestResolvePurchaseOrderWithAutoCreation(TestCase):
    """Test the main smart PO resolution activity."""

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.PurchaseOrderDetails"
    )
    def test_resolve_po_found_immediately(self, mock_po_details, mock_get_po):
        """Test when PO is found on first lookup - no auto-creation needed."""
        # Create a mock PO object
        mock_po = MagicMock()
        mock_po.pk = "123"
        mock_po.po_number = "PO431"

        # Mock get_purchase_order_from_po_number to return the PO
        mock_get_po.return_value = mock_po

        # Mock PurchaseOrderDetails
        mock_po_details_instance = MagicMock()
        mock_po_details_instance.model_dump.return_value = {"po_number": "PO431"}
        mock_po_details.from_model.return_value = mock_po_details_instance

        result = asyncio.run(
            resolve_purchase_order_with_auto_creation(
                po_number="PO431",
                team_id=4,
                source_email_id="email123",
                source_context="order_acknowledgement",
                enable_auto_creation=True,  # Add missing parameter
            )
        )

        # Verify no auto-creation attempted
        self.assertTrue(result["success"])
        self.assertFalse(result["auto_created"])
        self.assertFalse(result["auto_creation_attempted"])
        self.assertEqual(result["purchase_order_id"], "123")

    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.team_has_erp_auto_creation_capability"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    def test_resolve_po_not_found_auto_creation_disabled(
        self, mock_get_po, mock_has_capability
    ):
        """Test when PO not found and auto-creation is disabled for team."""
        # Mock failed lookup - PO not found
        mock_get_po.return_value = None

        # Test case 1: Disabled by workflow configuration
        result = asyncio.run(
            resolve_purchase_order_with_auto_creation(
                po_number="PO431",
                team_id=999,
                source_email_id="email123",
                source_context="shipment",
                enable_auto_creation=False,  # Disabled by workflow config
            )
        )

        self.assertFalse(result["success"])
        self.assertFalse(result["auto_creation_attempted"])
        self.assertEqual(
            result["auto_creation_error"],
            "Auto-creation disabled by workflow configuration",
        )

        # Test case 2: Enabled by workflow but no ERP capability
        mock_has_capability.return_value = False

        result = asyncio.run(
            resolve_purchase_order_with_auto_creation(
                po_number="PO431",
                team_id=999,
                source_email_id="email123",
                source_context="shipment",
                enable_auto_creation=True,  # Enabled but no capability
            )
        )

        self.assertFalse(result["success"])
        self.assertFalse(result["auto_creation_attempted"])
        self.assertEqual(
            result["auto_creation_error"],
            "ERP integration not configured for auto-creation",
        )

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.PurchaseOrderDetails"
    )
    def test_resolve_po_successful_auto_creation(self, mock_po_details, mock_get_po):
        """Test successful auto-creation and retry flow."""
        # Create mock PO for successful retry
        mock_po = MagicMock()
        mock_po.pk = "456"
        mock_po.po_number = "PO431"

        # Mock get_purchase_order_from_po_number - first None, then return PO
        mock_get_po.side_effect = [None, mock_po]

        # Mock PurchaseOrderDetails
        mock_po_details_instance = MagicMock()
        mock_po_details_instance.model_dump.return_value = {"po_number": "PO431"}
        mock_po_details.from_model.return_value = mock_po_details_instance

        with patch(
            "didero.workflows.shared_activities.purchase_order_operations.team_has_erp_auto_creation_capability"
        ) as mock_has_capability:
            mock_has_capability.return_value = True

            with patch(
                "didero.workflows.shared_activities.purchase_order_operations.enqueue_po_auto_creation_workflow",
                new_callable=AsyncMock,
            ) as mock_enqueue:
                # Configure AsyncMock to return the expected result
                mock_enqueue.return_value = {
                    "success": True,
                    "po_id": "456",
                    "po_number": "PO431",
                    "workflow_id": "auto-workflow-123",
                }

                # Call async function using asyncio.run
                result = asyncio.run(
                    resolve_purchase_order_with_auto_creation(
                        po_number="PO431",
                        team_id=4,  # IonQ team
                        source_email_id="email123",
                        source_context="order_acknowledgement",
                        enable_auto_creation=True,
                    )
                )

                # Verify successful auto-creation
                self.assertTrue(result["success"])
                self.assertTrue(result["auto_created"])
                self.assertTrue(result["auto_creation_attempted"])
                self.assertEqual(result["purchase_order_id"], "456")
                self.assertEqual(
                    result["creation_details"]["workflow_id"], "auto-workflow-123"
                )

                # Verify enqueue was called
                mock_enqueue.assert_called_once()


class TestIntegrationScenarios(TestCase):
    """Test realistic integration scenarios."""

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.PurchaseOrderDetails"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.team_has_erp_auto_creation_capability"
    )
    def test_ionq_order_acknowledgement_missing_po_scenario(
        self, mock_has_capability, mock_po_details, mock_get_po
    ):
        """Test realistic IonQ order acknowledgement with missing PO."""
        # Create mock PO for successful retry
        mock_po = MagicMock()
        mock_po.pk = "789"
        mock_po.po_number = "PO431"

        # Mock get_purchase_order_from_po_number - first None, then return PO
        mock_get_po.side_effect = [None, mock_po]

        # Mock PurchaseOrderDetails
        mock_po_details_instance = MagicMock()
        mock_po_details_instance.model_dump.return_value = {"po_number": "PO431"}
        mock_po_details.from_model.return_value = mock_po_details_instance

        # Mock team has capability
        mock_has_capability.return_value = True

        # Mock enqueue_po_auto_creation_workflow
        with patch(
            "didero.workflows.shared_activities.purchase_order_operations.enqueue_po_auto_creation_workflow",
            new_callable=AsyncMock,
        ) as mock_enqueue:
            # Configure AsyncMock
            mock_enqueue.return_value = {
                "success": True,
                "po_id": "789",
                "po_number": "PO431",
                "workflow_id": "auto-po-creation-PO431-4-order_acknowledgement",
            }

            # Call async function
            result = asyncio.run(
                resolve_purchase_order_with_auto_creation(
                    po_number="PO431",
                    team_id=4,  # IonQ team
                    source_email_id="oa_email_123",
                    source_context="order_acknowledgement",
                    enable_auto_creation=True,
                )
            )

            self.assertTrue(result["success"])
            self.assertTrue(result["auto_created"])
            self.assertEqual(result["purchase_order_id"], "789")

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.team_has_erp_auto_creation_capability"
    )
    def test_non_ionq_team_missing_po_scenario(self, mock_has_capability, mock_get_po):
        """Test non-IonQ team with missing PO - should not attempt auto-creation."""
        # Mock PO not found
        mock_get_po.return_value = None

        # Mock team doesn't have capability
        mock_has_capability.return_value = False

        # Call async function
        result = asyncio.run(
            resolve_purchase_order_with_auto_creation(
                po_number="PO999",
                team_id=999,  # Non-IonQ team
                source_email_id="email_456",
                source_context="shipment",
                enable_auto_creation=True,
            )
        )

        self.assertFalse(result["success"])
        self.assertFalse(result["auto_creation_attempted"])
        self.assertEqual(
            result["auto_creation_error"],
            "ERP integration not configured for auto-creation",
        )


class TestSplitActivities(TestCase):
    """Test the split document extraction and PO resolution activities."""

    @patch("didero.suppliers.models.Communication.objects")
    def test_extract_supplier_document_success(self, mock_comm_objects):
        """Test successful document extraction."""
        from unittest.mock import Mock

        from didero.suppliers.models import Communication

        # Mock email
        mock_email = Mock(spec=Communication)
        mock_comm_objects.select_related.return_value.get.return_value = mock_email

        # Mock document extraction
        with patch(
            "didero.workflows.shared_activities.supplier_document_operations._get_document_type_configs"
        ) as mock_configs:
            mock_extractor = Mock()
            mock_document = Mock()
            mock_document.po_number = "PO-123"
            mock_document.model_dump.return_value = {"po_number": "PO-123", "items": []}
            mock_extractor.return_value = mock_document

            mock_configs.return_value = {
                "order_acknowledgement": Mock(
                    extractor=mock_extractor,
                    po_number_getter=lambda doc: doc.po_number,
                    log_prefix="OA",
                    source_context="order_acknowledgement",
                )
            }

            # Call the activity
            result = extract_supplier_document(
                {
                    "document_type": "order_acknowledgement",
                    "email_id": "email123",
                    "team_id": 4,
                }
            )

            # Verify results
            self.assertEqual(result["document_type"], "order_acknowledgement")
            self.assertEqual(result["po_number"], "PO-123")
            self.assertEqual(result["email_id"], "email123")
            self.assertEqual(result["team_id"], 4)
            self.assertTrue(result["extraction_succeeded"])

    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    def test_resolve_purchase_order_for_document_found(self, mock_get_po):
        """Test PO resolution when PO is found immediately."""
        from unittest.mock import Mock

        from didero.orders.models import PurchaseOrder

        # Mock PO found
        mock_po = Mock(spec=PurchaseOrder)
        mock_po.pk = 123
        mock_get_po.return_value = mock_po

        # Call the activity
        result = asyncio.run(
            resolve_purchase_order_for_document(
                {"po_number": "PO-123", "team_id": 4, "enable_po_auto_creation": True}
            )
        )

        # Verify results
        self.assertEqual(result["purchase_order_id"], "123")
        self.assertFalse(result["auto_created_po"])

    @patch(
        "didero.workflows.shared_activities.purchase_order_operations.resolve_purchase_order_with_auto_creation"
    )
    @patch(
        "didero.workflows.core.nodes.purchase_orders.shipments.get_purchase_order_from_po_number"
    )
    @patch("didero.orders.models.PurchaseOrder.objects.get")
    def test_resolve_purchase_order_for_document_auto_created(
        self, mock_po_get, mock_get_po, mock_resolve
    ):
        """Test PO resolution with auto-creation."""
        from unittest.mock import Mock

        from didero.orders.models import PurchaseOrder

        # Mock PO not found initially
        mock_get_po.return_value = None

        # Mock auto-creation success - use AsyncMock
        from unittest.mock import AsyncMock

        mock_resolve.side_effect = AsyncMock(
            return_value={
                "success": True,
                "purchase_order_id": "456",
                "auto_created": True,
            }
        )

        # Mock PO found after creation
        mock_po = Mock(spec=PurchaseOrder)
        mock_po.pk = 456
        mock_po.po_number = "PO-123"
        mock_po_get.return_value = mock_po

        # Call the activity
        result = asyncio.run(
            resolve_purchase_order_for_document(
                {
                    "po_number": "PO-123",
                    "team_id": 4,
                    "source_email_id": "email123",
                    "source_context": "order_acknowledgement",
                    "enable_po_auto_creation": True,
                }
            )
        )

        # Verify results
        self.assertEqual(result["purchase_order_id"], "456")
        self.assertTrue(result["auto_created_po"])
