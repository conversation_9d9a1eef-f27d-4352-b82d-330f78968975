"""
Tests for document matching activities, particularly task creation with document IDs.
"""

from django.contrib.contenttypes.models import ContentType

from didero.invoices.models import Invoice
from didero.tasks.models import Task, TaskAction
from didero.tasks.schemas import TaskType as TaskTypeName
from didero.testing.cases import TestCase
from didero.workflows.shared_activities.document_matching import (
    create_document_match_review_task_activity,
)
from didero.workflows.shared_activities.schemas import (
    DocumentInfo,
    DocumentMatchContext,
    MatchingResult,
    TaskAssignment,
    TaskMetadata,
)


class TestDocumentMatchingActivity(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user()
        self.team = self.user.teams.first()

        # Ensure task types are synced for testing
        from didero.tasks.migrations.utils.task_action_definitions import (
            sync_task_action_types,
        )
        from didero.tasks.migrations.utils.task_v2_definitions import sync_task_type_v2

        sync_task_type_v2()
        sync_task_action_types()

        # Force refresh of task types to ensure sync took effect
        from django.core.cache import cache

        cache.clear()

        # Create a supplier and purchase order for testing
        self.supplier = self.create_supplier(team=self.team)
        self.purchase_order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )

        # Create an invoice for testing
        from datetime import date

        self.invoice = Invoice.objects.create(
            invoice_number="INV-001",
            team=self.team,
            purchase_order=self.purchase_order,
            invoice_date=date.today(),
            payment_terms="Net 30",
        )

    def test_create_document_match_review_task_with_document_ids(self):
        """Test that document match review tasks are created with proper document IDs"""

        # Create test data using our Pydantic models
        context = DocumentMatchContext(
            model_id=str(self.invoice.id),
            model_type_name="Invoice",
            team_id=self.team.id,
            document1=DocumentInfo(
                doc_type="invoice",
                reference="INV-001",
                doc_id=str(self.invoice.id),
                data={"invoice_number": "INV-001", "amount": 1000.00},
            ),
            document2=DocumentInfo(
                doc_type="purchase_order",
                reference="PO-123",
                doc_id="po-uuid-456",
                data={"po_number": "PO-123", "amount": 1000.00},
            ),
        )

        matching_result = MatchingResult(
            match_result="within_tolerance",
            matching_score=0.85,
            comparison_summary="Invoice matches PO within tolerance",
            comparison_data={
                "header_comparison": {"amount": {"status": "match"}},
                "line_items_comparison": [],
            },
        )

        metadata = TaskMetadata(
            supplier_name="Test Supplier",
        )

        assignment = TaskAssignment(
            user_ids=[self.user.id],
            user_group_names=[],
        )

        # Call the activity function directly
        result = create_document_match_review_task_activity(
            context, matching_result, metadata, assignment
        )

        # Verify the task was created successfully
        self.assertTrue(result["success"])
        self.assertIn("task_ids", result)
        self.assertEqual(len(result["task_ids"]), 1)

        # Get the created task and verify its parameters
        task_id = result["task_ids"][0]
        task = Task.objects.get(pk=task_id)

        # Verify basic task properties
        self.assertEqual(task.user, self.user)
        self.assertEqual(task.task_type_v2.name, TaskTypeName.DOCUMENT_MATCH_REVIEW)
        self.assertEqual(task.model_id, str(self.invoice.id))
        self.assertEqual(task.model_type, ContentType.objects.get_for_model(Invoice))

        # Verify task parameters include document IDs
        task_config = task.task_config
        task_params = task_config["task_params"]
        self.assertEqual(task_params["document1_type"], "invoice")
        self.assertEqual(task_params["document1_reference"], "INV-001")
        self.assertEqual(task_params["document1_id"], str(self.invoice.id))
        self.assertEqual(task_params["document2_type"], "purchase_order")
        self.assertEqual(task_params["document2_reference"], "PO-123")
        self.assertEqual(task_params["document2_id"], "po-uuid-456")
        self.assertEqual(task_params["match_result"], "within_tolerance")
        self.assertEqual(task_params["matching_score"], "0.85")
        self.assertEqual(task_params["supplier_name"], "Test Supplier")

        # Verify task actions were created with document IDs
        task_actions = TaskAction.objects.filter(task=task)
        self.assertGreater(len(task_actions), 0)

        # Find the APPROVE_DOCUMENT_MATCH action
        approve_action = task_actions.filter(
            action_type__name="APPROVE_DOCUMENT_MATCH"
        ).first()
        self.assertIsNotNone(approve_action)

        # Verify the action has the document IDs in execution params
        execution_params = approve_action.execution_params
        self.assertEqual(execution_params["document1_id"], str(self.invoice.id))
        self.assertEqual(execution_params["document1_type"], "invoice")
        self.assertEqual(execution_params["document2_id"], "po-uuid-456")
        self.assertEqual(execution_params["document2_type"], "purchase_order")

    def test_task_creation_fails_with_missing_document_ids(self):
        """Test that task creation fails gracefully when document IDs are missing"""

        context = DocumentMatchContext(
            model_id=str(self.invoice.id),
            model_type_name="Invoice",
            team_id=self.team.id,
            document1=DocumentInfo(
                doc_type="invoice",
                reference="INV-001",
                doc_id="",  # Empty document ID should cause validation error
                data={"invoice_number": "INV-001"},
            ),
            document2=DocumentInfo(
                doc_type="purchase_order",
                reference="PO-123",
                doc_id="",  # Empty document ID should cause validation error
                data={"po_number": "PO-123"},
            ),
        )

        matching_result = MatchingResult(
            match_result="within_tolerance",
            matching_score=0.85,
            comparison_summary="Test summary",
            comparison_data={},
        )

        metadata = TaskMetadata(
            supplier_name="Test Supplier",
        )

        assignment = TaskAssignment(
            user_ids=[self.user.id],
            user_group_names=[],
        )

        # This should work since Pydantic validation happens at the boundary
        # But the task should be created with empty document IDs
        result = create_document_match_review_task_activity(
            context, matching_result, metadata, assignment
        )

        # The task should still be created, but with empty document IDs
        self.assertTrue(result["success"])
        task_id = result["task_ids"][0]
        task = Task.objects.get(pk=task_id)

        # Verify empty document IDs are preserved
        task_params = task.task_config["task_params"]
        self.assertEqual(task_params["document1_id"], "")
        self.assertEqual(task_params["document2_id"], "")
