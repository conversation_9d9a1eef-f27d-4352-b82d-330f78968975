import asyncio
import uuid
from dataclasses import dataclass
from typing import Any, Mapping, Optional, Union

import structlog
from django.conf import settings
from django.db import transaction
from temporalio.client import Client

from didero.telemetry.temporal_metrics import get_temporal_runtime
from didero.users.models.team_models import Team
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums
from didero.workflows.core.nodes.follow_up.schemas import FollowUpType
from didero.workflows.core.nodes.follow_up.workflows import (
    FollowupWorkflow,
)
from didero.workflows.core.user_workflow_definitions import DagBFSWorkflow
from didero.workflows.core_workflows import CORE_WORKFLOW_CLASSES
from didero.workflows.models import (
    UserWorkflow,
    WorkflowBehaviorConfig,
    WorkflowRun,
    WorkflowSnapshot,
)
from didero.workflows.queue_config import get_queue_for_workflow_type
from didero.workflows.schemas import (
    WorkflowBehaviorConfigBase,
    WorkflowRunState,
    WorkflowTrigger,
    WorkflowType,
)

log = structlog.get_logger(__name__)

temporal_server_url = settings.TEMPORAL_SERVER_URL


# Workflow context dataclasses for type safety
@dataclass
class EmailWorkflowContext:
    """Context for email-triggered workflows"""

    email: "Communication"  # Forward reference to avoid circular import


@dataclass
class ShipmentWorkflowContext(EmailWorkflowContext):
    """Context for shipment workflows with additional notification type"""

    notification_type: Optional[str] = None


@dataclass
class ApprovalWorkflowContext:
    """Context for approval workflows"""

    purchase_order_id: str


# Type alias for all possible context types
WorkflowContext = Union[
    EmailWorkflowContext,
    ShipmentWorkflowContext,
    ApprovalWorkflowContext,
    "Communication",
]


async def get_temporal_client():
    # Get the metrics-enabled runtime
    runtime = get_temporal_runtime()

    if temporal_server_url.startswith("localhost"):
        client = await Client.connect(
            settings.TEMPORAL_SERVER_URL,
            runtime=runtime,
        )
    else:
        client = await Client.connect(
            settings.TEMPORAL_SERVER_URL,
            namespace=settings.TEMPORAL_NAMESPACE,
            rpc_metadata={"temporal-namespace": settings.TEMPORAL_NAMESPACE},
            api_key=settings.TEMPORAL_API_KEY,
            tls=True,
            runtime=runtime,
        )
    return client


async def trigger_po_creation_workflow_for_document(
    document_id: str, team_id: str
) -> str:
    """
    Directly trigger the PO Creation Workflow for external document imports.
    This bypasses the need for UserWorkflow configuration.

    Args:
        document_id: The ID of the uploaded document
        team_id: The team ID

    Returns:
        The Temporal workflow ID for tracking
    """
    from didero.workflows.core_workflows.po_creation.workflow import (
        POCreationParams,
        POCreationWorkflow,
    )

    client = await get_temporal_client()

    # Generate a unique workflow ID
    instance_id = uuid.uuid4()
    temporal_id = f"po-creation-document-import-team-{team_id}-{str(instance_id)}"

    # Create workflow params
    params = POCreationParams(
        team_id=team_id,
        workflow_id="",  # Empty since we're not using UserWorkflow
        document_id=document_id,
    )

    # Get the appropriate task queue
    task_queue = get_queue_for_workflow_type(WorkflowType.PURCHASE_ORDER_CREATION)

    log.info(
        "Triggering PO Creation Workflow for document import",
        document_id=document_id,
        team_id=team_id,
        temporal_id=temporal_id,
    )

    # Start the workflow
    await client.start_workflow(
        POCreationWorkflow.run,
        args=("", params),  # Empty workflow_id as first arg
        id=temporal_id,
        task_queue=task_queue,
    )

    # Return the workflow ID for tracking
    return temporal_id


# This is called from the shell at the moment. It's not exposed in the API in anyway yet.
def create_workflow(workflow_type, trigger, team_id, schema, params):
    team = Team.objects.get(id=team_id)

    with transaction.atomic():
        workflow = UserWorkflow.objects.create(
            workflow_type=workflow_type,
            trigger=trigger,
            team=team,
        )
        snapshot = WorkflowSnapshot.objects.create(
            workflow=workflow,
            graph=schema,
            params=params,
        )

        workflow.current_snapshot = snapshot
        workflow.save()

    return workflow


def create_or_update_workflow(workflow_type, trigger, team_id, schema, params):
    """Create a new workflow or update existing one for a team.

    This function handles the case where a workflow already exists for the
    given type, trigger, and team combination. Instead of failing with a
    unique constraint violation, it updates the existing workflow with a
    new snapshot.
    """
    team = Team.objects.get(id=team_id)

    with transaction.atomic():
        workflow, created = UserWorkflow.objects.get_or_create(
            workflow_type=workflow_type,
            trigger=trigger,
            team=team,
        )

        # Always create a new snapshot (whether workflow is new or existing)
        snapshot = WorkflowSnapshot.objects.create(
            workflow=workflow,
            graph=schema,
            params=params,
        )

        workflow.current_snapshot = snapshot
        workflow.save()

        if created:
            log.info(
                "Created new workflow",
                workflow_type=workflow_type,
                trigger=trigger,
                team_id=team_id,
                workflow_id=workflow.id,
            )
        else:
            log.info(
                "Updated existing workflow with new snapshot",
                workflow_type=workflow_type,
                trigger=trigger,
                team_id=team_id,
                workflow_id=workflow.id,
                snapshot_id=snapshot.id,
            )

    return workflow


def create_or_update_core_workflow(
    workflow_type: WorkflowType,
    trigger: WorkflowTrigger,
    team_id: str,
    config: Optional[WorkflowBehaviorConfigBase] = None,
) -> UserWorkflow:
    """Create a new core workflow for a team.

    Core workflows don't need DAG snapshots as they have their logic
    implemented in code. This function creates the workflow record and
    optionally its behavior configuration.

    Args:
        workflow_type: The type of workflow to create
        trigger: The trigger for the workflow
        team_id: The team ID
        config: Optional configuration dict for WorkflowBehaviorConfig

    Returns:
        The created or updated UserWorkflow instance
    """
    # Verify this is a core workflow type
    if workflow_type not in CORE_WORKFLOW_CLASSES:
        raise ValueError(f"{workflow_type} is not configured as a core workflow type")

    team = Team.objects.get(id=team_id)

    with transaction.atomic():
        workflow, created = UserWorkflow.objects.get_or_create(
            workflow_type=workflow_type,
            trigger=trigger,
            team=team,
        )

        # Core workflows don't need snapshots
        workflow.current_snapshot = None
        workflow.save()

        # Create or update behavior config if provided
        if config is not None:
            WorkflowBehaviorConfig.objects.update_or_create(
                workflow=workflow, defaults={"config": config.model_dump()}
            )

        if created:
            log.info(
                "Created new core workflow",
                workflow_type=workflow_type,
                trigger=trigger,
                team_id=team_id,
                workflow_id=workflow.id,
            )
        else:
            log.info(
                "Updated existing core workflow",
                workflow_type=workflow_type,
                trigger=trigger,
                team_id=team_id,
                workflow_id=workflow.id,
            )

    return workflow


def update_workflow_by_id(workflow_id, schema, params):
    workflow = UserWorkflow.objects.filter(pk=workflow_id).first()
    if not workflow:
        raise ValueError("Workflow not found")

    if schema is None or params is None:
        raise ValueError("Schema and params are required")

    with transaction.atomic():
        new_snapshot = WorkflowSnapshot.objects.create(
            workflow=workflow,
            graph=schema,
            params=params,
        )
        workflow.current_snapshot = new_snapshot
        workflow.save()

    return workflow


def update_workflow_by_type_and_trigger(workflow_type, trigger, team, schema, params):
    workflow = UserWorkflow.objects.filter(
        workflow_type=workflow_type, trigger=trigger, team=team
    ).first()
    if not workflow:
        raise ValueError("Workflow not found")

    return update_workflow_by_id(workflow.id, schema, params)


async def enqueue_dag_workflow(
    instance_uuid, graph_schema, params, temporal_id, workflow_type=None
):
    """
    Enqueue a DAG-based workflow for asynchronous execution by a Temporal worker.

    This function schedules the workflow execution in the specified task queue.
    The actual execution will happen when a worker picks up the job.

    Args:
        instance_uuid: UUID of the workflow run instance
        graph_schema: The DAG schema defining the workflow
        params: Parameters to pass to the workflow
        temporal_id: Unique identifier for the Temporal workflow execution
        workflow_type: Optional workflow type to determine queue routing
    """
    client = await get_temporal_client()
    # Determine the queue based on workflow type
    task_queue = (
        get_queue_for_workflow_type(workflow_type)
        if workflow_type
        else "user_workflows"
    )

    await client.start_workflow(
        DagBFSWorkflow.run,
        args=(instance_uuid, graph_schema, params),
        id=temporal_id,
        task_queue=task_queue,
    )


async def enqueue_core_workflow(workflow: UserWorkflow, params: dict, temporal_id: str):
    """
    Enqueue a core workflow for asynchronous execution by a Temporal worker.

    Core workflows are implemented in code (not DAG-based) and have dedicated
    workflow classes. This function schedules the workflow execution in the
    appropriate task queue based on the workflow type.

    Args:
        workflow: The UserWorkflow instance containing workflow configuration
        params: Parameters to pass to the workflow
        temporal_id: Unique identifier for the Temporal workflow execution
    """
    client = await get_temporal_client()
    workflow_class = workflow.get_core_workflow_class()

    if not workflow_class:
        raise ValueError(
            f"No core workflow class found for type: {workflow.workflow_type}"
        )

    # Use the dict format for all core workflows
    workflow_params = params

    # Determine the queue based on workflow type
    task_queue = get_queue_for_workflow_type(workflow.workflow_type)

    # Pass workflow_id as first argument, params as second
    await client.start_workflow(
        workflow_class.run,
        args=(str(workflow.id), workflow_params),
        id=temporal_id,
        task_queue=task_queue,
    )


def merge_params(global_params, workflow_params, instance_params):
    default_params = {
        **global_params,
        **workflow_params.get("default", {}),
        **instance_params.get("default", {}),
    }
    func_params = {
        node_id: {
            **workflow_params.get(node_id, {}),
            **instance_params.get(node_id, {}),
        }
        for node_id in set(workflow_params.keys()).union(instance_params.keys())
        if node_id not in ["default"]
    }
    return {
        "default": default_params,
        **func_params,
    }


def run_workflow(workflow_id: str, params: Mapping[str, Any]) -> Optional[WorkflowRun]:
    workflow = UserWorkflow.objects.get(id=workflow_id)

    instance_id = uuid.uuid4()
    temporal_id = f"{workflow.workflow_type}-{workflow.trigger}-team-{workflow.team.id}-{str(instance_id)}"

    # Check if this workflow uses a core implementation
    if workflow.uses_core_workflow:
        log.info(
            f"Enqueuing core workflow {workflow.workflow_type}",
            workflow_id=workflow_id,
            team_id=workflow.team.id,
            temporal_id=temporal_id,
        )

        # For core workflows, we pass structured params
        # The workflow will handle its own state management
        workflow_params = {
            "workflow_id": str(workflow.id),
            "team_id": str(workflow.team.id),
            **params,  # Include any additional params passed in
        }

        asyncio.run(enqueue_core_workflow(workflow, workflow_params, temporal_id))

        # For now, we don't create WorkflowRun for core workflows
        # They manage their own state internally
        return None

    # Legacy DAG-based workflow execution
    snapshot = workflow.current_snapshot
    if not snapshot:
        raise ValueError("Workflow has no current snapshot to run")

    workflow_state = {
        "overall_state": WorkflowRunState.QUEUED.value,
        **{
            node["id"]: WorkflowRunState.QUEUED.value
            for node in snapshot.graph.get("nodes", [])
        },
    }
    instance = WorkflowRun.objects.create(
        uuid=instance_id,
        temporal_id=temporal_id,
        snapshot=snapshot,
        params=params,
        state=workflow_state,
    )

    log.info(
        f"Run Workflow instance created {instance.uuid} {instance.temporal_id} {instance.state}"
    )
    global_params = {
        "team_id": workflow.team.id,
        "run_id": instance.uuid,
    }
    full_params = merge_params(global_params, snapshot.params, params)
    asyncio.run(
        enqueue_dag_workflow(
            str(instance.uuid),
            snapshot.graph,
            full_params,
            instance.temporal_id,
            workflow.workflow_type,
        )
    )
    return instance


async def run_workflow_async(workflow_id, params):
    workflow = await UserWorkflow.objects.select_related("team").aget(id=workflow_id)

    instance_id = uuid.uuid4()
    temporal_id = f"{workflow.workflow_type}-{workflow.trigger}-team-{workflow.team.id}-{str(instance_id)}"

    # Check if this workflow uses a core implementation
    if workflow.uses_core_workflow:
        log.info(
            f"Enqueuing core workflow {workflow.workflow_type} (async)",
            workflow_id=workflow_id,
            team_id=workflow.team.id,
            temporal_id=temporal_id,
        )

        # For core workflows, we pass structured params
        workflow_params = {
            "workflow_id": str(workflow.id),
            "team_id": str(workflow.team.id),
            **params,  # Include any additional params passed in
        }

        await enqueue_core_workflow(workflow, workflow_params, temporal_id)

        # For now, we don't create WorkflowRun for core workflows
        return None

    # Legacy DAG-based workflow execution
    snapshot = workflow.current_snapshot
    if not snapshot:
        raise ValueError("Workflow has no current snapshot to run")

    run_state = {
        "overall_state": WorkflowRunState.QUEUED.value,
        **{
            node["id"]: WorkflowRunState.QUEUED.value
            for node in snapshot.graph.get("nodes", [])
        },
    }
    instance = await WorkflowRun.objects.acreate(
        uuid=instance_id,
        temporal_id=temporal_id,
        snapshot=snapshot,
        params=params,
        state=run_state,
    )

    global_params = {
        "team_id": workflow.team.id,
        "run_id": instance.uuid,
    }
    full_params = merge_params(global_params, snapshot.params, params)
    await enqueue_dag_workflow(
        str(instance.uuid),
        snapshot.graph,
        full_params,
        instance.temporal_id,
        workflow.workflow_type,
    )
    return instance


def trigger_workflow_if_exists(
    workflow_type: str,
    trigger: str,
    team: Team,
    params: Mapping[str, Any] = {},
    context_object: Optional[WorkflowContext] = None,
) -> Optional[WorkflowRun]:
    """
    Use this to call workflows in the app
    Checks if a workflow type and a trigger exist and runs it if it does

    Args:
        workflow_type: The type of workflow to trigger
        trigger: The trigger event type
        team: The team to run the workflow for
        params: Parameters to pass to the workflow (deprecated, use context_object instead)
        context_object: The context object (e.g., email, purchase_order) to generate params from

    Returns:
        WorkflowRun for DAG workflows, None for core workflows (but None doesn't mean failure)
        To check if workflow was triggered, use trigger_workflow_if_exists_with_status instead
    """
    workflow = UserWorkflow.objects.filter(
        workflow_type=workflow_type, trigger=trigger, team=team
    ).first()

    if workflow:
        # If context_object is provided, generate params based on workflow type
        if context_object:
            final_params = _get_workflow_params_from_context(
                workflow_type=workflow_type,
                workflow=workflow,
                context_object=context_object,
            )
        else:
            # Use provided params directly (legacy behavior)
            final_params = params

        workflow_run = run_workflow(str(workflow.id), final_params)
        return workflow_run

    log.info(
        "Skipping workflow because it does not exist",
        workflow_type=workflow_type,
        trigger=trigger,
        team=team,
    )
    return None


@dataclass
class WorkflowTriggerResult:
    """Result of triggering a workflow"""

    triggered: bool
    workflow_found: bool
    is_core_workflow: bool
    workflow_run: Optional[WorkflowRun] = None
    temporal_id: Optional[str] = None
    error_message: Optional[str] = None


def trigger_workflow_if_exists_with_status(
    workflow_type: str,
    trigger: str,
    team: Team,
    params: Mapping[str, Any] = {},
    context_object: Optional[WorkflowContext] = None,
) -> WorkflowTriggerResult:
    """
    Enhanced version that returns detailed status about workflow triggering

    Args:
        workflow_type: The type of workflow to trigger
        trigger: The trigger event type
        team: The team to run the workflow for
        params: Parameters to pass to the workflow (deprecated, use context_object instead)
        context_object: The context object (e.g., email, purchase_order) to generate params from

    Returns:
        WorkflowTriggerResult with detailed status information
    """
    log.info(
        "Attempting to trigger workflow",
        workflow_type=workflow_type,
        trigger=trigger,
        team_id=team.id,
        has_context_object=context_object is not None,
    )

    workflow = UserWorkflow.objects.filter(
        workflow_type=workflow_type, trigger=trigger, team=team
    ).first()

    if not workflow:
        log.info(
            "Workflow not found for team",
            workflow_type=workflow_type,
            trigger=trigger,
            team_id=team.id,
        )
        return WorkflowTriggerResult(
            triggered=False,
            workflow_found=False,
            is_core_workflow=False,
            error_message="Workflow not configured for team",
        )

    log.info(
        "Found workflow, preparing to trigger",
        workflow_type=workflow_type,
        trigger=trigger,
        team_id=team.id,
        workflow_id=workflow.id,
        is_core_workflow=workflow.uses_core_workflow,
    )

    try:
        # If context_object is provided, generate params based on workflow type
        if context_object:
            log.info(
                "Generating workflow parameters from context",
                workflow_type=workflow_type,
                team_id=team.id,
                context_type=type(context_object).__name__,
            )
            final_params = _get_workflow_params_from_context(
                workflow_type=workflow_type,
                workflow=workflow,
                context_object=context_object,
            )
        else:
            log.info(
                "Using provided parameters directly",
                workflow_type=workflow_type,
                team_id=team.id,
            )
            final_params = params

        log.info(
            "Executing workflow",
            workflow_type=workflow_type,
            team_id=team.id,
            workflow_id=workflow.id,
            param_count=len(final_params),
        )

        workflow_run = run_workflow(str(workflow.id), final_params)

        # Generate temporal ID for tracking (same logic as run_workflow)
        instance_id = uuid.uuid4()
        temporal_id = f"{workflow.workflow_type}-{workflow.trigger}-team-{workflow.team.id}-{str(instance_id)}"

        log.info(
            "Workflow triggered successfully",
            workflow_type=workflow_type,
            team_id=team.id,
            workflow_id=workflow.id,
            is_core_workflow=workflow.uses_core_workflow,
            has_workflow_run=workflow_run is not None,
            temporal_id=temporal_id,
        )

        return WorkflowTriggerResult(
            triggered=True,
            workflow_found=True,
            is_core_workflow=workflow.uses_core_workflow,
            workflow_run=workflow_run,
            temporal_id=temporal_id,
        )

    except Exception as e:
        log.error(
            "Failed to trigger workflow",
            workflow_type=workflow_type,
            team_id=team.id,
            workflow_id=workflow.id,
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True,
        )
        return WorkflowTriggerResult(
            triggered=False,
            workflow_found=True,
            is_core_workflow=workflow.uses_core_workflow,
            error_message=str(e),
        )


async def asend_signal_to_workflow(workflow_id: str, node_id: str, key: str, value):
    client = await get_temporal_client()
    handle = client.get_workflow_handle(workflow_id)
    await handle.signal(DagBFSWorkflow.node_signal, args=(node_id, key, value))


def send_signal_to_workflow(workflow_id: str, node_id: str, key: str, value):
    asyncio.run(asend_signal_to_workflow(workflow_id, node_id, key, value))


def get_workflow_enabled_for_team(
    workflow_type: str, workflow_trigger: str, team: Team
) -> bool:
    workflow = UserWorkflow.objects.filter(
        workflow_type=workflow_type, trigger=workflow_trigger, team=team
    ).first()
    return workflow is not None


def is_followup_workflow_enabled_for_team(team: Team) -> bool:
    """
    Check if the followup workflow is enabled for the team via team settings.

    Args:
        team: The team to check settings for

    Returns:
        True if the workflow is enabled, False otherwise
    """
    try:
        from didero.users.utils.team_setting_utils import get_team_followup_config

        followup_config = get_team_followup_config(team)
        return followup_config.global_config.workflow_enabled
    except Exception:
        return False  # Default to disabled if configuration doesn't exist


def get_followup_wait_time_hours(team: Team) -> float:
    """
    Get the follow-up wait time in hours for the given team.

    Args:
        team: The team to get settings for

    Returns:
        Wait time in hours, defaults to 24 if not set
    """
    try:
        setting = TeamSetting.objects.get(
            team=team,
            name=TeamSettingEnums.TEAM_FOLLOWUP_WAIT_TIME_HOURS.value,
            user=None,  # Team-level setting
        )
        if setting.value is not None:
            # Ensure the value is a float
            wait_time = float(setting.value)
            if wait_time > 0:
                return wait_time
    except (TeamSetting.DoesNotExist, ValueError, TypeError):
        pass

    # Default to 24 hours if not set or invalid
    return 24


async def enqueue_followup_workflow(
    team: Team,
    purchase_order_id: str,
    follow_up_type: str,
    email_object_id: str = "",
    email_thread_id: str = "",
    follow_up_wait_time_hours: float = 24,
    num_follow_up_retries: int = 3,
) -> str:
    """
    Enqueue a Followup workflow for asynchronous execution by a Temporal worker.

    This function schedules a FollowupWorkflow execution in the appropriate task queue
    based on the workflow type. The FollowupWorkflow doesn't use the DAG structure
    and has its own dedicated workflow implementation.

    Note: This function is kept separate from enqueue_core_workflow because:
    1. FollowupWorkflow doesn't have a UserWorkflow model instance
    2. It's triggered programmatically from within other workflows
    3. It uses team settings instead of workflow configuration
    4. It has unique parameters specific to follow-up logic

    Args:
        team: Team associated with the purchase order
        purchase_order_id: ID of the purchase order to follow up on
        email_object_id: ID of the email object for context
        email_thread_id: ID of the email thread
        follow_up_type: Type of follow-up (FollowUpType enum)
        follow_up_wait_time_hours: Hours to wait between follow-ups (can be fractional)
        num_follow_up_retries: Number of times to retry following up

    Returns:
        Workflow execution ID
    """
    client = await get_temporal_client()

    params = {
        "email_object_id": email_object_id,
        "email_thread_id": email_thread_id,
        "purchase_order_id": purchase_order_id,
        "follow_up_type": follow_up_type,
        "follow_up_wait_time_hours": follow_up_wait_time_hours,
        "num_follow_up_retries": num_follow_up_retries,
    }

    # Create a unique workflow ID
    workflow_id = f"followup-{team.id}-{purchase_order_id}-{uuid.uuid4()}"

    # Determine the queue based on workflow type
    task_queue = get_queue_for_workflow_type(WorkflowType.FOLLOWUP_WORKFLOW)

    # Enqueue the workflow for execution
    handle = await client.start_workflow(
        FollowupWorkflow.run,
        args=[params],
        id=workflow_id,
        task_queue=task_queue,
    )

    log.info(
        "Enqueued follow-up workflow",
        workflow_id=handle.id,
        team_id=team.id,
        purchase_order_id=purchase_order_id,
    )

    return handle.id


def _get_workflow_params_from_context(
    workflow_type: str,
    workflow: UserWorkflow,
    context_object: WorkflowContext,
) -> Mapping[str, Any]:
    """
    Generate workflow parameters from context object based on workflow type.

    This function handles the logic of determining whether a workflow uses
    core or DAG implementation and calls the appropriate parameter getter.

    Args:
        workflow_type: The type of workflow
        workflow: The UserWorkflow instance
        context_object: The context object (usually an email or purchase order)

    Returns:
        Parameters dictionary formatted for the workflow type
    """
    # Import parameter functions here to avoid circular imports
    from didero.suppliers.models import Communication

    if workflow_type == WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT:
        from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack import (
            get_purchase_order_acknowledgement_workflow_parameters,
        )

        # Handle both EmailWorkflowContext and raw Communication for backward compatibility
        if isinstance(context_object, EmailWorkflowContext):
            email = context_object.email
        elif isinstance(context_object, Communication):
            email = context_object
        else:
            log.error(
                f"Invalid context type for PO acknowledgement: {type(context_object)}"
            )
            return {}

        return get_purchase_order_acknowledgement_workflow_parameters(
            email, workflow.uses_core_workflow
        )

    elif workflow_type == WorkflowType.PURCHASE_ORDER_CREATION:
        from didero.workflows.core.nodes.purchase_orders.po_creation import (
            get_po_creation_workflow_parameters,
        )

        # Handle both EmailWorkflowContext and raw Communication for backward compatibility
        if isinstance(context_object, EmailWorkflowContext):
            email = context_object.email
        elif isinstance(context_object, Communication):
            email = context_object
        else:
            log.error(f"Invalid context type for PO creation: {type(context_object)}")
            return {}

        return get_po_creation_workflow_parameters(email, workflow.uses_core_workflow)

    elif workflow_type == WorkflowType.PURCHASE_ORDER_SHIPPED:
        from didero.workflows.core.nodes.purchase_orders.shipments import (
            get_shipping_workflow_parameters,
        )

        # Handle ShipmentWorkflowContext with notification_type
        if isinstance(context_object, ShipmentWorkflowContext):
            return get_shipping_workflow_parameters(
                context_object.email,
                context_object.notification_type,
                workflow.uses_core_workflow,
            )
        elif isinstance(context_object, (EmailWorkflowContext, Communication)):
            # Backward compatibility: treat as email without notification_type
            email = (
                context_object.email
                if isinstance(context_object, EmailWorkflowContext)
                else context_object
            )

            return get_shipping_workflow_parameters(
                email, None, workflow.uses_core_workflow
            )
        else:
            log.error(
                f"Invalid context type for shipment workflow: {type(context_object)}"
            )
            return {}

    elif workflow_type == WorkflowType.INVOICE_PROCESSING:
        from didero.workflows.shared_activities.invoice_operations import (
            get_invoice_workflow_parameters,
        )

        log.info(
            "Getting invoice workflow parameters",
            workflow_type=workflow_type,
            workflow=workflow,
            context_object=context_object,
        )

        # Handle both EmailWorkflowContext and raw Communication for backward compatibility
        if isinstance(context_object, EmailWorkflowContext):
            email = context_object.email
        elif isinstance(context_object, Communication):
            email = context_object
        else:
            log.error(
                f"Invalid context type for invoice processing: {type(context_object)}"
            )
            return {}

        return get_invoice_workflow_parameters(email)

    elif workflow_type == WorkflowType.SHIPPING_DOCUMENT_PROCESSING:
        from didero.workflows.core_workflows.shipping_document_processing.workflow import (
            ShippingDocumentProcessingParams,
        )

        # Handle both EmailWorkflowContext and raw Communication for backward compatibility
        if isinstance(context_object, EmailWorkflowContext):
            email = context_object.email
        elif isinstance(context_object, Communication):
            email = context_object
        else:
            log.error(
                f"Invalid context type for shipping document processing: {type(context_object)}"
            )
            return {}

        # Return parameters as a dictionary for the workflow system
        return {
            "email_id": str(email.id),
            "team_id": str(email.team_id),
        }

    else:
        # For unknown workflow types, return empty params
        log.warning(
            f"No parameter handler for workflow type: {workflow_type}",
            workflow_id=workflow.id,
        )
        return {}
