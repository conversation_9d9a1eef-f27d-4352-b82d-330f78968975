{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">
  <style>
    .approval-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    .approval-table th, .approval-table td {
      padding: 8px;
      border: 1px solid #ddd;
      text-align: left;
    }
    .approval-table th, .approval-table tfoot td {
      background-color: var(--header-bg);
      color: var(--header-fg);
    }
    .approval-table tbody tr {
      background-color: var(--darkened-bg);
    }
    
    .approval-table tbody tr:nth-child(even) {
      background-color: var(--body-bg);
    }
  </style>
{% endblock %}

{% block extrahead %}
  {{ block.super }}
  <script src="{% static 'admin/js/vendor/jquery/jquery.min.js' %}"></script>
  <script type="text/javascript">
    // Wait for document to be ready
    document.addEventListener('DOMContentLoaded', function() {
      // Use vanilla JavaScript to avoid jQuery dependency issues
      var teamSelect = document.getElementById('id_team');
      var approverSelect = document.getElementById('id_approver');
      var approverGroupSelect = document.getElementById('id_approver_group');
      var approverTypeSelect = document.getElementById('id_approver_type');
      
      // Function to load users and groups based on selected team
      function loadApprovers() {
        var teamId = teamSelect.value;
        if (teamId) {
          // Load users
          var xhr = new XMLHttpRequest();
          xhr.open('GET', "{% url 'admin:ajax_load_users' %}?team=" + teamId, true);
          xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
              approverSelect.innerHTML = xhr.responseText;
            }
          };
          xhr.send();
          
          // Load groups
          var xhrGroups = new XMLHttpRequest();
          xhrGroups.open('GET', "{% url 'admin:ajax_load_groups' %}?team=" + teamId, true);
          xhrGroups.onreadystatechange = function() {
            if (xhrGroups.readyState === 4 && xhrGroups.status === 200) {
              approverGroupSelect.innerHTML = xhrGroups.responseText;
            }
          };
          xhrGroups.send();
        } else {
          approverSelect.innerHTML = '<option value="">---------</option>';
          approverGroupSelect.innerHTML = '<option value="">---------</option>';
        }
      }
      
      // Function to toggle approver fields based on type
      function toggleApproverFields() {
        var approverType = approverTypeSelect.value;
        var approverRow = approverSelect.closest('.form-row');
        var approverGroupRow = approverGroupSelect.closest('.form-row');
        
        if (approverType === 'user') {
          approverRow.style.display = '';
          approverGroupRow.style.display = 'none';
        } else if (approverType === 'group') {
          approverRow.style.display = 'none';
          approverGroupRow.style.display = '';
        } else {
          approverRow.style.display = '';
          approverGroupRow.style.display = '';
        }
      }
      
      // Add change event listeners
      if (teamSelect) {
        teamSelect.addEventListener('change', loadApprovers);
        
        // Trigger the change event on page load if a team is selected
        if (teamSelect.value) {
          loadApprovers();
        }
      }
      
      if (approverTypeSelect) {
        approverTypeSelect.addEventListener('change', toggleApproverFields);
        // Initialize visibility on page load
        toggleApproverFields();
      }
    });
  </script>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_label|capfirst }}</a>
&rsaquo; <a href="{% url 'admin:workflows_userworkflow_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; {% trans 'Manage PO Approval Workflows' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <h1>{% trans "Manage Purchase Order Approval Workflows" %}</h1>
  
  <div class="module">
    <h2>{% trans "Current Approval Assignments" %}</h2>
    {% if team_approver_map %}
      <table class="approval-table">
        <thead>
          <tr>
            <th>{% trans "Team" %}</th>
            <th>{% trans "Approver" %}</th>
            <th>{% trans "Threshold" %}</th>
            <th>{% trans "Workflow ID" %}</th>
            <th>{% trans "Actions" %}</th>
          </tr>
        </thead>
        <tbody>
          {% for team_id, info in team_approver_map.items %}
            <tr>
              <td>{{ info.team_name }}</td>
              <td>{{ info.approver_name }}</td>
              <td>{{ info.threshold }}</td>
              <td>{{ info.workflow_id }}</td>
              <td>
                <form method="post" action="" style="display:inline;">
                  {% csrf_token %}
                  <input type="hidden" name="delete_workflow" value="{{ info.workflow_id }}">
                  <button type="submit" 
                         onclick="return confirm('{% trans "Are you sure you want to delete this workflow?" %}');"
                         class="deletelink" style="border:none;background:none;cursor:pointer;color:#ba2121;text-decoration:underline;padding:0;">
                    {% trans "Delete" %}
                  </button>
                </form>
              </td>
            </tr>
          {% endfor %}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="5">{% trans "Total workflows" %}: {{ team_approver_map|length }}</td>
          </tr>
        </tfoot>
      </table>
    {% else %}
      <p>{% trans "No active PO approval workflows found." %}</p>
    {% endif %}
  </div>

  <div class="module">
    <h2>{% trans "Create or Update Approval Workflow" %}</h2>
    <form action="" method="post" id="po-approval-form">
      {% csrf_token %}
      <fieldset class="module aligned">
        {% for field in form %}
          <div class="form-row">
            <div class="field-box">
              {{ field.errors }}
              {{ field.label_tag }}
              {{ field }}
              {% if field.help_text %}
                <div class="help">{{ field.help_text|safe }}</div>
              {% endif %}
            </div>
          </div>
        {% endfor %}
      </fieldset>
      
      <div class="submit-row">
        <input type="submit" value="{% trans 'Save' %}" class="default" />
      </div>
    </form>
  </div>
</div>

{% endblock %}