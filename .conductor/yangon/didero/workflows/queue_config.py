"""Temporal queue configuration for workflow routing."""

from didero.workflows.schemas import WorkflowType

# Default queue for workflows that don't have specific routing
DEFAULT_QUEUE = "user_workflows"

# Mapping of workflow types to their dedicated queues
WORKFLOW_QUEUE_MAPPING: dict[WorkflowType, str] = {
    WorkflowType.PURCHASE_ORDER_CREATION: "po_creation_queue",
    WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT: "order_ack_queue",
    WorkflowType.PURCHASE_ORDER_SHIPPED: "shipment_queue",
    WorkflowType.PURCHASE_ORDER_FOLLOW_UP: "follow_up_queue",
    WorkflowType.INVOICE_PROCESSING: "invoice_processing_queue",
    WorkflowType.SHIPPING_DOCUMENT_PROCESSING: "shipping_document_queue",
    # Add more mappings as needed
}


def get_queue_for_workflow_type(workflow_type: str) -> str:
    """
    Get the appropriate Temporal queue for a given workflow type.

    Args:
        workflow_type: The workflow type string

    Returns:
        The queue name to use for this workflow type
    """
    # Convert string to WorkflowType enum for lookup
    try:
        wf_type = WorkflowType(workflow_type)
        return WORKFLOW_QUEUE_MAPPING.get(wf_type, DEFAULT_QUEUE)
    except ValueError:
        # If it's not a valid WorkflowType, use default queue
        return DEFAULT_QUEUE
