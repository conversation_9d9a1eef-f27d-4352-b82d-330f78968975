import logging
import os
import subprocess
from typing import Dict, List, Optional

from django.conf import settings
from django.core.management.base import BaseCommand

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Sync Temporal search attributes across all environments"

    # Define search attributes to create
    SEARCH_ATTRIBUTES = [
        {"name": "TeamId", "type": "Keyword"},
        {"name": "PurchaseOrderNumber", "type": "Keyword"},
        {"name": "PurchaseOrderId", "type": "Keyword"},
        {"name": "SupplierId", "type": "Keyword"},
        {"name": "DocumentId", "type": "Keyword"},
        {"name": "EmailId", "type": "Keyword"},
        {"name": "WorkflowCategory", "type": "Keyword"},
    ]

    def __init__(self):
        super().__init__()
        self.temporal_config = self._get_temporal_config()

    def _get_temporal_config(self) -> Dict[str, str]:
        """Get Temporal configuration using TEMPORAL_SERVER_URL and TEMPORAL_NAMESPACE"""
        # Use TEMPORAL_SERVER_URL if available, otherwise fall back to TEMPORAL_ADDRESS
        server_url = os.environ.get("TEMPORAL_SERVER_URL") or os.environ.get(
            "TEMPORAL_ADDRESS", "localhost:7233"
        )
        namespace = os.environ.get("TEMPORAL_NAMESPACE", "default")

        # Extract address from URL (remove http/https prefix if present)
        address = server_url.replace("http://", "").replace("https://", "")

        return {
            "address": address,
            "namespace": namespace,
        }

    def _run_temporal_command(
        self, command: List[str], check: bool = False
    ) -> subprocess.CompletedProcess:
        """Run a temporal CLI command with current environment configuration"""
        config = self.temporal_config
        full_command = [
            "temporal",
            "--address",
            config["address"],
            "--namespace",
            config["namespace"],
        ] + command

        try:
            result = subprocess.run(
                full_command, capture_output=True, text=True, check=check
            )
            return result
        except subprocess.CalledProcessError as e:
            logger.error(f"Error running command: {e.stderr}")
            return e
        except FileNotFoundError:
            logger.error("Temporal CLI not found. Please ensure it's installed.")
            return None

    def create_search_attribute(self, name: str, attr_type: str) -> bool:
        """Create a single search attribute in the current environment"""
        config = self.temporal_config
        self.stdout.write(
            f"Creating '{name}' ({attr_type}) at {config['address']} namespace {config['namespace']}..."
        )

        result = self._run_temporal_command(
            [
                "operator",
                "search-attribute",
                "create",
                "--name",
                name,
                "--type",
                attr_type,
            ]
        )

        if result is None:
            self.stdout.write(self.style.ERROR("✗ Temporal CLI not available"))
            return False

        if result.returncode == 0:
            self.stdout.write(self.style.SUCCESS(f"✓ Created '{name}'"))
            return True
        elif "already exists" in result.stderr.lower():
            self.stdout.write(self.style.WARNING(f"✓ '{name}' already exists"))
            return True
        else:
            self.stdout.write(self.style.ERROR(f"✗ Failed: {result.stderr}"))
            return False

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be created without actually creating",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        config = self.temporal_config

        if dry_run:
            self.stdout.write("DRY RUN MODE - No changes will be made")
            self.stdout.write(
                f"\nWould sync to: {config['address']} namespace {config['namespace']}"
            )
            self.stdout.write("\nSearch attributes to create:")
            for attr in self.SEARCH_ATTRIBUTES:
                self.stdout.write(f"  - {attr['name']} ({attr['type']})")
            return

        # Track results
        total_success = 0
        total_failed = 0

        self.stdout.write(f"\n{'='*50}")
        self.stdout.write("Syncing to Temporal environment")
        self.stdout.write(f"Address: {config['address']}")
        self.stdout.write(f"Namespace: {config['namespace']}")
        self.stdout.write(f"{'='*50}\n")

        for attr in self.SEARCH_ATTRIBUTES:
            success = self.create_search_attribute(attr["name"], attr["type"])
            if success:
                total_success += 1
            else:
                total_failed += 1

        # Summary
        self.stdout.write(f"\n{'='*50}")
        self.stdout.write("SUMMARY")
        self.stdout.write(f"{'='*50}")
        self.stdout.write(f"Total successful: {total_success}")
        self.stdout.write(f"Total failed: {total_failed}")

        if total_failed > 0:
            self.stdout.write(
                self.style.WARNING(
                    "\nSome attributes failed to create. Check logs above."
                )
            )
