"""
Django management command to migrate PO Creation workflows from DAG to core implementation.

Usage:
    python manage.py migrate_po_workflow_to_core --team-id <team_id> [--dry-run]
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from didero.users.models.team_models import Team
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig
from didero.workflows.schemas import (
    POCreationBehaviorConfig,
    WorkflowTrigger,
    WorkflowType,
)


class Command(BaseCommand):
    help = "Migrate PO Creation workflow from DAG-based to core implementation"

    def add_arguments(self, parser):
        parser.add_argument(
            "--team-id",
            type=str,
            required=True,
            help="Team ID to migrate workflow for",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Analyze without making changes",
        )
        parser.add_argument(
            "--require-human-validation",
            type=str,
            choices=["true", "false", "preserve"],
            default="preserve",
            help="Override human validation setting (default: preserve existing)",
        )

    def handle(self, *args, **options):
        team_id = options["team_id"]
        dry_run = options["dry_run"]
        human_validation_override = options["require_human_validation"]

        try:
            team = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Team {team_id} not found"))
            return

        # Find PO Creation workflow
        try:
            workflow = UserWorkflow.objects.get(
                team=team,
                workflow_type=WorkflowType.PURCHASE_ORDER_CREATION,
                trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED,
            )
        except UserWorkflow.DoesNotExist:
            self.stdout.write(
                self.style.WARNING(
                    f"No PO Creation workflow found for team {team.name}"
                )
            )
            return

        # Check if already migrated
        if workflow.current_snapshot is None:
            # Check if it has behavior config
            try:
                behavior_config = workflow.behavior_config
                config = behavior_config.get_config_as_pydantic()
                self.stdout.write(
                    self.style.SUCCESS(
                        "Workflow already migrated to core implementation"
                    )
                )
                self.stdout.write(f"Current config: {config.model_dump_json(indent=2)}")
                return
            except WorkflowBehaviorConfig.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        "Workflow appears to be core but has no behavior config"
                    )
                )

        # Analyze DAG workflow
        self.stdout.write(f"\nAnalyzing DAG workflow for team: {team.name}")

        if workflow.current_snapshot:
            snapshot = workflow.current_snapshot
            self.stdout.write(f"  - Snapshot ID: {snapshot.id}")
            self.stdout.write(f"  - Created: {snapshot.created_at}")

            # Show nodes in the DAG
            nodes = snapshot.graph.get("nodes", [])
            self.stdout.write(f"  - Nodes in DAG: {len(nodes)}")
            for node in nodes:
                self.stdout.write(
                    f"    • {node.get('type')}: {node.get('label', 'No label')}"
                )

        # Extract configuration from team settings
        config_dict = self._extract_configuration(team, human_validation_override)

        self.stdout.write("\nExtracted configuration:")
        for key, value in config_dict.items():
            self.stdout.write(f"  - {key}: {value}")

        if dry_run:
            self.stdout.write(self.style.WARNING("\nDRY RUN - No changes will be made"))
            return

        # Perform migration
        self.stdout.write("\nMigrating workflow...")

        try:
            with transaction.atomic():
                # Clear the DAG snapshot reference
                workflow.current_snapshot = None
                workflow.save()

                # Create behavior config
                config = POCreationBehaviorConfig(**config_dict)
                behavior_config, created = (
                    WorkflowBehaviorConfig.objects.update_or_create(
                        workflow=workflow,
                        defaults={"config": config.model_dump()},
                    )
                )

                self.stdout.write(
                    self.style.SUCCESS(
                        "✓ Successfully migrated workflow to core implementation"
                    )
                )
                self.stdout.write(
                    f"  - Behavior config {'created' if created else 'updated'}"
                )
                self.stdout.write(f"  - Workflow ID: {workflow.id}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Migration failed: {str(e)}"))
            raise

    def _extract_configuration(
        self, team: Team, human_validation_override: str
    ) -> dict:
        """Extract configuration from team settings and defaults."""
        config = {
            "enabled": True,
            "enable_notifications": True,
            "validate_supplier": True,
            "validate_duplicate_po": True,
            "notification_channels": ["email"],
        }

        # Handle human validation setting
        if human_validation_override == "preserve":
            # Check team setting
            try:
                setting = TeamSetting.objects.get(
                    team=team,
                    name=TeamSettingEnums.TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED.value,
                    user=None,
                )
                config["require_human_validation"] = bool(setting.value)
                self.stdout.write(
                    f"  - Using existing human validation setting: {setting.value}"
                )
            except TeamSetting.DoesNotExist:
                # Default to False for core workflows
                config["require_human_validation"] = False
                self.stdout.write(
                    "  - No human validation setting found, defaulting to False"
                )
        else:
            # Use override
            config["require_human_validation"] = human_validation_override == "true"
            self.stdout.write(
                f"  - Overriding human validation to: {human_validation_override}"
            )

        # Check for notifications setting
        try:
            setting = TeamSetting.objects.get(
                team=team,
                name=TeamSettingEnums.TEAM_TASK_EMAIL_NOTIFICATIONS_ENABLED.value,
                user=None,
            )
            config["enable_notifications"] = bool(setting.value)
        except TeamSetting.DoesNotExist:
            pass

        return config
