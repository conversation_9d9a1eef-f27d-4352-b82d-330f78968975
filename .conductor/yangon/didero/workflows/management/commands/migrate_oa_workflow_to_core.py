"""
Management command to migrate Order Acknowledgement workflow from DAG to core implementation.
"""

import json

from django.core.management.base import BaseCommand
from django.db import transaction

from didero.users.models.team_models import Team
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig
from didero.workflows.schemas import (
    OrderAcknowledgementBehaviorConfig,
    WorkflowTrigger,
    WorkflowType,
)


class Command(BaseCommand):
    help = """
    Migrate Order Acknowledgement workflow from DAG-based to core implementation.
    
    This command:
    1. Finds the existing DAG-based OA workflow for a team
    2. Extracts configuration from team settings
    3. Creates a behavior config based on team settings
    4. Clears the DAG snapshot to enable core workflow
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "--team-id",
            type=str,
            required=True,
            help="The team ID to migrate",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be migrated without making changes",
        )
        parser.add_argument(
            "--human-validation",
            type=str,
            choices=["true", "false", "keep"],
            default="keep",
            help="Override human validation setting (keep=use existing team setting)",
        )

    def _extract_configuration(
        self, team: Team, human_validation_override: str
    ) -> dict:
        """Extract OA workflow configuration from team settings."""
        config = {}

        # Get human validation setting
        if human_validation_override == "keep":
            try:
                setting = TeamSetting.objects.get(
                    team=team,
                    name=TeamSettingEnums.TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED.value,
                )
                config["require_human_validation"] = bool(setting.value)
            except TeamSetting.DoesNotExist:
                config["require_human_validation"] = False
        else:
            config["require_human_validation"] = human_validation_override == "true"

        # Set default validation flags (can be customized later)
        config["validate_info"] = True
        config["validate_items"] = True
        config["validate_freight_costs"] = True

        # Enable notifications by default
        config["enable_notifications"] = True
        config["notification_channels"] = ["email"]

        return config

    def handle(self, *args, **options):
        team_id = options["team_id"]
        dry_run = options["dry_run"]
        human_validation_override = options["human_validation"]

        try:
            team = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Team with ID {team_id} does not exist")
            )
            return

        # Find the OA workflow
        try:
            workflow = UserWorkflow.objects.get(
                team=team,
                workflow_type=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT,
                trigger=WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED,
            )
        except UserWorkflow.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(
                    f"No Order Acknowledgement workflow found for team {team.name}"
                )
            )
            return

        self.stdout.write(
            f"\nFound Order Acknowledgement workflow for team: {team.name}"
        )
        self.stdout.write(f"Workflow ID: {workflow.id}")

        # Check if already migrated
        if workflow.current_snapshot is None:
            # Check if it has behavior config
            try:
                behavior_config = workflow.behavior_config
                config = behavior_config.get_config_as_pydantic()
                self.stdout.write(
                    self.style.SUCCESS(
                        "Workflow already migrated to core implementation"
                    )
                )
                self.stdout.write(f"Current config: {config.model_dump_json(indent=2)}")
                return
            except WorkflowBehaviorConfig.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        "Workflow appears to be core but has no behavior config"
                    )
                )

        # Show current DAG structure
        if workflow.current_snapshot:
            self.stdout.write("\nCurrent DAG implementation:")
            self.stdout.write(f"  Snapshot ID: {workflow.current_snapshot.id}")
            self.stdout.write(f"  Created: {workflow.current_snapshot.created_at}")

            nodes = workflow.current_snapshot.graph.get("nodes", [])
            self.stdout.write(f"  Number of nodes: {len(nodes)}")
            self.stdout.write("  Nodes:")
            for node in nodes:
                self.stdout.write(
                    f"    • {node.get('type')}: {node.get('node_class', 'No class')}"
                )

        # Extract configuration from team settings
        config_dict = self._extract_configuration(team, human_validation_override)

        self.stdout.write("\nExtracted configuration:")
        for key, value in config_dict.items():
            self.stdout.write(f"  - {key}: {value}")

        if dry_run:
            self.stdout.write(self.style.WARNING("\nDRY RUN - No changes will be made"))
            return

        # Perform migration
        self.stdout.write("\nMigrating workflow...")

        try:
            with transaction.atomic():
                # Clear the DAG snapshot reference
                workflow.current_snapshot = None
                workflow.save()

                # Create behavior config
                config = OrderAcknowledgementBehaviorConfig(**config_dict)
                behavior_config, created = (
                    WorkflowBehaviorConfig.objects.update_or_create(
                        workflow=workflow,
                        defaults={"config": config.model_dump()},
                    )
                )

                self.stdout.write(
                    self.style.SUCCESS(
                        "✓ Successfully migrated workflow to core implementation"
                    )
                )
                self.stdout.write(
                    f"  - Behavior config {'created' if created else 'updated'}"
                )
                self.stdout.write(f"  - Workflow ID: {workflow.id}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Migration failed: {str(e)}"))
            raise

        self.stdout.write(
            self.style.NOTICE(
                "\nNote: The temporal worker must be restarted to pick up the changes"
            )
        )
