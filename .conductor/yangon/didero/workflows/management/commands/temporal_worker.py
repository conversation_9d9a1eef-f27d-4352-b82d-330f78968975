import asyncio
import os
from concurrent.futures import Thread<PERSON>oolExecutor

from django.conf import settings
from django.core.management.base import BaseCommand
from temporalio import workflow
from temporalio.client import (
    Client,
)
from temporalio.worker import Worker

from didero.telemetry.temporal_metrics import get_temporal_runtime
from didero.workflows.core.nodes.follow_up.activities import (
    create_follow_up_task,
    is_order_shipped,
    order_partially_shipped,
)
from didero.workflows.core.nodes.meta import save_state_to_db
from didero.workflows.core.nodes.purchase_orders.approvals import (
    get_purchase_order_approval,
    is_purchase_order_cost_geq,
    set_purchase_order_to_approved,
)
from didero.workflows.core.nodes.purchase_orders.netsuite_sync import (
    netsuite_po_sync_activity,
)
from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.order_ack import (
    extract_order_acknowledgement_details,
    order_acknowledgement_post_validation_actions,
)
from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.validation import (
    validate_order_acknowledgement_freight_costs,
    validate_order_acknowledgement_info,
    validate_order_acknowledgement_items,
)
from didero.workflows.core.nodes.purchase_orders.po_creation.activities import (
    create_po,
    extract_po_details,
    extract_po_from_erp,
    link_email,
)
from didero.workflows.core.nodes.purchase_orders.shipments import (
    check_notification_type,
    create_shipment_from_details,
    parse_shipment_details_from_email,
    process_pickup_notification,
    submit_shipment_to_netsuite,
    validate_shipment_email,
)
from didero.workflows.core.nodes.tasks import (
    create_task_activity,
    create_task_v2_activity,
)
from didero.workflows.core.user_workflow_definitions import (
    DagBFSWorkflow,
)
from didero.workflows.core_workflows.activities import (
    get_task_assignment_user_id,
    load_workflow_config,
)
from didero.workflows.core_workflows.follow_up.activities import (
    attempt_follow_up_activity,
    calculate_business_hours_delay_activity,
    oa_follow_up_activity,
    ship_date_follow_up_activity,
    shipment_follow_up_activity,
)
from didero.workflows.core_workflows.follow_up.workflow import FollowUpWorkflow
from didero.workflows.core_workflows.invoice_processing.activities import (
    store_invoice_activity,
    update_invoice_po_link_activity,
)
from didero.workflows.core_workflows.invoice_processing.workflow import (
    InvoiceProcessingWorkflow,
)
from didero.workflows.core_workflows.order_ack.activities import (
    save_oa_activity,
)
from didero.workflows.core_workflows.order_ack.workflow import (
    OrderAcknowledgementWorkflow,
)
from didero.workflows.core_workflows.po_creation.workflow import POCreationWorkflow
from didero.workflows.core_workflows.shipments.workflow import ShipmentWorkflow
from didero.workflows.core_workflows.shipping_document_processing.workflow import (
    ShippingDocumentProcessingWorkflow,
)
from didero.workflows.shared_activities.document_matching import (
    create_document_match_review_task_activity,
    match_documents_activity,
)
from didero.workflows.shared_activities.document_retrieval import (
    retrieve_document_activity,
    search_document_activity,
)
from didero.workflows.shared_activities.follow_up_data import (
    get_followup_config_activity,
)
from didero.workflows.shared_activities.invoice_operations import (
    extract_invoice_details,
)
from didero.workflows.shared_activities.purchase_order_operations import (
    get_purchase_order_activity,
    get_purchase_order_details_activity,
    get_purchase_order_id_from_po_number_activity,
    resolve_purchase_order_with_auto_creation,
    retrieve_purchase_order_activity,
)
from didero.workflows.shared_activities.shipping_document_operations import (
    extract_shipping_document_details,
    store_shipping_document_from_email_activity,
)
from didero.workflows.shared_activities.supplier_document_operations import (
    extract_supplier_document,
    resolve_purchase_order_for_document,
)

with workflow.unsafe.imports_passed_through():
    import structlog

server_url = settings.TEMPORAL_SERVER_URL

log = structlog.get_logger(__name__)

ALL_ACTIVITIES = [
    get_purchase_order_approval,
    set_purchase_order_to_approved,
    save_state_to_db,
    create_task_activity,
    create_task_v2_activity,
    is_purchase_order_cost_geq,
    validate_shipment_email,
    parse_shipment_details_from_email,
    create_shipment_from_details,
    submit_shipment_to_netsuite,
    check_notification_type,
    process_pickup_notification,
    extract_order_acknowledgement_details,
    order_acknowledgement_post_validation_actions,
    validate_order_acknowledgement_freight_costs,
    validate_order_acknowledgement_info,
    validate_order_acknowledgement_items,
    netsuite_po_sync_activity,
    # PO Creation workflow activities (used by both DAG and explicit workflows)
    extract_po_details,
    extract_po_from_erp,
    create_po,
    link_email,
    # Follow-up workflow activities (legacy)
    is_order_shipped,
    order_partially_shipped,
    create_follow_up_task,
    # New follow-up workflow activities
    get_purchase_order_activity,
    get_followup_config_activity,
    oa_follow_up_activity,
    ship_date_follow_up_activity,
    shipment_follow_up_activity,
    attempt_follow_up_activity,
    calculate_business_hours_delay_activity,
    load_workflow_config,
    get_task_assignment_user_id,
    # Invoice processing workflow activities
    extract_invoice_details,
    match_documents_activity,
    retrieve_purchase_order_activity,
    get_purchase_order_details_activity,
    get_purchase_order_id_from_po_number_activity,
    resolve_purchase_order_with_auto_creation,
    store_invoice_activity,
    update_invoice_po_link_activity,
    create_document_match_review_task_activity,
    # Shared document retrieval activities
    retrieve_document_activity,
    search_document_activity,
    # New OA workflow activities
    save_oa_activity,
    # Shipping document activities
    extract_shipping_document_details,
    store_shipping_document_from_email_activity,
    # Split supplier document activities
    extract_supplier_document,
    resolve_purchase_order_for_document,
]


async def run_worker(queue_names: str):
    # Get the metrics-enabled runtime
    runtime = get_temporal_runtime()

    # Get max workers from environment or use default
    max_workers = int(os.environ.get("TEMPORAL_MAX_WORKERS", "10"))

    # Parse queue names (comma-separated)
    queues = [q.strip() for q in queue_names.split(",")]

    if server_url.startswith("localhost"):
        client = await Client.connect(
            settings.TEMPORAL_SERVER_URL,
            runtime=runtime,
        )
    else:
        client = await Client.connect(
            settings.TEMPORAL_SERVER_URL,
            namespace=settings.TEMPORAL_NAMESPACE,
            rpc_metadata={"temporal-namespace": settings.TEMPORAL_NAMESPACE},
            api_key=settings.TEMPORAL_API_KEY,
            tls=True,
            runtime=runtime,
        )

    ### Code for scheduled workflows, enable when we want to schedule things
    ###
    # async for schedule in await client.list_schedules():
    #     handle = client.get_schedule_handle(schedule.id)
    #     await handle.delete()

    # await client.create_schedule(
    #     "email-reconciliation",
    #     Schedule(
    #         action=ScheduleActionStartWorkflow(
    #             EmailReconciliationWorkflow.run,
    #             24 * 60 * 60 * 2,  # look for emails in the last 2 days
    #             id="email-reconciliation",
    #             task_queue="scheduled_tasks",
    #         ),
    #         spec=ScheduleSpec(
    #             intervals=[ScheduleIntervalSpec(every=timedelta(minutes=1))]
    #         ),
    #         state=ScheduleState(note="Here's a note on my Schedule."),
    #     ),
    # )

    # Create and run workers for each queue
    workers = []
    for queue in queues:
        worker = Worker(
            client,
            task_queue=queue,
            workflows=[
                DagBFSWorkflow,
                FollowUpWorkflow,
                POCreationWorkflow,
                OrderAcknowledgementWorkflow,
                ShipmentWorkflow,
                InvoiceProcessingWorkflow,
                ShippingDocumentProcessingWorkflow,
            ],
            activities=ALL_ACTIVITIES,
            activity_executor=ThreadPoolExecutor(max_workers=max_workers),
            workflow_failure_exception_types=[
                Exception
            ],  # fail on all exceptions for now for visibility
        )
        workers.append(worker)
        log.info(
            f"Created Temporal worker for queue '{queue}' with {max_workers} max workers"
        )

    # Run all workers concurrently
    if len(workers) == 1:
        # Single worker - just run it
        await workers[0].run()
    else:
        # Multiple workers - run them concurrently
        await asyncio.gather(*[worker.run() for worker in workers])


class Command(BaseCommand):
    help = "Runs a temporal worker"

    def add_arguments(self, parser):
        parser.add_argument(
            "--queue",
            type=str,
            help="Specify the task queue name(s). Use comma-separated values for multiple queues",
            required=True,
        )

    def handle(self, *args, **options):
        queue_name = options["queue"]
        log.info(f"Starting worker with queue name: {queue_name}")
        asyncio.run(run_worker(queue_name))
