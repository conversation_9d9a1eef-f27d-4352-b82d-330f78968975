"""
Signal utilities for Temporal workflows.

This module provides helper functions for sending signals to workflows,
particularly for the document upload completion flow.
"""

from typing import Any, Optional

import structlog
from temporalio.client import Client

logger = structlog.get_logger(__name__)


async def send_workflow_signal(
    workflow_id: str,
    signal_name: str,
    signal_data: Any,
    client: Optional[Client] = None,
) -> bool:
    """
    Send a signal to a workflow.

    Args:
        workflow_id: ID of the target workflow
        signal_name: Name of the signal to send
        signal_data: Data to send with the signal
        client: Optional Temporal client (will create if not provided)

    Returns:
        True if signal sent successfully, False otherwise
    """
    if not client:
        try:
            # Use the existing get_temporal_client function
            from didero.workflows.utils import get_temporal_client

            client = await get_temporal_client()
        except Exception:
            logger.error("Failed to connect to Temporal server", exc_info=True)
            return False

    try:
        workflow_handle = client.get_workflow_handle(workflow_id)
        await workflow_handle.signal(signal_name, signal_data)

        logger.info(
            "Successfully sent signal to workflow",
            workflow_id=workflow_id,
            signal_name=signal_name,
        )
        return True

    except Exception as e:
        logger.error(
            "Failed to send signal to workflow",
            workflow_id=workflow_id,
            signal_name=signal_name,
            error=str(e),
            exc_info=True,
        )
        return False


async def send_document_upload_success_signal(
    workflow_id: str,
    document_data: Any,
    client: Optional[Client] = None,
) -> bool:
    """
    Send a document upload success signal to a workflow.

    Args:
        workflow_id: ID of the target workflow
        document_data: The uploaded and processed document data
        client: Optional Temporal client

    Returns:
        True if signal sent successfully, False otherwise
    """
    return await send_workflow_signal(
        workflow_id=workflow_id,
        signal_name="document_uploaded_signal",
        signal_data=document_data,
        client=client,
    )


async def send_document_upload_failure_signal(
    workflow_id: str,
    error_message: str,
    client: Optional[Client] = None,
) -> bool:
    """
    Send a document upload failure signal to a workflow.

    Args:
        workflow_id: ID of the target workflow
        error_message: Error message describing the failure
        client: Optional Temporal client

    Returns:
        True if signal sent successfully, False otherwise
    """
    return await send_workflow_signal(
        workflow_id=workflow_id,
        signal_name="document_upload_failed_signal",
        signal_data=error_message,
        client=client,
    )
