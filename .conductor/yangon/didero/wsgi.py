"""
WSGI config for didero project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import os

import structlog
from django.core.wsgi import get_wsgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

application = get_wsgi_application()

logger = structlog.get_logger(__name__)

# Import settings after Django is initialized
from django.conf import settings

# Initialize OpenTelemetry
try:
    from didero.telemetry import setup_telemetry

    # Check if metrics are enabled (defaults to False if not set)
    metrics_enabled = getattr(settings, "OTEL_METRICS_ENABLED", False)

    # Initialize OpenTelemetry with settings from Django
    tracer_provider, meter_provider = setup_telemetry(
        app_name=settings.OTEL_SETTINGS.get("SERVICE_NAME", "didero-api"),
        env=settings.OTEL_SETTINGS.get("ENVIRONMENT", "development"),
        metrics_enabled=metrics_enabled,
    )

    # Set up Celery monitoring
    from didero.telemetry import setup_celery_monitoring

    setup_celery_monitoring()

    logger.info(
        "OpenTelemetry instrumentation initialized",
        service_name=settings.OTEL_SETTINGS.get("SERVICE_NAME"),
        exporter_endpoint=settings.OTEL_SETTINGS.get("EXPORTER_OTLP_ENDPOINT"),
        metrics_enabled=metrics_enabled,
    )
except Exception as e:
    # Don't crash the application if telemetry setup fails
    logger.exception("Failed to initialize OpenTelemetry", error=str(e))
    # Continue with the application without telemetry
