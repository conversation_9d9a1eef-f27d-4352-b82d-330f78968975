import structlog
from celery import shared_task
from celery_singleton import Singleton

from didero.emails.postmark_service import PostmarkEmailService
from didero.runtime_configs.models import RuntimeConfig
from didero.users.models.user_models import User

logger = structlog.get_logger(__name__)

LOCK_EXPIRE = 60 * 60  # 1 hour


@shared_task(base=Singleton, lock_expiry=LOCK_EXPIRE, queue="periodic_tasks")
def send_notifications_reminder_email():
    if RuntimeConfig.get_tasks_email_service_disabled():
        logger.info(
            "RuntimeConfig is disabling the email service; skipping 'send_notifications_reminder_email'"
        )
        return

    for user in User.objects.all():
        try:
            PostmarkEmailService.send_unread_notifications_email(user)
        except Exception as e:
            # log errors but swallow them so we can try to send notifications to all users
            logger.error(e)
