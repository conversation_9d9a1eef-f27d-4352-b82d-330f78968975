from django.contrib.contenttypes.models import ContentType

from didero.notifications.models import Notification
from didero.utils.utils import get_didero_ai_user


class NotificationHelper:
    def create_notification_for_po_status_update(
        po, old_status, new_status, actor=None
    ):
        if not actor or actor is None:
            actor = get_didero_ai_user(po.placed_by.teams.first())

        # Didero AI updated PO-123 from DRAFT to APPROVED
        # <PERSON> updated PO-234 from APPROVAL_PENDING to APPROVED
        return Notification.objects.create(
            recipient=po.placed_by,
            team=po.placed_by.teams.first(),
            actor=actor,
            verb=f"updated {po.po_number} from {old_status} to {new_status}",
            item_type=ContentType.objects.get_for_model(po),
            item_id=po.pk,
        )

    def create_approve_po_notification(po, approver, next_approver):
        return Notification.objects.create(
            recipient=po.placed_by,
            team=po.placed_by.teams.first(),
            actor=approver,
            verb=f"approved {po.po_number}. Sending to the next approver: {next_approver.display_name}",
            item_type=ContentType.objects.get_for_model(po),
            item_id=po.pk,
        )

    def create_deny_po_notification(po, approver):
        return Notification.objects.create(
            recipient=po.placed_by,
            team=po.placed_by.teams.first(),
            actor=approver,
            verb=f"denied {po.po_number}",
            item_type=ContentType.objects.get_for_model(po),
            item_id=po.pk,
        )
