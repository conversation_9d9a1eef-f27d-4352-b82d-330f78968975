from typing import TYPE_CHECKING

from admin_extra_buttons.api import ExtraButtonsMixin
from django.contrib import admin

from didero.notifications.models import Notification

if TYPE_CHECKING:
    TypedNotificationAdmin = admin.ModelAdmin[Notification]
else:
    TypedNotificationAdmin = admin.ModelAdmin


class NotificationAdmin(ExtraButtonsMixin, TypedNotificationAdmin):  # type: ignore
    autocomplete_fields = ["recipient", "team", "actor"]


admin.site.register(Notification, NotificationAdmin)
