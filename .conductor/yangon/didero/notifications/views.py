from django.utils import timezone
from rest_framework import serializers, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.notifications.serializers import NotificationSerializer
from didero.views import APIView, PagesPagination


class NotificationPagination(PagesPagination):
    """
    Adds a count of unread notifications into the
    regular PagesPagination schema
    """

    def get_paginated_response(self, data):
        return Response(
            {
                "next": self.get_next_link(),
                "previous": self.get_previous_link(),
                "count": self.page.paginator.count,
                "countUnread": self.request.user.get_notifications(
                    team=self.request.team,
                    unread_only=True,
                ).count(),
                "results": data,
            }
        )


class UserNotificationViewset(APIView, viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = NotificationPagination

    class MarkReadSerializer(serializers.Serializer):
        read = serializers.ListField(
            child=serializers.IntegerField(), allow_empty=True, required=False
        )
        unread = serializers.ListField(
            child=serializers.IntegerField(), allow_empty=True, required=False
        )

    def get_queryset(self):
        user = self.request.user
        team = self.request.team
        unread_only = self.request.GET.get("unread") == "1"
        return user.get_notifications(team=team, unread_only=unread_only)

    @action(detail=False, methods=["post"])
    def status(self, request):
        user = request.user
        szlr = self.validate_or_error(request, szlr_class=self.MarkReadSerializer)

        mark_as_read = szlr.validated_data.get("read")
        mark_as_unread = szlr.validated_data.get("unread")
        notifications = user.get_notifications(team=request.team)

        if mark_as_read:
            notifications.filter(id__in=mark_as_read).update(read_at=timezone.now())
        if mark_as_unread:
            notifications.filter(id__in=mark_as_unread).update(read_at=None)

        paginator = self.pagination_class()
        result_page = paginator.paginate_queryset(self.get_queryset(), request)
        serializer = self.serializer_class(result_page, many=True)
        return paginator.get_paginated_response(serializer.data)
