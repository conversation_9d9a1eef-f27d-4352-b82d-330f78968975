# Generated by Django 4.2.7 on 2024-03-21 12:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("users", "0014_alter_team_uuid"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("read_at", models.DateTimeField(blank=True, default=None, null=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                (
                    "verb",
                    models.CharField(
                        help_text='What the actor did (eg, "mentioned you")',
                        max_length=64,
                    ),
                ),
                ("target_id", models.PositiveIntegerField()),
                ("item_id", models.PositiveIntegerField()),
                (
                    "actor",
                    models.ForeignKey(
                        help_text="The user that performed the verb",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "item_type",
                    models.ForeignKey(
                        help_text="The item itself. eg, the note you were mentioned in. Clicking the notification will generally navigate the user to this item.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        help_text="The user we're notifying",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "target_type",
                    models.ForeignKey(
                        help_text="The target is the object they acted on. (eg, the Supplier a note was created on)",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        help_text="The relevant team for this user & notification",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.team",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["target_type", "target_id"],
                        name="notificatio_target__1f2fb1_idx",
                    ),
                    models.Index(
                        fields=["item_type", "item_id"],
                        name="notificatio_item_ty_600883_idx",
                    ),
                ],
            },
        ),
    ]
