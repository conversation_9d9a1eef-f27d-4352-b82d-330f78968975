# Generated by Django 4.2.7 on 2024-06-04 20:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("notifications", "0002_notification_email_sent_at"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="notification",
            name="notificatio_target__1f2fb1_idx",
        ),
        migrations.RemoveField(
            model_name="notification",
            name="target_id",
        ),
        migrations.RemoveField(
            model_name="notification",
            name="target_type",
        ),
        migrations.AlterField(
            model_name="notification",
            name="item_type",
            field=models.ForeignKey(
                help_text="The item itself. eg, the note you were mentioned in. Clicking the notification will navigate the user to this item.",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="contenttypes.contenttype",
            ),
        ),
    ]
