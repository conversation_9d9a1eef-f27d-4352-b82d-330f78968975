import structlog
from rest_framework import serializers

from didero.notifications.models import Notification
from didero.serializers import PoppableModelSzlr
from didero.suppliers.models import Supplier, SupplierComment
from didero.suppliers.serializers import (
    BasicSupplierSerializer,
    SupplierCommentSerializer,
)
from didero.users.serializers import UserSerializer

logger = structlog.get_logger(__name__)


class NotificationGenericRelationField(serializers.RelatedField):
    """
    A custom field to use for the `tagged_object` generic relationship.
    """

    def to_representation(self, value):
        """
        Serialize tagged objects to a simple textual representation.
        """
        if isinstance(value, Supplier):
            return BasicSupplierSerializer(value).data
        elif isinstance(value, SupplierComment):
            return SupplierCommentSerializer(value).data
        else:
            logger.info(
                "no known representation for notification relation",
                value=value,
                cls=value.__class__,
            )
            return {"id": value.pk}


class NotificationSerializer(PoppableModelSzlr):
    actor = UserSerializer(read_only=True)
    item = NotificationGenericRelationField(read_only=True)
    item_type = serializers.SerializerMethodField(read_only=True)

    def get_item_type(self, value):
        return value.item.__class__.__name__.lower()

    class Meta:
        model = Notification
        read_only_fields = (
            "id",
            "created_at",
            "read_at",
            "verb",
            "actor",
            "item",
            "item_type",
        )
        fields = read_only_fields
