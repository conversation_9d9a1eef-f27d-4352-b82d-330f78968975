from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.timezone import now

from didero.models import BaseModel


class Notification(BaseModel):
    """
    Describes a notification for a specific user.

    Consists of an actor, a verb, an item.

    "<PERSON> (actor) mentioned you in xyz (verb)"
    The item would be the comment itself.
    """

    recipient = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="notifications",
        help_text="The user we're notifying",
    )
    team = models.ForeignKey(
        "users.Team",
        on_delete=models.CASCADE,
        help_text="The relevant team for this user & notification",
    )
    read_at = models.DateTimeField(default=None, null=True, blank=True)
    created_at = models.DateTimeField(default=now, db_index=True)
    email_sent_at = models.DateTimeField(default=None, null=True, blank=True)

    actor = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        related_name="+",
        help_text="The user that performed the verb",
    )
    verb = models.CharField(
        max_length=64,
        help_text='What the actor did (eg, "mentioned you")',
    )

    item = GenericForeignKey("item_type", "item_id")
    item_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        related_name="+",
        help_text="The item itself. eg, the comment you were mentioned in. Clicking the notification will navigate the user to this item.",
    )
    item_id = models.PositiveIntegerField()

    class Meta:
        indexes = [
            models.Index(fields=["item_type", "item_id"]),
        ]

    def __str__(self):
        return f"{self.pk}: {self.actor.email} {self.verb}"

    def build_preview_string(self):
        return f"{self.actor.display_name} {self.verb}"
