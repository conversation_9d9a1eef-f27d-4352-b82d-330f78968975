import structlog
from django_filters.rest_framework import DjangoFilterB<PERSON><PERSON>
from rest_framework.filters import Ordering<PERSON>ilter
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from didero.invoices.filters import InvoiceFilter
from didero.invoices.models import Invoice
from didero.invoices.serializers import InvoiceDetailSerializer, InvoiceListSerializer
from didero.views import APIView, PagesPagination, TeamOwnerFilteredModelView

logger = structlog.get_logger(__name__)


class InvoiceListView(APIView, TeamOwnerFilteredModelView[Invoice]):
    """
    List view for invoices with filtering and pagination support.
    """

    model = Invoice
    serializer_class = InvoiceListSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_class = InvoiceFilter

    # The fields we can sort by
    ordering_fields = [
        "invoice_number",
        "invoice_date",
        "due_date",
        "created_at",
        "modified_at",
    ]
    ordering = ["-invoice_date"]  # Default ordering is by invoice date descending

    def get_queryset(self):
        """
        Get invoices for the current team with related data prefetched.
        """
        queryset = super().get_queryset()

        # Prefetch related data to optimize queries
        queryset = queryset.select_related(
            "purchase_order",
            "supplier",
            "team",
        )

        return queryset

    def list(self, request: Request, *args, **kwargs):  # type: ignore[override]
        """
        List invoices with filtering and pagination.
        """
        queryset = self.filter_queryset(self.get_queryset())

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class InvoiceDetailView(APIView, TeamOwnerFilteredModelView[Invoice]):
    """
    Detail view for a single invoice with all related data.
    """

    model = Invoice
    serializer_class = InvoiceDetailSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Get invoices for the current team with related data prefetched.
        """
        queryset = super().get_queryset()

        # Optimize queries with select_related and prefetch_related
        queryset = queryset.select_related(
            "purchase_order",
            "supplier",
            "billing_address",
            "team",
        ).prefetch_related(
            "items",
            "items__item",
            "line_items",
        )

        return queryset

    def retrieve(self, request: Request, pk: int | None = None):
        """
        Get a specific invoice with all details.
        """
        logger.info(
            "Retrieving invoice",
            invoice_id=pk,
            team_id=request.team.pk,  # type: ignore[attr-defined]
            user_id=request.user.id,  # type: ignore[attr-defined]
        )

        invoice = self.get_object()
        serializer = self.get_serializer(invoice)
        return Response(serializer.data)
