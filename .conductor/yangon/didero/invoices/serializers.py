from rest_framework import serializers

from didero.addresses.serializers import AddressSerializer
from didero.invoices.models import Invoice, InvoiceItem, InvoiceLineItem
from didero.orders.serializers import BasicPurchaseOrderSerializer


class InvoiceLineItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = InvoiceLineItem
        fields = [
            "id",
            "category",
            "description",
            "amount",
            "amount_currency",
        ]


class InvoiceItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = InvoiceItem
        fields = [
            "id",
            "item",
            "item_number",
            "item_description",
            "quantity",
            "unit_price",
            "unit_price_currency",
            "total_price",
            "total_price_currency",
        ]


class InvoiceListSerializer(serializers.ModelSerializer):
    """
    Serializer for invoice list view - includes key fields for display.
    """

    purchase_order = BasicPurchaseOrderSerializer(read_only=True)

    # Split out details
    supplier = serializers.SerializerMethodField()
    document = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = [
            "id",
            "invoice_number",
            "invoice_date",
            "due_date",
            "payment_terms",
            "purchase_order",
            "notes",
            "special_instructions",
            "created_at",
            "modified_at",
            "supplier",
            "document",
        ]
        read_only_fields = ["created_at", "modified_at"]

    def get_supplier(self, obj: Invoice) -> dict[str, int | str | None] | None:
        """
        Get supplier details from the invoice.
        """
        if obj.supplier:
            return {
                "id": obj.supplier.id,
                "name": obj.supplier.name,
                "domain": obj.supplier.website_url,
            }
        return None

    def get_document(self, obj: Invoice) -> dict[str, int | str | None] | None:
        """
        Get invoice document from the model method.
        """
        document_data = obj.get_document()
        if document_data:
            # Transform to match the expected format
            return {
                "uuid": document_data.get("id"),  # Using id as uuid for now
                "name": document_data.get("name"),
            }
        return None


class InvoiceDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for single invoice view - includes all fields and nested data.
    """

    purchase_order = BasicPurchaseOrderSerializer(read_only=True)
    billing_address = AddressSerializer(read_only=True)
    items = InvoiceItemSerializer(many=True, read_only=True)
    line_items = InvoiceLineItemSerializer(many=True, read_only=True)

    # Split out details
    supplier = serializers.SerializerMethodField()
    document = serializers.SerializerMethodField()

    # Computed fields
    total_amount = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = [
            "id",
            "invoice_number",
            "invoice_date",
            "due_date",
            "payment_terms",
            "purchase_order",
            "billing_address",
            "notes",
            "special_instructions",
            "items",
            "line_items",
            "created_at",
            "modified_at",
            "total_amount",
            "supplier",
            "document",
        ]
        read_only_fields = ["created_at", "modified_at"]

    def get_total_amount(self, obj: Invoice) -> dict[str, str]:
        """
        Calculate total amount including items and line items.
        """
        # Get sum of invoice items
        items_total = sum(item.total_price.amount for item in obj.items.all())  # type: ignore[attr-defined]

        # Get sum of line items (extra charges)
        line_items_total = sum(
            line_item.amount.amount
            for line_item in obj.line_items.all()  # type: ignore[attr-defined]
        )

        total = items_total + line_items_total

        # Get currency from first item or line item, default to USD
        currency = "USD"
        if obj.items.exists():  # type: ignore[attr-defined]
            currency = str(obj.items.first().total_price_currency)  # type: ignore[attr-defined]
        elif obj.line_items.exists():  # type: ignore[attr-defined]
            currency = str(obj.line_items.first().amount_currency)  # type: ignore[attr-defined]

        return {"amount": str(total), "currency": currency}

    def get_supplier(self, obj: Invoice) -> dict[str, int | str | None] | None:
        """
        Get supplier details from the invoice.
        """
        if obj.supplier:
            return {
                "id": obj.supplier.id,
                "name": obj.supplier.name,
                "domain": obj.supplier.website_url,
            }
        return None

    def get_document(self, obj: Invoice) -> dict[str, int | str] | None:
        """
        Get invoice document from the model method.
        """
        return obj.get_document()
