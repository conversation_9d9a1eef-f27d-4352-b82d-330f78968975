from enum import Enum
from typing import TYPE_CHECKING, List, Optional

from pydantic import BaseModel, Field

from didero.addresses.schemas import AddressSchema
from didero.orders.schemas import OrderAcknowledgementOrderItem, PaymentStatus

if TYPE_CHECKING:
    from didero.invoices.models import Invoice as InvoiceModel


class InvoiceStatus(str, Enum):
    MISMATCH = "mismatch"
    MATCHED = "matched"
    UPDATED_IN_ERP = "updated_in_erp"
    AWAITING_DOCUMENT = "awaiting_document"


class Invoice(BaseModel):
    # Core identifiers
    po_number: str = Field(..., description="Customer's purchase order number")
    invoice_number: str = Field(..., description="Supplier's invoice number")
    email_id: str = Field(..., description="The email ID of the invoice email")

    # Company information
    billing_address: AddressSchema = Field(
        ..., description="The address where the invoice should be billed to"
    )

    # Invoice details
    invoice_date: str = Field(..., description="Date when the invoice was issued")
    due_date: str = Field(..., description="Date when payment is due")
    order_items: List[OrderAcknowledgementOrderItem] = (
        Field(  # OrderItem is same between OrderAck and Invoice. Not renaming for now to avoid breaking changes.
            ..., description="List of items in the invoice"
        )
    )

    # Pricing information
    subtotal: str = Field(..., description="Subtotal amount of the invoice")
    tax_rate: Optional[float] = Field(
        None, description="Applicable tax rate for the invoice"
    )
    tax_amount: Optional[str] = Field(
        None, description="Total tax amount for the invoice"
    )
    total_amount: str = Field(
        ..., description="Total amount of the invoice including all charges"
    )
    shipping_or_freight_charges: Optional[str] = Field(
        None, description="Shipping or freight charges applicable to the invoice"
    )
    other_special_charges: Optional[str] = Field(
        None, description="Other special charges applicable to the invoice"
    )

    # Payment details
    payment_terms: Optional[str] = Field(
        None, description="Terms of payment, e.g., Net 30, 2% 10 Net 30"
    )
    payment_status: Optional[PaymentStatus] = Field(
        None, description="Current status of the payment"
    )

    # Additional information
    notes: Optional[str] = Field(
        None, description="Additional notes related to the invoice"
    )
    special_instructions: Optional[str] = Field(
        None, description="Special instructions for handling the invoice"
    )

    @classmethod
    def from_model(
        cls, invoice_model: "InvoiceModel", email_id: Optional[str] = None
    ) -> "Invoice":
        """
        Convert Django Invoice model to Invoice schema.

        Args:
            invoice_model: Django Invoice instance
            email_id: Optional email ID if available

        Returns:
            Invoice schema instance
        """

        # Convert items
        items = []
        if hasattr(invoice_model, "items"):
            for item in invoice_model.items.all():
                items.append(
                    OrderAcknowledgementOrderItem(
                        item_number=item.item_number,
                        item_description=item.item_description,
                        quantity=float(item.quantity),
                        unit_of_measure=item.unit_of_measure,
                        unit_price=str(item.unit_price.amount)
                        if item.unit_price
                        else "0",
                        total_price=str(item.total_price.amount)
                        if item.total_price
                        else "0",
                        is_backorder=False,  # Default value
                        promised_ship_date=None,
                        promised_delivery_date=None,
                    )
                )

        # Convert billing address
        if invoice_model.billing_address:
            billing_address = AddressSchema(
                line_1=invoice_model.billing_address.line_1 or "",
                line_2=invoice_model.billing_address.line_2 or "",
                city=invoice_model.billing_address.city or "",
                state_or_province=invoice_model.billing_address.state_or_province or "",
                postal_code=invoice_model.billing_address.postal_code or "",
                country=invoice_model.billing_address.country or "",
            )
        else:
            billing_address = None

        return cls(
            po_number=invoice_model.purchase_order.po_number
            if invoice_model.purchase_order
            else "",
            invoice_number=invoice_model.invoice_number,
            email_id=email_id or "",
            billing_address=billing_address,
            invoice_date=invoice_model.invoice_date.isoformat()
            if invoice_model.invoice_date
            else "",
            due_date=invoice_model.due_date.isoformat()
            if invoice_model.due_date
            else "",
            order_items=items,
            subtotal="0",  # Calculate if needed
            tax_rate=None,
            tax_amount=None,
            total_amount="0",  # Calculate if needed
            shipping_or_freight_charges=None,
            other_special_charges=None,
            payment_terms=invoice_model.payment_terms,
            payment_status=None,
            notes=invoice_model.notes,
            special_instructions=None,
        )
