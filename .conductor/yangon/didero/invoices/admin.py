from django.contrib import admin

from didero.invoices.models import Invoice, InvoiceItem, InvoiceLineItem


class InvoiceLineItemInline(admin.TabularInline):
    model = InvoiceLineItem
    extra = 0
    fields = ("category", "description", "amount")


class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 0
    fields = (
        "item",
        "item_number",
        "item_description",
        "quantity",
        "unit_of_measure",
        "unit_price",
        "total_price",
    )
    readonly_fields = ("total_price",)


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = (
        "invoice_number",
        "supplier",
        "team",
        "invoice_date",
        "due_date",
        "payment_terms",
        "created_at",
    )
    list_filter = (
        "invoice_date",
        "due_date",
        "payment_terms",
        "supplier",
        "team",
        "created_at",
    )
    search_fields = ("invoice_number", "supplier__name", "team__name", "notes")
    raw_id_fields = ("purchase_order", "supplier", "team", "billing_address")
    inlines = [InvoiceItemInline, InvoiceLineItemInline]

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("invoice_number", "invoice_date", "due_date", "payment_terms")},
        ),
        (
            "Relationships",
            {"fields": ("purchase_order", "supplier", "team", "billing_address")},
        ),
        (
            "Additional Information",
            {"fields": ("notes", "special_instructions"), "classes": ("collapse",)},
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "modified_at"),
                "classes": ("collapse",),
                "description": "Automatically managed timestamps",
            },
        ),
    )
    readonly_fields = ("created_at", "modified_at")

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("supplier", "team", "purchase_order")
        )


@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    list_display = (
        "invoice",
        "item_number",
        "item_description",
        "quantity",
        "unit_price",
        "total_price",
    )
    list_filter = ("invoice__invoice_date", "unit_of_measure")
    search_fields = ("item_number", "item_description", "invoice__invoice_number")
    raw_id_fields = ("invoice", "item")

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("invoice", "item")


@admin.register(InvoiceLineItem)
class InvoiceLineItemAdmin(admin.ModelAdmin):
    list_display = ("invoice", "category", "description", "amount")
    list_filter = ("category", "invoice__invoice_date")
    search_fields = ("description", "invoice__invoice_number")
    raw_id_fields = ("invoice",)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("invoice")
