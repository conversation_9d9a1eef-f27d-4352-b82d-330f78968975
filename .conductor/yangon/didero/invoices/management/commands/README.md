# Invoice Seeding Command

The `seed_invoices` management command creates comprehensive test data for invoice-related features.

## What it creates

For each invoice, the command creates:

1. **Suppliers** (up to 3) with:
   - Contact information (email, phone, website)
   - Physical addresses

2. **Purchase Orders** with:
   - Multiple statuses (APPROVED, PARTIALLY_RECEIVED, RECEIVED)
   - Realistic order dates and delivery dates
   - Shipping and sender addresses

3. **Items** (10 different catalog items) with:
   - Item numbers, descriptions, units of measure
   - Realistic pricing

4. **Order Items** (3-6 per PO) with:
   - Random quantities
   - Slight price variations from catalog prices

5. **Invoices** with:
   - Proper invoice numbers and dates
   - Payment terms (Net 15, Net 30, Net 45, Net 60, 2/10 Net 30)
   - Due dates calculated from payment terms
   - Billing addresses
   - Reference notes and special instructions

6. **Invoice Items** matching the PO items

7. **Invoice Line Items** including:
   - Sales tax (5-10%)
   - Shipping charges ($25-$150)
   - Occasional discounts (30% chance, 2-5% off)

8. **Invoice Documents**:
   - PDF documents with invoice details
   - Linked to the purchase order via DocumentLink
   - Contains invoice number, items, line items, and total
   - Stored as Document objects with doc_type=INVOICE

## Usage

```bash
# Create 5 invoices (default)
uv run python manage.py seed_invoices

# Create 10 invoices
uv run python manage.py seed_invoices --count 10

# Clear existing invoices and create new ones
uv run python manage.py seed_invoices --clear --count 5

# View help
uv run python manage.py seed_invoices --help
```

## Prerequisites

- At least one user must exist in the database
- The user must belong to a team

If no users exist, create one first or run the demo data generation.

## Example Output

```
Creating 5 invoices with related data...
Created supplier: TechParts Global Ltd.
Created supplier: Industrial Supply Co.
Created supplier: Component Masters Inc.
Created invoice INV-2024-45678 for TechParts Global Ltd. with 4 items, 3 line items, and invoice document
Created invoice INV-2024-78901 for Industrial Supply Co. with 5 items, 2 line items, and invoice document
...
Successfully created 5 invoices with all related data
```