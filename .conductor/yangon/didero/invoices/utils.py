"""
Invoice utility functions for email thread linking and document management.
"""

from typing import <PERSON>ple

import structlog

from didero.emails.models import EmailThreadToInvoiceLink
from didero.invoices.models import Invoice
from didero.suppliers.models import Communication

logger = structlog.get_logger(__name__)


def link_invoice_to_email_thread(
    invoice: Invoice, communication: Communication
) -> Tuple[EmailThreadToInvoiceLink, bool]:
    """
    Link an invoice to an email thread, following the same pattern as POs.

    Args:
        invoice: Invoice instance to link
        communication: Communication instance containing the email thread

    Returns:
        Tuple of (link_instance, created_boolean)
    """
    if not communication.email_thread:
        logger.warning(
            f"Communication {communication.id} has no email_thread, cannot link to invoice {invoice.id}"
        )
        return None, False

    link, created = EmailThreadToInvoiceLink.objects.get_or_create(
        email_thread=communication.email_thread,
        invoice=invoice,
    )

    if created:
        logger.info(
            f"Linked invoice {invoice.id} to email thread {communication.email_thread.id}"
        )
    else:
        logger.debug(
            f"Invoice {invoice.id} already linked to email thread {communication.email_thread.id}"
        )

    return link, created


def get_related_communications_for_invoice(invoice: Invoice):
    """
    Get all communications related to an invoice through email thread links.

    Args:
        invoice: Invoice instance

    Returns:
        QuerySet of Communication objects ordered by most recent
    """
    email_thread_ids = EmailThreadToInvoiceLink.objects.filter(
        invoice=invoice
    ).values_list("email_thread_id", flat=True)

    return Communication.objects.filter(email_thread__id__in=email_thread_ids).order_by(
        "-comm_time"
    )
