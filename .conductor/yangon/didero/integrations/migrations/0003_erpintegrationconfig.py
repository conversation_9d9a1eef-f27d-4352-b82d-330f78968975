# Generated by Django 4.2.7 on 2025-06-24 19:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0055_alter_teamsetting_name"),
        ("integrations", "0002_teamintegratedappcredential"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPIntegrationConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "erp_type",
                    models.CharField(
                        help_text="Type of ERP system (e.g., netsuite, sap, oracle)",
                        max_length=50,
                    ),
                ),
                (
                    "enabled",
                    models.Boolean<PERSON>ield(
                        default=False,
                        help_text="Whether ERP sync is enabled for this team",
                    ),
                ),
                (
                    "field_mappings",
                    models.JSONField(
                        default=dict, help_text="Mapping of ERP fields to Didero fields"
                    ),
                ),
                (
                    "config",
                    models.J<PERSON><PERSON>ield(
                        default=dict,
                        help_text="ERP-specific configuration (endpoints, versions, etc.)",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "team",
                    models.OneToOneField(
                        help_text="Team this ERP configuration belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="erp_config",
                        to="users.team",
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP Integration Config",
                "verbose_name_plural": "ERP Integration Configs",
                "db_table": "erp_integration_config",
            },
        ),
    ]
