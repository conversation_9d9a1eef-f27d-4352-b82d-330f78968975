# Generated by Django 4.2.7 on 2024-06-28 20:26

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("users", "0019_remove_team_default_address"),
    ]

    operations = [
        migrations.CreateModel(
            name="TeamIntegrationProvider",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("provider", models.CharField(choices=[("alloy", "ALLOY")])),
                ("provider_user_id", models.CharField(max_length=256)),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="integration_providers",
                        to="users.team",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
