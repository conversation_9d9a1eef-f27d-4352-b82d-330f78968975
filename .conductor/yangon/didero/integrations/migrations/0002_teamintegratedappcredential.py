# Generated by Django 4.2.7 on 2024-07-10 20:33

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0019_remove_team_default_address"),
        ("integrations", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TeamIntegratedAppCredential",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "app_name",
                    models.CharField(
                        choices=[
                            ("netsuite", "NETSUITE"),
                            ("business_central", "BUSINESS_CENTRAL"),
                        ]
                    ),
                ),
                ("credential_id", models.<PERSON>r<PERSON><PERSON>(max_length=256)),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="integrated_app_credentials",
                        to="users.team",
                    ),
                ),
            ],
            options={
                "unique_together": {("team", "app_name")},
            },
        ),
    ]
