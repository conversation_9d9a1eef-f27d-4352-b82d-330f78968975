from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.integrations.models import (
    TeamIntegratedAppCredential,
    TeamIntegrationProvider,
)
from didero.integrations.schemas import IntegrationProvider
from didero.integrations.utils.alloy import (
    get_alloy_app_credential_id,
    get_alloy_connections,
    get_alloy_user_token,
)
from didero.views import APIView, TeamOwnerFilteredModelView


def _get_alloy_user_id(team_id):
    return TeamIntegrationProvider.objects.get(
        team=team_id, provider=IntegrationProvider.ALLOY
    ).provider_user_id


class AlloyTokenView(APIView, TeamOwnerFilteredModelView):
    permission_classes = [IsAuthenticated]
    model = TeamIntegrationProvider

    def get(self, request):
        try:
            alloy_provider = (
                super().get_queryset().get(provider=IntegrationProvider.ALLOY)
            )
            return Response(get_alloy_user_token(alloy_provider.provider_user_id))
        except TeamIntegrationProvider.DoesNotExist:
            return Response({"error": "Alloy provider not found"}, status=404)


class AlloyCredentialView(APIView, TeamOwnerFilteredModelView):
    permission_classes = [IsAuthenticated]
    model = TeamIntegratedAppCredential

    def get(self, request): ...

    def post(self, request):
        """
        Hacky roundabout way of adding an alloy credentialId
        the FE modal only returns a connectionId so we need to map:
        connectionId -> app_name -> credentialId
        TODO: There might be a better way to do this with the embedded iPaaS
        """
        user_id = _get_alloy_user_id(request.team)

        connection_id = request.data["connection_id"]

        all_connections = get_alloy_connections(user_id)
        filtered_connections = [
            c for c in all_connections if c["connectionId"] == connection_id
        ]

        if len(filtered_connections) == 0:
            return Response({"error": "Connection not found"}, status=404)

        app_name = filtered_connections[0]["app"]

        credential_id = get_alloy_app_credential_id(user_id, app_name)

        TeamIntegratedAppCredential.objects.create(
            team=request.team, app_name=app_name, credential_id=credential_id
        )

        return Response({"credentialId": credential_id})
