from auditlog.registry import auditlog
from django.db import models

from didero.integrations.erp.schemas import ERPType
from didero.integrations.schemas import IntegratedApp, IntegrationProvider
from didero.models import BaseModel


class TeamIntegrationProvider(BaseModel):
    team = models.ForeignKey(
        "users.Team", on_delete=models.CASCADE, related_name="integration_providers"
    )
    provider = models.CharField(
        choices=[(provider.value, provider.name) for provider in IntegrationProvider],
    )
    provider_user_id = models.CharField(max_length=256)


auditlog.register(TeamIntegrationProvider)


class TeamIntegratedAppCredential(BaseModel):
    team = models.ForeignKey(
        "users.Team",
        on_delete=models.CASCADE,
        related_name="integrated_app_credentials",
    )
    app_name = models.CharField(
        choices=[(app.value, app.name) for app in IntegratedApp]
    )
    credential_id = models.Char<PERSON>ield(max_length=256)

    class Meta:
        unique_together = ("team", "app_name")


auditlog.register(TeamIntegratedAppCredential)


class ERPIntegrationConfig(models.Model):
    """
    Stores ERP integration configuration per team.
    Each team can have only one ERP integration configured.
    """

    team = models.OneToOneField(
        "users.Team",
        on_delete=models.CASCADE,
        related_name="erp_config",
        help_text="Team this ERP configuration belongs to",
    )

    erp_type = models.CharField(
        max_length=50, help_text="Type of ERP system (e.g., netsuite, sap, oracle)"
    )

    enabled = models.BooleanField(
        default=False, help_text="Whether ERP sync is enabled for this team"
    )

    # Field mappings: Didero field name -> ERP field name
    field_mappings = models.JSONField(
        default=dict, help_text="Mapping of Didero fields to ERP fields"
    )

    # Additional configuration (endpoints, API versions, etc.)
    config = models.JSONField(
        default=dict, help_text="ERP-specific configuration (endpoints, versions, etc.)"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "erp_integration_config"
        verbose_name = "ERP Integration Config"
        verbose_name_plural = "ERP Integration Configs"

    def __str__(self):
        return f"{self.team.name} - {self.erp_type} ({'Enabled' if self.enabled else 'Disabled'})"


auditlog.register(ERPIntegrationConfig)


class ShipmentErpSync(models.Model):
    """
    Tracks ERP synchronization status for shipments.
    Separated from Shipment model to keep ERP concerns isolated in integrations app.
    """

    shipment = models.OneToOneField(
        "orders.Shipment",
        on_delete=models.CASCADE,
        related_name="erp_sync",
        help_text="Shipment this sync record tracks",
    )

    sync_status = models.CharField(
        max_length=20,
        choices=[
            ("pending", "Pending"),
            ("in_progress", "In Progress"),
            ("success", "Success"),
            ("failed", "Failed"),
            ("not_configured", "Not Configured"),
        ],
        default="not_configured",
        help_text="Status of ERP synchronization",
    )

    attempted_at = models.DateTimeField(
        null=True, blank=True, help_text="Last ERP sync attempt timestamp"
    )

    completed_at = models.DateTimeField(
        null=True, blank=True, help_text="Successful ERP sync timestamp"
    )

    error = models.TextField(blank=True, help_text="Last ERP sync error message")

    # Store which ERP system was used for this sync
    erp_type = models.CharField(
        max_length=20,
        choices=[(erp.value, erp.value.upper()) for erp in ERPType],
        null=True,
        blank=True,
        help_text="ERP system used for this sync (e.g., netsuite, sap)",
    )

    # Track which fields were synced for audit purposes
    synced_fields = models.JSONField(
        default=list, help_text="List of fields that were successfully synced"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "shipment_erp_sync"
        verbose_name = "Shipment ERP Sync"
        verbose_name_plural = "Shipment ERP Syncs"
        indexes = [
            models.Index(fields=["sync_status"]),
            models.Index(fields=["attempted_at"]),
            models.Index(fields=["completed_at"]),
            models.Index(fields=["erp_type"]),
        ]

    def __str__(self):
        return f"ERP Sync for Shipment {self.shipment.id} - {self.sync_status}"


auditlog.register(ShipmentErpSync)
