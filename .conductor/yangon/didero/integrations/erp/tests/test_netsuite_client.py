"""Tests for NetSuite client implementation."""

import datetime
from unittest.mock import <PERSON><PERSON><PERSON>, patch

from django.test import TestCase

from didero.integrations.erp import ERPType, NetSuiteClient


class NetSuiteClientTests(TestCase):
    """Test cases for NetSuite client."""

    def setUp(self):
        """Set up test data."""
        self.credentials = {
            "account_id": "TEST_ACCOUNT",
            "consumer_key": "test_consumer_key",
            "consumer_secret": "test_consumer_secret",
            "token_id": "test_token_id",
            "token_secret": "test_token_secret",
        }

        # Import field mappings from constants
        from didero.integrations.erp.customers.ionq.constants import FIELD_MAP

        self.field_mappings = FIELD_MAP

    def test_client_initialization(self):
        """Test client can be initialized with valid credentials."""
        client = NetSuiteClient(
            credentials=self.credentials,
            field_mappings=self.field_mappings,
        )

        self.assertEqual(client.credentials, self.credentials)
        self.assertEqual(client.field_mappings, self.field_mappings)
        self.assertEqual(client.api_version, "2023_2")

    def test_missing_credentials_raises_error(self):
        """Test initialization fails with missing credentials."""
        incomplete_creds = self.credentials.copy()
        del incomplete_creds["account_id"]

        with self.assertRaises(ValueError) as context:
            NetSuiteClient(credentials=incomplete_creds)

        self.assertIn("Missing required credentials", str(context.exception))

    def test_endpoint_generation(self):
        """Test endpoint URL is generated correctly."""
        client = NetSuiteClient(credentials=self.credentials)

        expected_endpoint = (
            "https://test-account.suitetalk.api.netsuite.com"
            "/services/NetSuitePort_2023_2"
        )
        self.assertEqual(client.endpoint, expected_endpoint)

    def test_custom_endpoint(self):
        """Test custom endpoint can be provided."""
        custom_endpoint = "https://custom.netsuite.com/soap"
        client = NetSuiteClient(
            credentials=self.credentials,
            config={"endpoint": custom_endpoint},
        )

        self.assertEqual(client.endpoint, custom_endpoint)

    def test_field_mapping(self):
        """Test field mapping functionality."""
        client = NetSuiteClient(
            credentials=self.credentials,
            field_mappings=self.field_mappings,
        )

        didero_data = {
            "tracking_number": "ABC123",
            "estimated_delivery_date": "2024-01-15",
            "unmapped_field": "value",
        }

        mapped_data = client.map_fields(didero_data)

        self.assertEqual(mapped_data["custbody_ionq_tracking_number"], "ABC123")
        self.assertEqual(mapped_data["expectedreceiptdate"], "2024-01-15")
        self.assertEqual(mapped_data["unmapped_field"], "value")

    @patch("requests.Session.post")
    def test_test_connection_success(self, mock_post):
        """Test successful connection test."""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '<platformCore:status isSuccess="true"/>'
        mock_post.return_value = mock_response

        client = NetSuiteClient(credentials=self.credentials)
        result = client.test_connection()

        self.assertTrue(result)
        mock_post.assert_called_once()

    @patch("requests.Session.post")
    def test_test_connection_failure(self, mock_post):
        """Test failed connection test."""
        # Mock error response
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.raise_for_status.side_effect = Exception("Unauthorized")
        mock_post.return_value = mock_response

        client = NetSuiteClient(credentials=self.credentials)
        result = client.test_connection()

        self.assertFalse(result)

    @patch("requests.Session.post")
    def test_update_shipment_fields(self, mock_post):
        """Test updating shipment fields."""
        client = NetSuiteClient(
            credentials=self.credentials,
            field_mappings=self.field_mappings,
        )

        # Create response objects for the sequence of calls
        # First call: search for PO
        search_response = MagicMock()
        search_response.status_code = 200
        search_response.text = """
            <searchResult>
                <platformCore:record internalId="123" type="purchaseOrder">
                    <tranPurch:tranId>PO001</tranPurch:tranId>
                </platformCore:record>
            </searchResult>
        """

        # Second call: get PO details
        get_response = MagicMock()
        get_response.status_code = 200
        get_response.text = """<platformCore:status isSuccess="true"/>
            <tranPurch:itemList>
                <tranPurch:item>
                    <tranPurch:line>1</tranPurch:line>
                </tranPurch:item>
            </tranPurch:itemList>"""

        # Third call: update PO
        update_response = MagicMock()
        update_response.status_code = 200
        update_response.text = '<platformCore:status isSuccess="true"/>'

        # Set up the mock to return different responses for each call
        mock_post.side_effect = [search_response, get_response, update_response]

        result = client.update_shipment_fields(
            po_number="PO001",
            tracking_number="TRACK123",
            estimated_delivery_date=datetime.datetime(2024, 1, 15),
        )

        self.assertTrue(result.success)
        self.assertEqual(result.internal_id, "123")
        self.assertEqual(mock_post.call_count, 3)

    def test_oauth_signature_generation(self):
        """Test OAuth signature is generated."""
        client = NetSuiteClient(credentials=self.credentials)

        timestamp, nonce, signature = client._generate_oauth_signature()

        # Verify format
        self.assertTrue(timestamp.isdigit())
        self.assertEqual(len(nonce), 20)
        self.assertTrue(len(signature) > 0)

    def test_soap_envelope_structure(self):
        """Test SOAP envelope is structured correctly."""
        client = NetSuiteClient(credentials=self.credentials)

        body_content = "<test>content</test>"
        envelope = client._create_soap_envelope(body_content)

        # Check structure
        self.assertIn('<?xml version="1.0" encoding="utf-8"?>', envelope)
        self.assertIn("<soap:Envelope", envelope)
        self.assertIn("<soap:Header>", envelope)
        self.assertIn("<platformMsgs:tokenPassport>", envelope)
        self.assertIn(
            f"<platformCore:account>{self.credentials['account_id']}</platformCore:account>",
            envelope,
        )
        self.assertIn("<soap:Body>", envelope)
        self.assertIn(body_content, envelope)
