"""Tests for NetSuite field mapper, focusing on IonQ date handling logic."""

from datetime import date, datetime
from decimal import Decimal
from unittest.mock import Mock, patch

from django.test import TestCase

from didero.integrations.erp.field_mapper import NetSuiteFieldMapper
from didero.integrations.erp.schemas import ERPUpdateRequest, LineItemUpdate
from didero.orders.models import (
    Item,
    OrderAcknowledgement,
    OrderAcknowledgementItem,
    OrderItem,
    PurchaseOrder,
    Shipment,
)
from didero.suppliers.models import Supplier
from didero.users.models import Team


class TestNetSuiteFieldMapper(TestCase):
    """Test cases for NetSuite field mapper."""

    def setUp(self):
        """Set up test data."""
        # Create team and supplier
        self.team = Team.objects.create(name="IonQ Test")
        self.supplier = Supplier.objects.create(name="Test Supplier", team=self.team)

        # Create items
        self.item1 = Item.objects.create(
            item_number="ITEM-001", description="Test Item 1", team=self.team
        )
        self.item2 = Item.objects.create(
            item_number="ITEM-002", description="Test Item 2", team=self.team
        )

        # Create purchase order with requested date
        self.po = PurchaseOrder.objects.create(
            po_number="PO-12345",
            supplier=self.supplier,
            team=self.team,
            requested_date=date(2024, 1, 15),
            order_status="issued",
        )

        # Create PO items
        self.po_item1 = OrderItem.objects.create(
            purchase_order=self.po,
            item=self.item1,
            quantity=10,
            price=Decimal("100.00"),
        )
        self.po_item2 = OrderItem.objects.create(
            purchase_order=self.po, item=self.item2, quantity=5, price=Decimal("200.00")
        )

        # Field mappings
        self.field_mappings = {
            "tracking_number": "custbody_ionq_tracking_number",
            "estimated_delivery_date": "expectedreceiptdate",
            "promised_ship_date": "custcol_ionq_supplierpromisedatefield",
        }

    def test_prepare_oa_data_no_date_changes(self):
        """Test prepare_oa_data when OA dates match PO requested date."""
        # Create OA with same dates as PO
        oa = OrderAcknowledgement.objects.create(
            purchase_order=self.po, order_number="OA-001"
        )

        # Create OA items with same date as PO
        OrderAcknowledgementItem.objects.create(
            order_acknowledgement=oa,
            item=self.item1,
            order_item=self.po_item1,
            quantity=10,
            promised_delivery_date=self.po.requested_date,  # Same as PO
        )

        # Prepare OA data
        result = NetSuiteFieldMapper.prepare_oa_data(oa, self.po, self.field_mappings)

        # Should have no line items since dates match
        self.assertIsNone(result.line_items)

    def test_prepare_oa_data_with_date_changes(self):
        """Test prepare_oa_data when OA dates differ from PO requested date."""
        # Create OA
        oa = OrderAcknowledgement.objects.create(
            purchase_order=self.po, order_number="OA-001"
        )

        # Create OA items with different dates
        oa_item1 = OrderAcknowledgementItem.objects.create(
            order_acknowledgement=oa,
            item=self.item1,
            order_item=self.po_item1,
            quantity=10,
            promised_delivery_date=date(2024, 1, 20),  # Different from PO
        )
        oa_item2 = OrderAcknowledgementItem.objects.create(
            order_acknowledgement=oa,
            item=self.item2,
            order_item=self.po_item2,
            quantity=5,
            promised_delivery_date=date(2024, 1, 22),  # Different from PO
        )

        # Prepare OA data
        result = NetSuiteFieldMapper.prepare_oa_data(oa, self.po, self.field_mappings)

        # Should have line items with date changes
        self.assertIsNotNone(result.line_items)
        self.assertEqual(len(result.line_items), 2)

        # Verify line 1
        line1 = result.line_items[0]
        self.assertEqual(line1.line, 1)
        self.assertEqual(line1.item_number, "ITEM-001")
        self.assertEqual(line1.estimated_delivery_date, date(2024, 1, 20))
        self.assertIsNone(line1.promised_ship_date)  # Never set for OA

        # Verify line 2
        line2 = result.line_items[1]
        self.assertEqual(line2.line, 2)
        self.assertEqual(line2.item_number, "ITEM-002")
        self.assertEqual(line2.estimated_delivery_date, date(2024, 1, 22))
        self.assertIsNone(line2.promised_ship_date)  # Never set for OA

    def test_prepare_oa_data_partial_date_changes(self):
        """Test prepare_oa_data when only some OA items have date changes."""
        # Create OA
        oa = OrderAcknowledgement.objects.create(
            purchase_order=self.po, order_number="OA-001"
        )

        # Create OA items - one with same date, one with different
        OrderAcknowledgementItem.objects.create(
            order_acknowledgement=oa,
            item=self.item1,
            order_item=self.po_item1,
            quantity=10,
            promised_delivery_date=self.po.requested_date,  # Same as PO
        )
        OrderAcknowledgementItem.objects.create(
            order_acknowledgement=oa,
            item=self.item2,
            order_item=self.po_item2,
            quantity=5,
            promised_delivery_date=date(2024, 1, 25),  # Different from PO
        )

        # Prepare OA data
        result = NetSuiteFieldMapper.prepare_oa_data(oa, self.po, self.field_mappings)

        # Should only have one line item (the one with date change)
        self.assertIsNotNone(result.line_items)
        self.assertEqual(len(result.line_items), 1)

        line = result.line_items[0]
        self.assertEqual(line.line, 2)  # Second PO item
        self.assertEqual(line.item_number, "ITEM-002")
        self.assertEqual(line.estimated_delivery_date, date(2024, 1, 25))

    def test_prepare_oa_data_without_order_item_fk(self):
        """Test prepare_oa_data fallback when order_item FK is not set."""
        # Create OA
        oa = OrderAcknowledgement.objects.create(
            purchase_order=self.po, order_number="OA-001"
        )

        # Create OA item without order_item FK (uses item matching)
        OrderAcknowledgementItem.objects.create(
            order_acknowledgement=oa,
            item=self.item1,
            order_item=None,  # No FK relationship
            quantity=10,
            promised_delivery_date=date(2024, 1, 20),
        )

        # Prepare OA data
        result = NetSuiteFieldMapper.prepare_oa_data(oa, self.po, self.field_mappings)

        # Should still map correctly using item matching
        self.assertIsNotNone(result.line_items)
        self.assertEqual(len(result.line_items), 1)

        line = result.line_items[0]
        self.assertEqual(line.line, 1)
        self.assertEqual(line.item_number, "ITEM-001")

    @patch("didero.integrations.erp.field_mapper._get_line_items_with_promised_dates")
    def test_prepare_shipment_data_no_date_changes(self, mock_get_line_items):
        """Test prepare_shipment_data when shipment dates match OA dates."""
        # Create shipment
        shipment = Mock(spec=Shipment)
        shipment.id = 1
        shipment.estimated_delivery_date = date(2024, 1, 20)
        shipment.tracking_number = "TRACK-123"

        # Mock line items data - dates match shipment
        mock_get_line_items.return_value = [
            {
                "line": 1,
                "item_number": "ITEM-001",
                "item_id": 1,
                "quantity": 10,
                "promised_ship_date": date(2024, 1, 20),  # Same as shipment
            }
        ]

        # Prepare shipment data
        result = NetSuiteFieldMapper.prepare_shipment_data(
            shipment, self.field_mappings
        )

        # Should return empty request since dates match
        self.assertIsNotNone(result)
        self.assertIsNone(result.line_items)
        self.assertIsNone(result.tracking_number)

    @patch("didero.integrations.erp.field_mapper._get_line_items_with_promised_dates")
    def test_prepare_shipment_data_with_date_changes(self, mock_get_line_items):
        """Test prepare_shipment_data when shipment dates differ from OA dates."""
        # Create shipment
        shipment = Mock(spec=Shipment)
        shipment.id = 1
        shipment.estimated_delivery_date = date(2024, 1, 25)
        shipment.tracking_number = "TRACK-123"

        # Mock line items data - dates differ from shipment
        mock_get_line_items.return_value = [
            {
                "line": 1,
                "item_number": "ITEM-001",
                "item_id": 1,
                "quantity": 10,
                "promised_ship_date": date(2024, 1, 20),  # Different from shipment
            },
            {
                "line": 2,
                "item_number": "ITEM-002",
                "item_id": 2,
                "quantity": 5,
                "promised_ship_date": date(2024, 1, 22),  # Different from shipment
            },
        ]

        # Prepare shipment data
        result = NetSuiteFieldMapper.prepare_shipment_data(
            shipment, self.field_mappings
        )

        # Should have line items and tracking number
        self.assertIsNotNone(result.line_items)
        self.assertEqual(len(result.line_items), 2)
        self.assertEqual(result.tracking_number, "TRACK-123")

        # Verify all lines have updated delivery date but no promised ship date
        for line in result.line_items:
            self.assertEqual(line.estimated_delivery_date, date(2024, 1, 25))
            self.assertIsNone(line.promised_ship_date)  # Never set for shipments

    @patch("didero.integrations.erp.field_mapper._get_line_items_with_promised_dates")
    def test_prepare_shipment_data_no_delivery_date(self, mock_get_line_items):
        """Test prepare_shipment_data when shipment has no estimated delivery date."""
        # Create shipment without delivery date
        shipment = Mock(spec=Shipment)
        shipment.id = 1
        shipment.estimated_delivery_date = None
        shipment.tracking_number = "TRACK-123"

        # Prepare shipment data
        result = NetSuiteFieldMapper.prepare_shipment_data(
            shipment, self.field_mappings
        )

        # Should return empty request
        self.assertIsNotNone(result)
        self.assertIsNone(result.line_items)
        self.assertIsNone(result.tracking_number)

        # Should not call line items function
        mock_get_line_items.assert_not_called()

    @patch("didero.integrations.erp.field_mapper._get_line_items_with_promised_dates")
    def test_prepare_shipment_data_mixed_date_matches(self, mock_get_line_items):
        """Test prepare_shipment_data when some lines match and others don't."""
        # Create shipment
        shipment = Mock(spec=Shipment)
        shipment.id = 1
        shipment.estimated_delivery_date = date(2024, 1, 25)
        shipment.tracking_number = "TRACK-123"

        # Mock line items - one matches, one doesn't
        mock_get_line_items.return_value = [
            {
                "line": 1,
                "item_number": "ITEM-001",
                "item_id": 1,
                "quantity": 10,
                "promised_ship_date": date(2024, 1, 25),  # Matches shipment
            },
            {
                "line": 2,
                "item_number": "ITEM-002",
                "item_id": 2,
                "quantity": 5,
                "promised_ship_date": date(2024, 1, 20),  # Different from shipment
            },
        ]

        # Prepare shipment data
        result = NetSuiteFieldMapper.prepare_shipment_data(
            shipment, self.field_mappings
        )

        # Should only have one line item (the one that differs)
        self.assertIsNotNone(result.line_items)
        self.assertEqual(len(result.line_items), 1)

        line = result.line_items[0]
        self.assertEqual(line.line, 2)
        self.assertEqual(line.item_number, "ITEM-002")
        self.assertEqual(line.estimated_delivery_date, date(2024, 1, 25))
        self.assertIsNone(line.promised_ship_date)
