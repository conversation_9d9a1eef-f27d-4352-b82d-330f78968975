"""Tests to verify IonQ shipment sync never updates promised ship date field."""

from datetime import date
from decimal import Decimal
from unittest.mock import Mock, patch

from django.test import TestCase

from didero.integrations.erp.clients.netsuite import NetSuiteClient
from didero.integrations.erp.customers.ionq.constants import (
    FIELD_MAP,
    IONQ_LINE_LEVEL_MAPPINGS,
    PROMISED_SHIP_DATE,
)
from didero.integrations.erp.field_mapper import NetSuiteFieldMapper
from didero.integrations.erp.schemas import ERPUpdateRequest, LineItemUpdate


class TestIonQShipmentFieldProtection(TestCase):
    """Test cases to ensure shipment sync protects promised ship date field."""

    def test_ionq_constants_exclude_promised_ship_date(self):
        """Verify IONQ_LINE_LEVEL_MAPPINGS doesn't include PROMISED_SHIP_DATE."""
        # This is the critical configuration that prevents shipment updates
        self.assertNotIn(PROMISED_SHIP_DATE, IONQ_LINE_LEVEL_MAPPINGS)

        # Verify it only includes estimated_delivery_date
        self.assertEqual(len(IONQ_LINE_LEVEL_MAPPINGS), 1)
        self.assertIn("estimated_delivery_date", IONQ_LINE_LEVEL_MAPPINGS)

    def test_field_map_still_contains_promised_ship_date(self):
        """Verify FIELD_MAP still has promised_ship_date for backward compatibility."""
        # The mapping should still exist for other uses (like PO creation)
        self.assertIn(PROMISED_SHIP_DATE, FIELD_MAP)
        self.assertEqual(
            FIELD_MAP[PROMISED_SHIP_DATE], "custcol_ionq_supplierpromisedatefield"
        )

    @patch("didero.integrations.erp.field_mapper._get_line_items_with_promised_dates")
    def test_prepare_shipment_data_never_sets_promised_ship_date(
        self, mock_get_line_items
    ):
        """Test that prepare_shipment_data never sets promised_ship_date field."""
        # Create mock shipment
        shipment = Mock()
        shipment.id = 1
        shipment.estimated_delivery_date = date(2024, 1, 25)
        shipment.tracking_number = "TRACK-123"

        # Mock line items with OA promised dates
        mock_get_line_items.return_value = [
            {
                "line": 1,
                "item_number": "ITEM-001",
                "item_id": 1,
                "quantity": 10,
                "promised_ship_date": date(2024, 1, 20),  # Different from shipment
            },
            {
                "line": 2,
                "item_number": "ITEM-002",
                "item_id": 2,
                "quantity": 5,
                "promised_ship_date": date(2024, 1, 22),  # Different from shipment
            },
        ]

        # Prepare shipment data
        field_mappings = {
            "tracking_number": "custbody_ionq_tracking_number",
            "estimated_delivery_date": "expectedreceiptdate",
            "promised_ship_date": "custcol_ionq_supplierpromisedatefield",
        }

        result = NetSuiteFieldMapper.prepare_shipment_data(shipment, field_mappings)

        # Verify line items exist (dates differ)
        self.assertIsNotNone(result.line_items)
        self.assertEqual(len(result.line_items), 2)

        # Verify NONE of the line items have promised_ship_date set
        for line in result.line_items:
            self.assertIsNone(line.promised_ship_date)
            # But they should have estimated_delivery_date
            self.assertEqual(line.estimated_delivery_date, date(2024, 1, 25))

    def test_netsuite_client_shipment_update_excludes_promised_date(self):
        """Test that NetSuite client doesn't send promised ship date in shipment updates."""
        # Create mock client
        client = NetSuiteClient(
            credentials={
                "account_id": "test",
                "consumer_key": "test",
                "consumer_secret": "test",
                "token_id": "test",
                "token_secret": "test",
            },
            config={"api_version": "2023_2"},
            field_mappings={
                "tracking_number": "custbody_ionq_tracking_number",
                "estimated_delivery_date": "expectedreceiptdate",
                "promised_ship_date": "custcol_ionq_supplierpromisedatefield",
            },
        )

        # Create shipment data with line items
        shipment_data = ERPUpdateRequest(
            tracking_number="TRACK-123",
            line_items=[
                LineItemUpdate(
                    line=1,
                    item_number="ITEM-001",
                    quantity=10,
                    estimated_delivery_date=date(2024, 1, 25),
                    promised_ship_date=None,  # Explicitly None
                ),
                LineItemUpdate(
                    line=2,
                    item_number="ITEM-002",
                    quantity=5,
                    estimated_delivery_date=date(2024, 1, 25),
                    promised_ship_date=None,  # Explicitly None
                ),
            ],
        )

        # Mock the get_purchase_order to avoid PO lookup
        with patch.object(client, "get_purchase_order") as mock_get_po:
            mock_get_po.return_value = {"internal_id": "12345", "po_number": "PO-12345"}

            # Mock the _make_soap_request to avoid actual API calls
            with patch.object(client, "_make_soap_request") as mock_request:
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.text = """<?xml version="1.0" encoding="UTF-8"?>
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                    <soapenv:Body>
                        <updateResponse>
                            <platformCore:status isSuccess="true"/>
                            <baseRef internalId="12345" type="purchaseOrder"/>
                        </updateResponse>
                    </soapenv:Body>
                </soapenv:Envelope>"""
                mock_request.return_value = mock_response

                # Update purchase order with shipment data
                result = client.update_purchase_order("PO-12345", shipment_data)

        # Verify the result
        self.assertTrue(result.success)

        # Verify what was sent - the key is that promised_ship_date should be None
        # in all line items, ensuring it's not updated
        for line in shipment_data.line_items:
            self.assertIsNone(line.promised_ship_date)

        # Verify that _make_soap_request was called
        mock_request.assert_called_once()

    def test_erp_update_request_schema_allows_none_promised_date(self):
        """Test that ERPUpdateRequest schema allows None for promised_ship_date."""
        # Create request with explicit None values
        request = ERPUpdateRequest(
            tracking_number="TRACK-123",
            estimated_delivery_date=None,
            promised_ship_date=None,  # Should be allowed
            line_items=[
                LineItemUpdate(
                    line=1,
                    item_number="ITEM-001",
                    quantity=10,
                    estimated_delivery_date=date(2024, 1, 25),
                    promised_ship_date=None,  # Should be allowed
                )
            ],
        )

        # Verify the request was created successfully
        self.assertIsNone(request.promised_ship_date)
        self.assertIsNone(request.line_items[0].promised_ship_date)

    @patch("didero.integrations.erp.field_mapper._get_line_items_with_promised_dates")
    def test_shipment_sync_with_no_date_changes_sends_nothing(
        self, mock_get_line_items
    ):
        """Test that shipment sync sends no update when dates match OA."""
        # Create mock shipment
        shipment = Mock()
        shipment.id = 1
        shipment.estimated_delivery_date = date(2024, 1, 20)
        shipment.tracking_number = "TRACK-123"

        # Mock line items - dates match shipment (no change needed)
        mock_get_line_items.return_value = [
            {
                "line": 1,
                "item_number": "ITEM-001",
                "item_id": 1,
                "quantity": 10,
                "promised_ship_date": date(2024, 1, 20),  # Same as shipment
            }
        ]

        # Prepare shipment data
        field_mappings = {
            "tracking_number": "custbody_ionq_tracking_number",
            "estimated_delivery_date": "expectedreceiptdate",
            "promised_ship_date": "custcol_ionq_supplierpromisedatefield",
        }

        result = NetSuiteFieldMapper.prepare_shipment_data(shipment, field_mappings)

        # Should have no line items (dates match, no update needed)
        self.assertIsNone(result.line_items)
        self.assertIsNone(result.tracking_number)

        # Verify the result would not trigger any NetSuite update
        self.assertIsNone(result.estimated_delivery_date)
        self.assertIsNone(result.promised_ship_date)
