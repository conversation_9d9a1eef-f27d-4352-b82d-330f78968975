"""Tests for ERP client registry."""

from django.test import TestCase

from didero.integrations.erp import (
    ERPClientRegistry,
    ERPType,
    NetSuiteClient,
)


class ERPClientRegistryTests(TestCase):
    """Test cases for ERP client registry."""

    def test_get_client_class_netsuite(self):
        """Test getting NetSuite client class."""
        client_class = ERPClientRegistry.get_client_class(ERPType.NETSUITE)
        self.assertEqual(client_class, NetSuiteClient)

    def test_get_client_class_unsupported(self):
        """Test error for unsupported ERP type."""
        with self.assertRaises(ValueError) as context:
            ERPClientRegistry.get_client_class(ERPType.SAP)

        self.assertIn("Unsupported ERP type", str(context.exception))

    def test_create_client(self):
        """Test creating a client instance."""
        credentials = {
            "account_id": "TEST",
            "consumer_key": "key",
            "consumer_secret": "secret",
            "token_id": "token",
            "token_secret": "token_secret",
        }

        client = ERPClientRegistry.create_client(
            erp_type=ERPType.NETSUITE,
            credentials=credentials,
        )

        self.assertIsInstance(client, NetSuiteClient)
        self.assertEqual(client.credentials, credentials)

    def test_list_supported_erps(self):
        """Test listing supported ERP types."""
        supported = ERPClientRegistry.list_supported_erps()

        self.assertIn(ERPType.NETSUITE, supported)
        self.assertEqual(len(supported), 1)  # Currently only NetSuite
