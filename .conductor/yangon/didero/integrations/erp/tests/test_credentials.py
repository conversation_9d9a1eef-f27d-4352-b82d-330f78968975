"""Tests for credential provider."""

import os
from pathlib import Path
from unittest.mock import patch

from django.test import TestCase

from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
from didero.integrations.erp.schemas import ERPType


class SimpleEnvCredentialProviderTests(TestCase):
    """Test cases for credential provider."""

    def test_load_env_file(self):
        """Test provider initialization - env vars should already be loaded by Django."""
        provider = SimpleEnvCredentialProvider()
        # Should not raise error, just uses environment variables
        self.assertIsNotNone(provider)

    def test_missing_env_file(self):
        """Test provider works without .env file - Django handles loading."""
        provider = SimpleEnvCredentialProvider()
        # Should not raise error
        self.assertIsNotNone(provider)

    @patch.dict(
        os.environ,
        {
            "IONQ_NETSUITE_ACCOUNT_ID": "test_account",
            "IONQ_NETSUITE_CONSUMER_KEY": "test_key",
            "IONQ_NETSUITE_CONSUMER_SECRET": "test_secret",
            "IONQ_NETSUITE_TOKEN_ID": "test_token",
            "IONQ_NETSUITE_TOKEN_SECRET": "test_token_secret",
        },
    )
    def test_get_credentials_netsuite(self):
        """Test getting NetSuite credentials for IonQ teams."""
        provider = SimpleEnvCredentialProvider()

        # Test with team ID 4 (IonQ test)
        credentials = provider.get_credentials("4", ERPType.NETSUITE)
        self.assertEqual(credentials["account_id"], "test_account")
        self.assertEqual(credentials["consumer_key"], "test_key")
        self.assertEqual(credentials["consumer_secret"], "test_secret")
        self.assertEqual(credentials["token_id"], "test_token")
        self.assertEqual(credentials["token_secret"], "test_token_secret")

        # Test with team ID 173 (IonQ production)
        credentials = provider.get_credentials("173", ERPType.NETSUITE)
        self.assertEqual(credentials["account_id"], "test_account")
        self.assertEqual(credentials["consumer_key"], "test_key")
        self.assertEqual(credentials["consumer_secret"], "test_secret")
        self.assertEqual(credentials["token_id"], "test_token")
        self.assertEqual(credentials["token_secret"], "test_token_secret")

    def test_get_credentials_missing_fields(self):
        """Test missing credential fields are handled."""
        provider = SimpleEnvCredentialProvider()

        # No env vars set, should return empty dict or partial
        credentials = provider.get_credentials("test_team", ERPType.NETSUITE)

        # Should not raise error, just return what's available
        self.assertIsInstance(credentials, dict)

    def test_validate_credentials_valid(self):
        """Test validating complete credentials."""
        provider = SimpleEnvCredentialProvider()

        valid_creds = {
            "account_id": "test",
            "consumer_key": "key",
            "consumer_secret": "secret",
            "token_id": "token",
            "token_secret": "token_secret",
        }

        result = provider.validate_credentials(valid_creds, ERPType.NETSUITE)
        self.assertTrue(result)

    def test_validate_credentials_missing(self):
        """Test validating incomplete credentials."""
        provider = SimpleEnvCredentialProvider()

        invalid_creds = {
            "account_id": "test",
            "consumer_key": "key",
            # Missing other required fields
        }

        result = provider.validate_credentials(invalid_creds, ERPType.NETSUITE)
        self.assertFalse(result)

    def test_team_id_uppercased(self):
        """Test team ID is uppercased in env var names."""
        provider = SimpleEnvCredentialProvider()

        with patch.dict(os.environ, {"TESTTEAM_NETSUITE_ACCOUNT_ID": "account123"}):
            credentials = provider.get_credentials("testteam", ERPType.NETSUITE)
            self.assertEqual(credentials.get("account_id"), "account123")

    def test_different_erp_types(self):
        """Test credential fields for different ERP types."""
        provider = SimpleEnvCredentialProvider()

        # Test SAP fields
        with patch.dict(
            os.environ,
            {
                "TEAM_SAP_CLIENT_ID": "sap_client",
                "TEAM_SAP_CLIENT_SECRET": "sap_secret",
                "TEAM_SAP_TENANT_ID": "sap_tenant",
            },
        ):
            sap_creds = provider.get_credentials("team", ERPType.SAP)
            self.assertEqual(sap_creds["client_id"], "sap_client")
            self.assertEqual(sap_creds["client_secret"], "sap_secret")
            self.assertEqual(sap_creds["tenant_id"], "sap_tenant")

        # Test Oracle fields
        with patch.dict(
            os.environ,
            {
                "TEAM_ORACLE_USERNAME": "oracle_user",
                "TEAM_ORACLE_PASSWORD": "oracle_pass",
                "TEAM_ORACLE_INSTANCE_URL": "oracle_url",
            },
        ):
            oracle_creds = provider.get_credentials("team", ERPType.ORACLE)
            self.assertEqual(oracle_creds["username"], "oracle_user")
            self.assertEqual(oracle_creds["password"], "oracle_pass")
            self.assertEqual(oracle_creds["instance_url"], "oracle_url")
