"""SOAP XML builder utilities for NetSuite integration."""

from contextlib import contextmanager
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from .customers.ionq.constants import (
    ESTIMATED_DELIVERY_DATE,
    NS_LINE_EXPECTED_RECEIPT_DATE,
    NS_LINE_PROMISED_SHIP_DATE,
    PROMISED_SHIP_DATE,
)
from .types import LineItemData
from .utils import SOAPNamespaces, format_netsuite_date


@dataclass
class XMLElement:
    """Represents an XML element with attributes and content."""

    tag: str
    content: Optional[str] = None
    attributes: Optional[Dict[str, str]] = None
    namespace: Optional[str] = None

    def to_xml(self) -> str:
        """Convert to XML string."""
        # Build tag with namespace
        full_tag = f"{self.namespace}:{self.tag}" if self.namespace else self.tag

        # Build attributes
        attr_str = ""
        if self.attributes:
            attrs = [f'{k}="{v}"' for k, v in self.attributes.items()]
            attr_str = " " + " ".join(attrs)

        if self.content is None:
            return f"<{full_tag}{attr_str}/>"
        else:
            return f"<{full_tag}{attr_str}>{self.content}</{full_tag}>"


class SOAPBuilder:
    """Builder for NetSuite SOAP XML requests."""

    def __init__(self, api_version: str):
        self.namespaces = SOAPNamespaces(api_version)
        self.elements: List[str] = []
        self.indent_level = 0

    def _indent(self) -> str:
        """Get current indentation."""
        return "    " * self.indent_level

    def add_element(self, element: XMLElement) -> "SOAPBuilder":
        """Add an XML element."""
        self.elements.append(self._indent() + element.to_xml())
        return self

    def add_raw(self, xml: str) -> "SOAPBuilder":
        """Add raw XML with current indentation."""
        self.elements.append(self._indent() + xml)
        return self

    @contextmanager
    def element_block(
        self,
        tag: str,
        namespace: Optional[str] = None,
        attributes: Optional[Dict[str, str]] = None,
    ):
        """Context manager for nested XML elements."""
        full_tag = f"{namespace}:{tag}" if namespace else tag
        attr_str = ""
        if attributes:
            attrs = [f'{k}="{v}"' for k, v in attributes.items()]
            attr_str = " " + " ".join(attrs)

        self.elements.append(f"{self._indent()}<{full_tag}{attr_str}>")
        self.indent_level += 1
        try:
            yield
        finally:
            self.indent_level -= 1
            self.elements.append(f"{self._indent()}</{full_tag}>")

    def build_custom_field(
        self, field_id: str, value: str, field_type: str = "StringCustomFieldRef"
    ) -> "SOAPBuilder":
        """Add a NetSuite custom field element."""
        with self.element_block(
            "customField",
            "platformCore",
            {"xsi:type": f"platformCore:{field_type}", "scriptId": field_id},
        ):
            self.add_element(XMLElement("value", value, namespace="platformCore"))
        return self

    def build_line_item(self, line_data: LineItemData) -> "SOAPBuilder":
        """Build a line item element."""
        with self.element_block("item", "tranPurch"):
            self.add_element(
                XMLElement("line", str(line_data["line"]), namespace="tranPurch")
            )

            # Expected receipt date
            if line_data.get("estimated_delivery_date"):
                date_value = format_netsuite_date(line_data["estimated_delivery_date"])
                if date_value:
                    self.add_element(
                        XMLElement(
                            "expectedReceiptDate", date_value, namespace="tranPurch"
                        )
                    )

            # Promised ship date (custom field)
            if line_data.get(PROMISED_SHIP_DATE):
                date_value = format_netsuite_date(line_data[PROMISED_SHIP_DATE])
                if date_value:
                    with self.element_block("customFieldList", "tranPurch"):
                        self.build_custom_field(
                            NS_LINE_PROMISED_SHIP_DATE,
                            date_value,
                            "DateCustomFieldRef",
                        )

        return self

    def build_header_custom_fields(
        self, header_fields: Dict[str, Any]
    ) -> "SOAPBuilder":
        """Build header-level custom fields."""
        if not header_fields:
            return self

        with self.element_block("customFieldList", "tranPurch"):
            for field, value in header_fields.items():
                self.build_custom_field(field, str(value))

        return self

    def build_line_items_list(self, line_items: List[LineItemData]) -> "SOAPBuilder":
        """Build itemList with line items."""
        with self.element_block("itemList", "tranPurch", {"replaceAll": "false"}):
            for line_item in line_items:
                self.build_line_item(line_item)
        return self

    def build_flat_line_updates(
        self, line_numbers: List[str], line_fields: Dict[str, Any]
    ) -> "SOAPBuilder":
        """Build line updates using flat field structure."""
        with self.element_block("itemList", "tranPurch", {"replaceAll": "false"}):
            for line_num in line_numbers:
                with self.element_block("item", "tranPurch"):
                    self.add_element(
                        XMLElement("line", line_num, namespace="tranPurch")
                    )

                    # Standard line fields
                    if NS_LINE_EXPECTED_RECEIPT_DATE in line_fields:
                        date_value = format_netsuite_date(
                            line_fields[NS_LINE_EXPECTED_RECEIPT_DATE]
                        )
                        if date_value:
                            self.add_element(
                                XMLElement(
                                    "expectedReceiptDate",
                                    date_value,
                                    namespace="tranPurch",
                                )
                            )

                    # Custom line fields
                    if NS_LINE_PROMISED_SHIP_DATE in line_fields:
                        date_value = format_netsuite_date(
                            line_fields[NS_LINE_PROMISED_SHIP_DATE]
                        )
                        if date_value:
                            with self.element_block("customFieldList", "tranPurch"):
                                self.build_custom_field(
                                    NS_LINE_PROMISED_SHIP_DATE,
                                    date_value,
                                    "DateCustomFieldRef",
                                )

        return self

    def to_string(self) -> str:
        """Convert to XML string."""
        return "\n".join(self.elements)
