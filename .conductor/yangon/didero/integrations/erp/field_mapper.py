"""Field mapping utilities for ERP integrations."""

from datetime import date
from typing import Any, Dict, List, Optional

import structlog

from didero.integrations.erp.customers.ionq.constants import IONQ_LINE_LEVEL_MAPPINGS
from didero.integrations.erp.schemas import ERPUpdateRequest, LineItemUpdate
from didero.orders.models import (
    OrderAcknowledgement,
    OrderAcknowledgementItem,
    PurchaseOrder,
    Shipment,
)

logger = structlog.get_logger(__name__)


class NetSuiteFieldMapper:
    """Field mapper for NetSuite integration."""

    @staticmethod
    def prepare_oa_data(
        order_acknowledgement: OrderAcknowledgement,
        purchase_order: PurchaseOrder,
        field_mappings: Dict[str, str],
    ) -> ERPUpdateRequest:
        """
        Prepare OA data for ERP sync when promised dates differ from PO requested date.

        Only updates expectedreceiptdate, never custcol_ionq_supplierpromisedatefield.
        Uses order_item FK for direct matching, falls back to item matching.
        """
        line_items = []
        po_items = list(purchase_order.items.order_by("id"))
        # Create line number mapping
        item_to_line = {item.id: idx + 1 for idx, item in enumerate(po_items)}

        for oa_item in order_acknowledgement.items.select_related("item", "order_item"):
            # Skip if no promised delivery date
            if not oa_item.promised_delivery_date:
                continue

            # Compare to PO requested date
            if oa_item.promised_delivery_date != purchase_order.requested_date:
                # Use direct order_item relationship if available
                if oa_item.order_item_id:
                    line_number = item_to_line.get(oa_item.order_item_id)
                else:
                    # Fallback: Find by item match
                    line_number = next(
                        (
                            idx + 1
                            for idx, po_item in enumerate(po_items)
                            if po_item.item_id == oa_item.item_id
                        ),
                        None,
                    )

                if line_number:
                    line_items.append(
                        LineItemUpdate(
                            line=line_number,
                            item_number=oa_item.item.item_number,
                            item_id=str(oa_item.item_id),
                            quantity=oa_item.quantity,
                            estimated_delivery_date=oa_item.promised_delivery_date,
                            # promised_ship_date explicitly None - never update
                            promised_ship_date=None,
                        )
                    )

                    logger.info(
                        "OA date change detected",
                        item_number=oa_item.item.item_number,
                        line=line_number,
                        old_date=purchase_order.requested_date,
                        new_date=oa_item.promised_delivery_date,
                        has_order_item=bool(oa_item.order_item_id),
                    )

        return ERPUpdateRequest(line_items=line_items if line_items else None)

    @staticmethod
    def prepare_shipment_data(
        shipment: Shipment, field_mappings: Dict[str, str]
    ) -> ERPUpdateRequest:
        """
        Modified to compare shipment dates with OA promised dates for IonQ.
        Only syncs if dates differ, preventing unnecessary ERP updates.
        """
        # Check if we have a delivery date to sync
        if not shipment.estimated_delivery_date:
            logger.info(
                "Shipment has no estimated_delivery_date, nothing to sync",
                shipment_id=shipment.id,
            )
            return ERPUpdateRequest()

        # Get line items with OA promised dates for comparison
        line_items_data = _get_line_items_with_promised_dates(shipment)

        # Filter to only items where dates differ
        changed_items = []
        for item_data in line_items_data:
            # Compare shipment date to OA promised delivery date
            # Note: _get_line_items_with_promised_dates returns promised_ship_date field
            oa_promised_date = item_data.get("promised_ship_date")

            # Skip if dates are the same (no update needed)
            if (
                oa_promised_date
                and oa_promised_date == shipment.estimated_delivery_date
            ):
                logger.info(
                    "Skipping line - dates match",
                    line=item_data["line"],
                    item_number=item_data["item_number"],
                    date=shipment.estimated_delivery_date,
                )
                continue

            # Include this line for update
            changed_items.append(
                LineItemUpdate(
                    line=item_data["line"],
                    item_number=item_data["item_number"],
                    item_id=str(item_data["item_id"])
                    if item_data.get("item_id")
                    else None,
                    quantity=item_data["quantity"],
                    estimated_delivery_date=shipment.estimated_delivery_date,
                    # Never update promised_ship_date
                    promised_ship_date=None,
                )
            )

            logger.info(
                "Shipment date differs from OA",
                line=item_data["line"],
                item_number=item_data["item_number"],
                oa_date=oa_promised_date,
                shipment_date=shipment.estimated_delivery_date,
            )

        # Only return update request if there are changes
        if changed_items:
            return ERPUpdateRequest(
                tracking_number=shipment.tracking_number
                if field_mappings.get("tracking_number")
                else None,
                line_items=changed_items,
            )
        else:
            logger.info("No date changes to sync for shipment", shipment_id=shipment.id)
            return ERPUpdateRequest()


def _get_line_items_with_promised_dates(shipment: Shipment) -> List[Dict[str, Any]]:
    """
    Get line-item-specific data including promised dates.

    This function:
    1. Iterates through ShipmentLineItems
    2. Matches each to its OrderItem
    3. Finds the corresponding OA item with promised date
    4. Returns structured data preserving line-item relationships

    Args:
        shipment: Shipment with line items

    Returns:
        List of dicts with line-specific data
    """
    line_items_data = []

    try:
        # Check if purchase order exists
        if not shipment.purchase_order:
            logger.warning(
                "Shipment has no purchase order - cannot map line items for ERP",
                shipment_id=shipment.id,
            )
            return []

        # Get PO items to determine line numbers
        po_items = list(shipment.purchase_order.items.all().order_by("id"))

        if not po_items:
            logger.warning(
                "No PO items found for line mapping",
                shipment_id=shipment.id,
                po_id=shipment.purchase_order.id,
            )
            return []

        # Create a mapping of OrderItem to line number
        item_to_line = {item.id: idx + 1 for idx, item in enumerate(po_items)}

        logger.info(
            "Created line mapping for ERP sync",
            shipment_id=shipment.id,
            total_po_items=len(po_items),
            line_mapping=item_to_line,
        )

        # Process each shipment line item
        shipment_lines = list(shipment.line_items.select_related("order_item__item"))

        if not shipment_lines:
            logger.warning("No shipment line items found", shipment_id=shipment.id)
            return []

        logger.info(
            "Processing shipment lines for ERP mapping",
            shipment_id=shipment.id,
            total_shipment_lines=len(shipment_lines),
        )

        for shipment_line in shipment_lines:
            order_item = shipment_line.order_item

            # Get line number from position with validation
            line_number = item_to_line.get(order_item.id)

            if line_number is None:
                logger.error(
                    "OrderItem not found in PO line mapping",
                    shipment_id=shipment.id,
                    order_item_id=order_item.id,
                    item_number=order_item.item.item_number,
                    available_po_items=[item.id for item in po_items],
                )
                # Skip this line item as it can't be mapped
                continue

            # Find promised date for this specific item
            promised_date = None

            # NEW: Direct lookup by order_item
            oa_item = (
                OrderAcknowledgementItem.objects.filter(
                    order_acknowledgement__purchase_order=shipment.purchase_order,
                    order_item=order_item,
                )
                .order_by("-order_acknowledgement__created_at")
                .first()
            )

            if oa_item:
                promised_date = oa_item.promised_ship_date
            else:
                # FALLBACK: Look for OA items matching this order item's Item
                oa_items = OrderAcknowledgementItem.objects.filter(
                    order_acknowledgement__purchase_order=shipment.purchase_order,
                    item=order_item.item,
                ).order_by("-order_acknowledgement__created_at")

                if oa_items.exists():
                    oa_item = oa_items.first()
                    promised_date = oa_item.promised_ship_date

            line_data = {
                "line": line_number,
                "item_number": order_item.item.item_number,
                "item_id": order_item.item.id,  # Add item_id for better tracking
                "quantity": shipment_line.shipped_quantity,
                "promised_ship_date": promised_date,
                "estimated_delivery_date": shipment.estimated_delivery_date,
            }

            line_items_data.append(line_data)

            logger.info(
                "Mapped line item data",
                line=line_number,
                item_id=order_item.item.id,
                item_number=order_item.item.item_number,
                promised_date=promised_date,
                estimated_delivery_date=shipment.estimated_delivery_date,
            )

    except Exception as e:
        logger.warning(
            "Error building line items data, falling back to flat structure",
            shipment_id=shipment.id,
            error=str(e),
        )
        return []

    # Final validation and summary
    if line_items_data:
        logger.info(
            "Successfully mapped line items for ERP sync",
            shipment_id=shipment.id,
            total_mapped_lines=len(line_items_data),
            mapped_line_numbers=[item["line"] for item in line_items_data],
            mapped_items=[item["item_number"] for item in line_items_data],
        )
    else:
        logger.warning(
            "No line items mapped for ERP sync",
            shipment_id=shipment.id,
            total_po_items=len(po_items) if po_items else 0,
            total_shipment_lines=len(shipment_lines) if shipment_lines else 0,
        )

    return line_items_data


def _get_promised_ship_date_from_oa(purchase_order: PurchaseOrder) -> Optional[date]:
    """
    Extract promised ship date from order acknowledgment.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        Promised ship date if found, None otherwise
    """
    try:
        # Get the latest OA
        oa = purchase_order.order_acknowledgements.order_by("-created_at").first()
        if not oa:
            return None

        # Get the first item with a promised ship date
        oa_item = oa.items.exclude(promised_ship_date__isnull=True).first()
        if oa_item:
            return oa_item.promised_ship_date

    except Exception as e:
        logger.warning(
            "Error extracting promised ship date from OA",
            po_number=purchase_order.po_number,
            error=str(e),
        )

    return None
