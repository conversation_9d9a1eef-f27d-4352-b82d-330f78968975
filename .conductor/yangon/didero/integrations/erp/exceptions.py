class ERPIntegrationError(Exception):
    """Base exception for ERP integration errors"""

    pass


class ERPConfigurationError(ERPIntegrationError):
    """Raised when there's an issue with ERP configuration"""

    pass


class ERPAuthenticationError(ERPIntegrationError):
    """Raised when ERP authentication fails"""

    pass


class ERPSyncError(ERPIntegrationError):
    """Raised when ERP sync operation fails"""

    pass


class ERPFieldMappingError(ERPIntegrationError):
    """Raised when field mapping fails"""

    pass
