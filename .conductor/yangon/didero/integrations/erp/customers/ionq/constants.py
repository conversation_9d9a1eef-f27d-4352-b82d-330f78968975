"""
Constants for ION-Q specific ERP integration, focusing on NetSuite fields.

CRITICAL DATE FIELD LOGIC:
=========================
NetSuite maintains two date fields on PO line items with distinct purposes:

1. expectedreceiptdate (NS_LINE_EXPECTED_RECEIPT_DATE)
   - Purpose: Dynamic field showing current best estimate of receipt date
   - Updates:
     * Order Acknowledgment: When OA promised_delivery_date ≠ PO requested_date
     * Shipment: When shipment estimated_delivery_date ≠ OA promised_delivery_date
   - Represents: What we currently expect

2. custcol_ionq_supplierpromisedatefield (NS_LINE_PROMISED_SHIP_DATE)
   - Purpose: Static field preserving original PO requested date
   - Updates: NEVER (after initial PO creation in NetSuite)
   - Represents: Historical record of what was originally requested

This logic ensures NetSuite users can see both:
- What was originally requested (static field)
- What is currently expected (dynamic field)
"""

# Internal field names (used within the Didero application)
TRACKING_NUMBER = "tracking_number"
ESTIMATED_DELIVERY_DATE = "estimated_delivery_date"
PROMISED_SHIP_DATE = "promised_ship_date"
LINE_ITEMS = "line_items"


# NetSuite Field Script IDs
# -- Header Fields --
NS_HEADER_TRACKING_NUMBER = "custbody_ionq_tracking_number"

# -- Line Item (Column) Fields --
NS_LINE_EXPECTED_RECEIPT_DATE = (
    "expectedreceiptdate"  # Dynamic - updates with OA and Shipment
)
NS_LINE_PROMISED_SHIP_DATE = (
    "custcol_ionq_supplierpromisedatefield"  # Static - original PO date
)


# Field Groupings
# These sets provide a single source of truth for categorizing fields.
# Using sets allows for efficient 'in' checks .

IONQ_HEADER_FIELDS = {
    NS_HEADER_TRACKING_NUMBER,
}

IONQ_LINE_FIELDS = {
    NS_LINE_EXPECTED_RECEIPT_DATE,
    NS_LINE_PROMISED_SHIP_DATE,
}

# Mapping from internal names to NetSuite script IDs
# IMPORTANT: promised_ship_date should NOT be used in shipment updates
FIELD_MAP = {
    TRACKING_NUMBER: NS_HEADER_TRACKING_NUMBER,
    ESTIMATED_DELIVERY_DATE: NS_LINE_EXPECTED_RECEIPT_DATE,
    PROMISED_SHIP_DATE: NS_LINE_PROMISED_SHIP_DATE,
}

# Context-specific mappings - Controls what fields are synced
# CRITICAL: Remove PROMISED_SHIP_DATE from IONQ_LINE_LEVEL_MAPPINGS
IONQ_LINE_LEVEL_MAPPINGS = [
    ESTIMATED_DELIVERY_DATE,  # Maps to expectedreceiptdate only
]

# IonQ team IDs
IONQ_TEAM_IDS = [
    4,  # IonQ test team
    173,  # IonQ production team
]
