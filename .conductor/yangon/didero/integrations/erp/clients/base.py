"""Base class for ERP client implementations."""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

import structlog

from didero.integrations.erp.schemas import (
    ERPSyncResult,
    ERPUpdateRequest,
    ERPUpdateResponse,
)

logger = structlog.get_logger(__name__)


class ERPClientBase(ABC):
    """Abstract base class for ERP client implementations."""

    def __init__(
        self,
        credentials: Dict[str, str],
        config: Optional[Dict[str, Any]] = None,
        field_mappings: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize the ERP client.

        Args:
            credentials: ERP-specific credentials
            config: Additional configuration (endpoints, versions, etc.)
            field_mappings: Mapping of Didero fields to ERP fields
        """
        self.credentials = credentials
        self.config = config or {}
        self.field_mappings = field_mappings or {}

    @abstractmethod
    def test_connection(self) -> bool:
        """
        Test the connection to the ERP system.

        Returns:
            True if connection is successful, False otherwise
        """
        pass

    @abstractmethod
    def update_purchase_order(
        self,
        po_identifier: str,
        updates: ERPUpdateRequest,
    ) -> ERPUpdateResponse:
        """
        Update fields on a purchase order.

        Args:
            po_identifier: PO number or internal ID
            updates: ERPUpdateRequest of fields to update

        Returns:
            ERPUpdateResponse with status and response data
        """
        pass

    def map_fields(self, didero_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map Didero field names to ERP field names.

        Args:
            didero_data: Data with Didero field names

        Returns:
            Data with ERP field names
        """
        erp_data = {}
        for didero_field, value in didero_data.items():
            erp_field = self.field_mappings.get(didero_field, didero_field)
            erp_data[erp_field] = value

        logger.info(
            "Mapped fields",
            input_fields=list(didero_data.keys()),
            output_fields=list(erp_data.keys()),
        )

        return erp_data

    def update_shipment(
        self, po_number: str, shipment_data: ERPUpdateRequest
    ) -> ERPSyncResult:
        """
        Update shipment-related fields on a purchase order.

        This is a convenience method that delegates to update_purchase_order
        and wraps the result in an ERPSyncResult object for consistency.

        Args:
            po_number: Purchase order number or internal ID
            shipment_data: ERPUpdateRequest of shipment data to update

        Returns:
            ERPSyncResult object with success status and optional error message
        """
        try:
            # Delegate to the abstract update_purchase_order method
            result = self.update_purchase_order(po_number, shipment_data)

            # Convert ERPUpdateResponse to ERPSyncResult
            return ERPSyncResult(
                success=result.success,
                error_message=result.error_message,
            )
        except Exception as e:
            # Convert any exception to ERPSyncResult with error
            logger.error("ERP update failed", po_number=po_number, error=str(e))
            return ERPSyncResult(success=False, error_message=str(e))
