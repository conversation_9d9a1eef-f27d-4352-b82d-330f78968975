from datetime import date, datetime
from enum import StrEnum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class ERPType(StrEnum):
    """Supported ERP systems"""

    NETSUITE = "netsuite"
    SAP = "sap"
    ORACLE = "oracle"
    DYNAMICS = "dynamics"


class FieldMappingSchema(BaseModel):
    """Schema for validating field mappings"""

    # ERP field name -> Didero field name
    mappings: Dict[str, str] = Field(
        ..., description="Mapping of ERP field names to Didero field names"
    )

    @validator("mappings")
    def validate_mappings(cls, v: Dict[str, str]) -> Dict[str, str]:
        if not v:
            raise ValueError("Field mappings cannot be empty")
        return v


class ERPConfigSchema(BaseModel):
    """Schema for validating ERP-specific configuration"""

    endpoint: Optional[str] = Field(None, description="API endpoint URL")
    api_version: Optional[str] = Field(None, description="API version to use")

    # Allow additional fields for ERP-specific config
    class Config:
        extra = "allow"


class ERPIntegrationConfigSchema(BaseModel):
    """Schema for the complete ERP integration configuration"""

    team_id: int
    erp_type: ERPType
    enabled: bool = False
    field_mappings: Dict[str, str]
    config: ERPConfigSchema


class LineItemUpdate(BaseModel):
    """Schema for line item updates in ERP systems."""

    line: int = Field(..., description="Line number in the purchase order")
    item_number: str = Field(..., description="Item number/SKU")
    item_id: Optional[str] = Field(None, description="Internal item ID for tracking")
    quantity: float = Field(..., description="Shipped quantity")
    promised_ship_date: Optional[date] = Field(
        None, description="Supplier promised ship date"
    )
    estimated_delivery_date: Optional[date] = Field(
        None, description="Estimated delivery date"
    )

    @validator("line")
    def validate_line_number(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Line number must be positive")
        return v

    @validator("quantity")
    def validate_quantity(cls, v: float) -> float:
        if v < 0:
            raise ValueError("Quantity cannot be negative")
        return v


class ERPUpdateRequest(BaseModel):
    """Schema for ERP update requests."""

    tracking_number: Optional[str] = Field(None, description="Shipment tracking number")
    estimated_delivery_date: Optional[date] = Field(
        None, description="Estimated delivery date"
    )
    promised_ship_date: Optional[date] = Field(None, description="Promised ship date")
    line_items: Optional[List[LineItemUpdate]] = Field(
        None, description="Line-specific updates"
    )

    # Allow extra fields for backward compatibility during transition
    model_config = {"extra": "allow"}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ERPUpdateRequest":
        """Create ERPUpdateRequest from dictionary for backward compatibility."""
        if isinstance(data, dict):
            return cls.model_validate(data)
        return data

    @validator("line_items")
    def validate_line_items(
        cls, v: Optional[List[LineItemUpdate]]
    ) -> Optional[List[LineItemUpdate]]:
        if v is not None:
            # Validate no duplicate line numbers
            line_numbers = [item.line for item in v]
            if len(line_numbers) != len(set(line_numbers)):
                raise ValueError("Duplicate line numbers found in line_items")
        return v

    def has_line_level_updates(self) -> bool:
        """Check if this request contains line-level updates."""
        return self.line_items is not None and len(self.line_items) > 0

    def total_lines(self) -> int:
        """Get total number of line items in this request."""
        return len(self.line_items) if self.line_items else 0


class ERPUpdateResponse(BaseModel):
    """Schema for ERP update responses."""

    success: bool
    internal_id: str
    error_message: Optional[str] = None


class ERPSyncResult(BaseModel):
    """Result object for ERP synchronization operations"""

    success: bool = Field(..., description="Whether the sync operation succeeded")
    error_message: Optional[str] = Field(
        None, description="Error message if sync failed"
    )
