from django.urls import path

from didero.integrations.views import AlloyCredentialView, AlloyTokenView

urlpatterns = [
    path(
        "api/integrations/alloy/token",
        AlloyTokenView.as_view({"get": "get"}),
        name="alloy-token",
    ),
    path(
        "api/integrations/alloy/credentials",
        AlloyCredentialView.as_view({"get": "get", "post": "post"}),
        name="alloy-credentials",
    ),
]
