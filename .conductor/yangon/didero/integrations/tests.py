from unittest.mock import patch

from django.urls import reverse
from rest_framework import status

from didero.integrations.models import (
    TeamIntegratedAppCredential,
    TeamIntegrationProvider,
)
from didero.integrations.schemas import IntegrationProvider
from didero.testing.cases import TestCase


class TestAlloyIntegrationViews(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user()
        self.auth_as_user(self.user)
        self.team = self.user.teams.first()

        # Create a TeamIntegrationProvider for Alloy
        self.team_integration_provider = TeamIntegrationProvider.objects.create(
            team=self.team,
            provider=IntegrationProvider.ALLOY,
            provider_user_id="alloy_user_id",
        )

    @patch("didero.integrations.views.get_alloy_user_token")
    def test_get_alloy_token_success(self, mock_get_alloy_user_token):
        mock_get_alloy_user_token.return_value = {"token": "mocked_alloy_token"}

        url = reverse("alloy-token")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_get_alloy_user_token.assert_called_once_with("alloy_user_id")
        self.assertEqual(response.json(), {"token": "mocked_alloy_token"})

    def test_get_alloy_token_provider_not_found(self):
        # Delete the integration provider to simulate absence
        self.team_integration_provider.delete()

        url = reverse("alloy-token")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.json(), {"error": "Alloy provider not found"})

    @patch("didero.integrations.views.get_alloy_connections")
    @patch("didero.integrations.views.get_alloy_app_credential_id")
    def test_post_alloy_credential_success(
        self, mock_get_credential_id, mock_get_connections
    ):
        mock_get_connections.return_value = [
            {"connectionId": "conn_1", "app": "app_1"},
            {"connectionId": "conn_2", "app": "app_2"},
        ]
        mock_get_credential_id.return_value = "credential_id_123"

        url = reverse("alloy-credentials")
        response = self.client.post(
            url, data={"connection_id": "conn_1"}, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_get_connections.assert_called_once_with("alloy_user_id")
        mock_get_credential_id.assert_called_once_with("alloy_user_id", "app_1")

        # Verify that the credential was saved
        self.assertTrue(
            TeamIntegratedAppCredential.objects.filter(
                team=self.team,
                app_name="app_1",
                credential_id="credential_id_123",
            ).exists()
        )

        self.assertEqual(response.json(), {"credentialId": "credential_id_123"})

    @patch("didero.integrations.views.get_alloy_connections")
    def test_post_alloy_credential_connection_not_found(self, mock_get_connections):
        mock_get_connections.return_value = [
            {"connectionId": "conn_1", "app": "app_1"},
        ]

        url = reverse("alloy-credentials")
        response = self.client.post(
            url, data={"connection_id": "unknown_conn"}, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.json(), {"error": "Connection not found"})

    def test_access_with_different_team(self):
        # Create a new user belonging to a different team
        user2 = self.create_user(email="<EMAIL>")
        self.auth_as_user(user2)

        url = reverse("alloy-token")
        response = self.client.get(url)

        # The new user should not have access to the original team's data
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.json(), {"error": "Alloy provider not found"})
