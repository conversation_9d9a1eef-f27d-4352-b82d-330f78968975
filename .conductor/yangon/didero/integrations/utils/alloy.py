import os

import requests

ALLOY_API_KEY = os.environ.get("ALLOY_API_KEY", "")
ALLOY_URL = "https://embedded.runalloy.com/2024-03"


def create_alloy_user(username: str):
    endpoint = ALLOY_URL + "/users"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    response = requests.post(endpoint, headers=headers, json={"username": username})
    return response.json().get("userId")


def get_alloy_user_token(user_id: str):
    endpoint = ALLOY_URL + f"/users/{user_id}/token"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    response = requests.get(endpoint, headers=headers)
    return response.json()


def get_alloy_connections(user_id: str):
    endpoint = ALLOY_URL + f"/one/connections?userId={user_id}"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    response = requests.get(endpoint, headers=headers)
    return response.json()["connections"]


def get_alloy_credential_ids(user_id: str):
    endpoint = ALLOY_URL + f"/users/{user_id}/credentials"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    response = requests.get(endpoint, headers=headers)
    return response.json()


def get_alloy_app_credential_id(user_id: str, app: str):
    resp = get_alloy_credential_ids(user_id)
    # Get the most recently created credential for the app
    # Assumes that user only has one credential at a time
    return [cred["credentialId"] for cred in resp["data"] if cred["type"] == app][-1]


def get_alloy_vendors(
    credential_id: str,
    page_number: int = 1,
    page_size: int = 25,
    vendor_name_contains: str = "",
):
    endpoint = ALLOY_URL + "/one/accounting/vendors"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    query_params = {
        "credentialId": credential_id,
        "pageNumber": page_number,
        "pageSize": page_size,
    }

    if vendor_name_contains:
        query_params["vendorNameContains"] = vendor_name_contains

    response = requests.get(endpoint, headers=headers, params=query_params)
    return response.json()["vendors"]


def get_alloy_vendor_count(credential_id: str):
    endpoint = ALLOY_URL + "/one/accounting/vendors/count"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    query_params = {
        "credentialId": credential_id,
    }

    response = requests.get(endpoint, headers=headers, params=query_params)
    return response.json()


def get_alloy_items(credential_id: str, page_number: int = 1, page_size: int = 25):
    endpoint = ALLOY_URL + "/one/accounting/items"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    query_params = {
        "credentialId": credential_id,
        "pageNumber": page_number,
        "pageSize": page_size,
    }

    response = requests.get(endpoint, headers=headers, params=query_params)
    return response.json()["items"]


def get_alloy_item_count(credential_id: str):
    endpoint = ALLOY_URL + "/one/accounting/items/count"
    headers = {
        "Authorization": f"Bearer {ALLOY_API_KEY}",
    }
    query_params = {
        "credentialId": credential_id,
    }

    response = requests.get(endpoint, headers=headers, params=query_params)
    return response.json()
