from enum import StrEnum
from typing import List, Optional

from pydantic import BaseModel


class FedexAddress(BaseModel):
    addressClassification: Optional[str] = None
    residential: Optional[bool] = None
    streetLines: Optional[List[str]] = None
    city: Optional[str] = None
    stateOrProvinceCode: Optional[str] = None
    postalCode: Optional[str] = None
    countryCode: Optional[str] = None
    countryName: Optional[str] = None


class FedexTrackingNumberInfo(BaseModel):
    trackingNumber: Optional[str] = None
    carrierCode: Optional[str] = None
    trackingNumberUniqueId: Optional[str] = None


class FedexPackageIdentifier(BaseModel):
    type: Optional[str] = None
    value: Optional[str] = None
    trackingNumberUniqueId: Optional[str] = None


class FedexAdditionalTrackingInfo(BaseModel):
    hasAssociatedShipments: Optional[bool] = None
    nickname: Optional[str] = None
    packageIdentifiers: Optional[List[FedexPackageIdentifier]] = None
    shipmentNotes: Optional[str] = None


class FedexDistanceToDestination(BaseModel):
    units: Optional[str] = None
    value: Optional[float] = None


class FedexReasonDetail(BaseModel):
    description: Optional[str] = None
    type: Optional[str] = None


class FedexConsolidationDetail(BaseModel):
    timeStamp: Optional[str] = None
    consolidationID: Optional[str] = None
    reasonDetail: Optional[FedexReasonDetail] = None
    packageCount: Optional[int] = None
    eventType: Optional[str] = None


class FedexReturnDetail(BaseModel):
    authorizationName: Optional[str] = None
    reasonDetail: Optional[List[FedexReasonDetail]] = None


class FedexServiceDetail(BaseModel):
    description: Optional[str] = None
    shortDescription: Optional[str] = None
    type: Optional[str] = None


class FedexLocationContactAndAddress(BaseModel):
    address: Optional[FedexAddress] = None


class FedexDestinationLocation(BaseModel):
    locationId: Optional[str] = None
    locationContactAndAddress: Optional[FedexLocationContactAndAddress] = None
    locationType: Optional[str] = None


class FedexAncillaryDetail(BaseModel):
    reason: Optional[str] = None
    reasonDescription: Optional[str] = None
    action: Optional[str] = None
    actionDescription: Optional[str] = None


class FedexDelayDetail(BaseModel):
    type: Optional[str] = None
    subType: Optional[str] = None
    status: Optional[str] = None


class FedexLatestStatusDetail(BaseModel):
    scanLocation: Optional[FedexAddress] = None
    code: Optional[str] = None
    derivedCode: Optional[str] = None
    ancillaryDetails: Optional[List[FedexAncillaryDetail]] = None
    statusByLocale: Optional[str] = None
    description: Optional[str] = None
    delayDetail: Optional[FedexDelayDetail] = None


class FedexServiceCommitMessage(BaseModel):
    message: Optional[str] = None
    type: Optional[str] = None


class FedexInformationNoteDetail(BaseModel):
    code: Optional[str] = None
    description: Optional[str] = None


class FedexErrorParameter(BaseModel):
    value: Optional[str] = None
    key: Optional[str] = None


class FedexError(BaseModel):
    code: Optional[str] = None
    parameterList: Optional[List[FedexErrorParameter]] = None
    message: Optional[str] = None


class FedexSpecialHandling(BaseModel):
    description: Optional[str] = None
    type: Optional[str] = None
    paymentType: Optional[str] = None


class FedexAvailableImage(BaseModel):
    size: Optional[str] = None
    type: Optional[str] = None


class FedexActualDeliveryAddress(BaseModel):
    addressClassification: Optional[str] = None
    residential: Optional[bool] = None
    streetLines: Optional[List[str]] = None
    city: Optional[str] = None
    stateOrProvinceCode: Optional[str] = None
    postalCode: Optional[str] = None
    countryCode: Optional[str] = None
    countryName: Optional[str] = None


class FedexDeliveryOptionEligibilityDetail(BaseModel):
    option: Optional[str] = None
    eligibility: Optional[str] = None


class FedexDeliveryDetails(BaseModel):
    receivedByName: Optional[str] = None
    destinationServiceArea: Optional[str] = None
    destinationServiceAreaDescription: Optional[str] = None
    locationDescription: Optional[str] = None
    actualDeliveryAddress: Optional[FedexActualDeliveryAddress] = None
    deliveryToday: Optional[bool] = None
    locationType: Optional[str] = None
    signedByName: Optional[str] = None
    officeOrderDeliveryMethod: Optional[str] = None
    deliveryAttempts: Optional[str] = None
    deliveryOptionEligibilityDetails: Optional[
        List[FedexDeliveryOptionEligibilityDetail]
    ] = None


class FedexScanLocation(BaseModel):
    addressClassification: Optional[str] = None
    residential: Optional[bool] = None
    streetLines: Optional[List[str]] = None
    city: Optional[str] = None
    stateOrProvinceCode: Optional[str] = None
    postalCode: Optional[str] = None
    countryCode: Optional[str] = None
    countryName: Optional[str] = None


class FedexScanEvent(BaseModel):
    date: Optional[str] = None
    derivedStatus: Optional[str] = None
    scanLocation: Optional[FedexScanLocation] = None
    locationId: Optional[str] = None
    locationType: Optional[str] = None
    exceptionDescription: Optional[str] = None
    eventDescription: Optional[str] = None
    eventType: Optional[str] = None
    derivedStatusCode: Optional[str] = None
    exceptionCode: Optional[str] = None
    delayDetail: Optional[FedexDelayDetail] = None


class FedexDateAndTime(BaseModel):
    dateTime: Optional[str] = None
    type: Optional[str] = None


class FedexPackagingDescription(BaseModel):
    description: Optional[str] = None
    type: Optional[str] = None


class FedexWeight(BaseModel):
    unit: Optional[str] = None
    value: Optional[str] = None


class FedexDimension(BaseModel):
    length: Optional[int] = None
    width: Optional[int] = None
    height: Optional[int] = None
    units: Optional[str] = None


class FedexWeightAndDimensions(BaseModel):
    weight: Optional[List[FedexWeight]] = None
    dimensions: Optional[List[FedexDimension]] = None


class FedexDeclaredValue(BaseModel):
    currency: Optional[str] = None
    value: Optional[float] = None


class FedexPackageDetails(BaseModel):
    physicalPackagingType: Optional[str] = None
    sequenceNumber: Optional[str] = None
    undeliveredCount: Optional[str] = None
    packagingDescription: Optional[FedexPackagingDescription] = None
    count: Optional[str] = None
    weightAndDimensions: Optional[FedexWeightAndDimensions] = None
    packageContent: Optional[List[str]] = None
    contentPieceCount: Optional[str] = None
    declaredValue: Optional[FedexDeclaredValue] = None


class FedexHoldAtLocation(BaseModel):
    locationId: Optional[str] = None
    locationContactAndAddress: Optional[FedexLocationContactAndAddress] = None
    locationType: Optional[str] = None


class FedexWindow(BaseModel):
    begins: Optional[str] = None
    ends: Optional[str] = None


class FedexRequestedAppointmentDetail(BaseModel):
    date: Optional[str] = None
    window: Optional[List[FedexWindow]] = None


class FedexCustomDeliveryOption(BaseModel):
    requestedAppointmentDetail: Optional[FedexRequestedAppointmentDetail] = None
    description: Optional[str] = None
    type: Optional[str] = None
    status: Optional[str] = None


class FedexEstimatedDeliveryTimeWindow(BaseModel):
    description: Optional[str] = None
    window: Optional[FedexWindow] = None
    type: Optional[str] = None


class FedexPieceCount(BaseModel):
    count: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None


class FedexOriginLocation(BaseModel):
    locationId: Optional[str] = None
    locationContactAndAddress: Optional[FedexLocationContactAndAddress] = None


class FedexRecipientInformation(BaseModel):
    address: Optional[FedexAddress] = None


class FedexStandardTransitTimeWindow(BaseModel):
    description: Optional[str] = None
    window: Optional[FedexWindow] = None
    type: Optional[str] = None


class FedexShipmentContent(BaseModel):
    itemNumber: Optional[str] = None
    receivedQuantity: Optional[str] = None
    description: Optional[str] = None
    partNumber: Optional[str] = None


class FedexSplitShipment(BaseModel):
    pieceCount: Optional[str] = None
    statusDescription: Optional[str] = None
    timestamp: Optional[str] = None
    statusCode: Optional[str] = None


class FedexShipmentDetails(BaseModel):
    contents: Optional[List[FedexShipmentContent]] = None
    beforePossessionStatus: Optional[bool] = None
    weight: Optional[List[FedexWeight]] = None
    contentPieceCount: Optional[str] = None
    splitShipments: Optional[List[FedexSplitShipment]] = None


class FedexShipperInformation(BaseModel):
    address: Optional[FedexAddress] = None


class FedexLastUpdatedDestinationAddress(BaseModel):
    addressClassification: Optional[str] = None
    residential: Optional[bool] = None
    streetLines: Optional[List[str]] = None
    city: Optional[str] = None
    stateOrProvinceCode: Optional[str] = None
    postalCode: Optional[str] = None
    countryCode: Optional[str] = None
    countryName: Optional[str] = None


class FedexTrackResult(BaseModel):
    trackingNumberInfo: Optional[FedexTrackingNumberInfo] = None
    additionalTrackingInfo: Optional[FedexAdditionalTrackingInfo] = None
    distanceToDestination: Optional[FedexDistanceToDestination] = None
    consolidationDetail: Optional[List[FedexConsolidationDetail]] = None
    meterNumber: Optional[str] = None
    returnDetail: Optional[FedexReturnDetail] = None
    serviceDetail: Optional[FedexServiceDetail] = None
    destinationLocation: Optional[FedexDestinationLocation] = None
    latestStatusDetail: Optional[FedexLatestStatusDetail] = None
    serviceCommitMessage: Optional[FedexServiceCommitMessage] = None
    informationNotes: Optional[List[FedexInformationNoteDetail]] = None
    error: Optional[FedexError] = None
    specialHandlings: Optional[List[FedexSpecialHandling]] = None
    availableImages: Optional[List[FedexAvailableImage]] = None
    deliveryDetails: Optional[FedexDeliveryDetails] = None
    scanEvents: Optional[List[FedexScanEvent]] = None
    dateAndTimes: Optional[List[FedexDateAndTime]] = None
    packageDetails: Optional[FedexPackageDetails] = None
    goodsClassificationCode: Optional[str] = None
    holdAtLocation: Optional[FedexHoldAtLocation] = None
    customDeliveryOptions: Optional[List[FedexCustomDeliveryOption]] = None
    estimatedDeliveryTimeWindow: Optional[FedexEstimatedDeliveryTimeWindow] = None
    pieceCounts: Optional[List[FedexPieceCount]] = None
    originLocation: Optional[FedexOriginLocation] = None
    recipientInformation: Optional[FedexRecipientInformation] = None
    standardTransitTimeWindow: Optional[FedexStandardTransitTimeWindow] = None
    shipmentDetails: Optional[FedexShipmentDetails] = None
    reasonDetail: Optional[FedexReasonDetail] = None
    availableNotifications: Optional[List[str]] = None
    shipperInformation: Optional[FedexShipperInformation] = None
    lastUpdatedDestinationAddress: Optional[FedexLastUpdatedDestinationAddress] = None


class FedexCompleteTrackResult(BaseModel):
    trackingNumber: Optional[str] = None
    trackResults: Optional[List[FedexTrackResult]] = None


class FedexOutput(BaseModel):
    completeTrackResults: Optional[List[FedexCompleteTrackResult]] = None


class FedexCarrierCode(StrEnum):
    FDXE = "FedEx Express Saver"
    FDX2 = "FedEx 2Day"
    FDX2AM = "FedEx 2Day A.M."
    FDXSO = "FedEx Standard Overnight"
    FDXPO = "FedEx Priority Overnight"
    FDXFO = "FedEx First Overnight"
    FDXG = "FedEx Ground"
    FDXHD = "FedEx Home Delivery"
    FDXFP = "FedEx Freight Priority"
    FDXFE = "FedEx Freight Economy"
    FDXIP = "FedEx International Priority"
    FDXIE = "FedEx International Economy"
    FDXIF = "FedEx International First"
    FDXIPF = "FedEx International Priority Freight"
    FDXIEF = "FedEx International Economy Freight"
    FDXIG = "FedEx International Ground"
    FDXSP = "FedEx SmartPost"
    FDXCC = "FedEx Custom Critical"
    FDXC = "FedEx Cargo"
    FDXSD = "FedEx SameDay"
    FDXSDC = "FedEx SameDay City"


class FedexServiceType(StrEnum):
    # Domestic Services
    GROUND_HOME_DELIVERY = "FedEx Home Delivery"
    SMART_POST = "FedEx Ground Economy"
    SAME_DAY = "FedEx SameDay"
    SAME_DAY_CITY = "FedEx SameDay City"
    FEDEX_GROUND = "FedEx Ground"

    # Overnight Services
    PRIORITY_OVERNIGHT = "FedEx Priority Overnight"
    FIRST_OVERNIGHT = "FedEx First Overnight"
    STANDARD_OVERNIGHT = "FedEx Standard Overnight"

    # Freight Services
    FEDEX_FIRST_FREIGHT = "FedEx First Overnight Freight"
    FEDEX_1_DAY_FREIGHT = "FedEx 1Day Freight"
    FEDEX_2_DAY_FREIGHT = "FedEx 2Day Freight"
    FEDEX_3_DAY_FREIGHT = "FedEx 3Day Freight"
    FEDEX_PRIORITY_FREIGHT = "FedEx Priority Freight"

    # Other Services
    FEDEX_FIRST = "FedEx First"
    FEDEX_PRIORITY = "FedEx Priority"
    TRANSBORDER_DISTRIBUTION = "Transborder distribution"

    # International Priority Services
    FEDEX_INTERNATIONAL_PRIORITY_EXPRESS = "FedEx International Priority Express"
    FEDEX_INTERNATIONAL_PRIORITY = "FedEx International Priority"
    INTERNATIONAL_FIRST = "FedEx International First"
    FEDEX_EXPRESS_SAVER = "FedEx Express Saver"
    EUROPE_FIRST_INTERNATIONAL_PRIORITY = "FedEx Europe First"
    FEDEX_PRIORITY_EXPRESS = "FedEx Priority Express"
    FEDEX_PRIORITY_EXPRESS_FREIGHT = "FedEx Priority Express Freight"

    # International Economy Services
    INTERNATIONAL_ECONOMY = "FedEx International Economy"
    FEDEX_INTERNATIONAL_DEFERRED_FREIGHT = "FedEx International Deferred Freight"
    INTERNATIONAL_ECONOMY_FREIGHT = "FedEx International Economy Freight"
    INTERNATIONAL_ECONOMY_DISTRIBUTION = (
        "FedEx International Economy DirectDistribution"
    )

    # International Ground Services
    FEDEX_INTERNATIONAL_CONNECT_PLUS = "FedEx International Connect Plus"
    INTERNATIONAL_PRIORITY_DISTRIBUTION = (
        "FedEx International Priority DirectDistribution"
    )
    INTERNATIONAL_DISTRIBUTION_FREIGHT = (
        "FedEx International Priority DirectDistribution Freight"
    )
    INTL_GROUND_DISTRIBUTION = "International Ground Distribution"


class FedexAlert(BaseModel):
    code: str
    message: str


class FedexParameter(BaseModel):
    name: str
    value: str


class FedexErrorResponseVO(BaseModel):
    code: str
    message: str
    parameterList: Optional[List[FedexParameter]]


### Didero Defined Fedex Response objects.


class FedexTrackingInformationRequestTrackingInfo(BaseModel):
    shipDateBegin: Optional[str] = None
    shipDateEnd: Optional[str] = None
    trackingNumberInfo: FedexTrackingNumberInfo


class FedexTrackingInformationRequest(BaseModel):
    includeDetailedScans: bool = False
    trackingInfo: list[FedexTrackingInformationRequestTrackingInfo]


class FedexTrackingInformationResponse(BaseModel):
    transactionId: str
    customerTransactionId: Optional[str] = None
    output: FedexOutput
    alerts: Optional[List[FedexAlert]] = None
    errors: Optional[List[FedexErrorResponseVO]] = None
