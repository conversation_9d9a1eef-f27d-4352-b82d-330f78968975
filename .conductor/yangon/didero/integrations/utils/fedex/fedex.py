from datetime import date, datetime
from typing import Optional

import requests
import structlog
from django.conf import settings
from pydantic import ValidationError

from didero.cache_util import get as cache_retrive
from didero.cache_util import save as cache_save
from didero.integrations.utils.fedex.schemas import (
    FedexServiceType,
    FedexTrackingInformationRequest,
    FedexTrackingInformationRequestTrackingInfo,
    FedexTrackingInformationResponse,
    FedexTrackingNumberInfo,
    FedexTrackResult,
)
from didero.orders.schemas import ShipmentStatus
from didero.shipments.schemas import TrackingInfo

FEDEX_AUTHORIZATION_TOKEN_REDIS_KEY = "fedex-auth-token"
BASE_URL = settings.FEDEX_API_BASE_URL
API_KEY = settings.FEDEX_API_KEY
API_SECRET = settings.FEDEX_API_SECRET
OAUTH_URL = "/oauth/token"
TRACKING_API_URL = "/track/v1/trackingnumbers"

logger = structlog.get_logger(__name__)


# Store and Fetch Authorization Token in Redis Cache
# Try to fetch it, if it's expired, refetch and put back in Redis.
def get_fedex_authorization_token() -> str:
    token = cache_retrive(FEDEX_AUTHORIZATION_TOKEN_REDIS_KEY)
    if token:
        return token

    response = requests.post(
        url=f"{BASE_URL}{OAUTH_URL}",
        data={
            "grant_type": "client_credentials",
            "client_id": API_KEY,
            "client_secret": API_SECRET,
        },
    )

    response_json = response.json()
    if (
        response.status_code != 200
        or not response_json
        or not response_json["access_token"]
    ):
        logger.error(
            f"Fedex Authorization Request failed. Got status {response.status_code} and {response.text}"
        )
        raise Exception(
            f"Fedex Authorization Request failed. Got status {response.status_code} and {response.text}. Ensure the API keys are correct."
        )

    cache_save(
        FEDEX_AUTHORIZATION_TOKEN_REDIS_KEY,
        response_json["access_token"],
        cache_duration=int(response_json["expires_in"])
        - 5,  # avoid race condition in case the request happens exactly at expiry
    )

    return response_json["access_token"]


def get_tracking_info_for_tracking_number(
    tracking_number: str,
) -> Optional[TrackingInfo]:
    token = get_fedex_authorization_token()

    request = FedexTrackingInformationRequest(
        trackingInfo=[
            FedexTrackingInformationRequestTrackingInfo(
                trackingNumberInfo=FedexTrackingNumberInfo(
                    trackingNumber=tracking_number
                )
            )
        ]
    )

    try:
        response = requests.post(
            url=f"{BASE_URL}{TRACKING_API_URL}",
            data=request.model_dump_json(),
            headers={
                "Content-Type": "application/json",
                "X-locale": "en_US",
                "Authorization": f"Bearer {token}",
            },
        )
        response.raise_for_status()
    except requests.exceptions.HTTPError as http_err:
        logger.error(
            f"HTTP error occurred when fetching tracking info from Fedex: {http_err}"
        )
        return None
    except Exception as err:
        logger.error(
            f"Other error occurred when fetching tracking info from Fedex: {err}"
        )
        return None

    try:
        response_data = response.json()
        fedex_response = FedexTrackingInformationResponse.model_validate(response_data)
    except ValidationError as err:
        logger.error(f"Invalid response from Fedex: {err}")
        return None

    if fedex_response.errors:
        logger.error(
            "Fedex Response contains errors, returning empty tracking info",
            errors=fedex_response.errors,
        )
        return None

    if (
        not fedex_response.output.completeTrackResults
        or len(fedex_response.output.completeTrackResults) == 0
        or not fedex_response.output.completeTrackResults[0].trackResults
        or len(fedex_response.output.completeTrackResults[0].trackResults) == 0
    ):
        logger.error(
            "Fedex Response contains no complete track results, returning empty tracking info"
        )
        return None

    any_fedex_errors = any(
        track_result.error
        for complete_result in fedex_response.output.completeTrackResults or []
        for track_result in complete_result.trackResults or []
    )

    if any_fedex_errors:
        logger.error(
            "Fedex Response Track Result contains errors, returning empty tracking info",
            errors=fedex_response.output.completeTrackResults[0].trackResults[0].error,
        )
        return None

    track_result = fedex_response.output.completeTrackResults[0].trackResults[0]

    shipment_status = _get_latest_status_from_fedex_track_result(track_result)

    eta = (
        _get_eta_from_fedex_track_result(track_result)
        if shipment_status == ShipmentStatus.SHIPPED
        else None
    )

    carrier = _get_carrier_from_fedex_track_result(track_result)

    shipment_date = _get_shipment_date_from_fedex_track_result(track_result)

    return TrackingInfo(
        tracking_number=tracking_number,
        eta=eta,
        shipment_date=shipment_date,
        actual_arrival_date=None,  # FedEx doesn't provide this in the same way
        shipment_status=shipment_status,
        carrier=carrier,
        # Ocean carrier fields not applicable for FedEx
        port_of_departure=None,
        port_of_arrival=None,
    )


# Fedex returns a lot of status codes, we need to map them to our ShipmentStatus enum
# For now we're only interested in whether or not it's delivered or not.
# If it's not delivered, for simplicity we'll assumed it's in some sort of SHIPPED state, since the tracking number is still valid.
def _get_latest_status_from_fedex_track_result(
    track_result: FedexTrackResult,
) -> ShipmentStatus:
    if not track_result.latestStatusDetail:
        return ShipmentStatus.SHIPPED

    fedex_status_code = track_result.latestStatusDetail.derivedCode
    if fedex_status_code == "DL":
        return ShipmentStatus.RECEIVED

    return ShipmentStatus.SHIPPED


def _get_shipment_date_from_fedex_track_result(
    track_result: FedexTrackResult,
) -> Optional[date]:
    if not track_result.dateAndTimes:
        return None

    # Look through the date and times and find one that has a type of "SHIP"
    date_time = None
    for date_and_time in track_result.dateAndTimes:
        if date_and_time.type == "SHIP":
            date_time = date_and_time.dateTime
            break

    if not date_time:
        return None

    return datetime.fromisoformat(date_time).date()


# Most of the fields in the Fedex API are optional :/
# This provides safe access for these fields with logging in the case they are not populated.
# ETA is a complicated field as where it may be depends on the state of the delivery,
# So we'll try to get it from the estimated delivery time window first, then the date and times object.
def _get_eta_from_fedex_track_result(track_result: FedexTrackResult) -> Optional[date]:
    if (
        not track_result.estimatedDeliveryTimeWindow
        or not track_result.estimatedDeliveryTimeWindow.window
        or not track_result.dateAndTimes
    ):
        logger.error(
            "Fedex Integration: Could not find estimated delivery time window in the tracking result, returning None for ETA"
        )
        return None

    # Check the Estimated Delivery Time Window to determine the ETA
    begin = track_result.estimatedDeliveryTimeWindow.window.begins
    ends = track_result.estimatedDeliveryTimeWindow.window.ends

    eta = None
    if ends:
        eta = ends
    elif begin:
        eta = begin
    elif track_result.dateAndTimes:
        # Look through the date and times object of the track_result. There may be an estimated delivery time in there.
        for date_and_time in track_result.dateAndTimes or []:
            if date_and_time.type == "ESTIMATED_DELIVERY":
                eta = date_and_time.dateTime

    if not eta:
        logger.error(
            "Fedex Integration: Estimated Delivery Time Window has no beginning or end, returning None for ETA"
        )
        return None

    return datetime.fromisoformat(eta).date()


def _get_carrier_from_fedex_track_result(track_result: FedexTrackResult) -> str:
    service_detail = track_result.serviceDetail

    if not service_detail:
        logger.error(
            "Fedex Integration: Could not find service detail in tracking result, returning empty carrier"
        )
        return ""

    service_type = service_detail.type

    if not service_type:
        logger.error(
            "Fedex Integration: Could not find service type in service detail, returning empty carrier"
        )
        return ""

    try:
        carrier = FedexServiceType[service_type].value
    except KeyError:
        logger.error(
            "Fedex Integration: Could not find carrier code for service type, returning empty carrier"
        )
        carrier = ""

    return carrier
