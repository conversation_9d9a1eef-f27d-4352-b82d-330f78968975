from typing import Optional

import structlog
from django.utils import timezone

from didero.integrations.stagehand.stagehand import (
    get_stagehand_job_status_sync,
    submit_stagehand_job_sync,
)
from didero.orders.schemas import CarrierType, ShipmentStatus
from didero.shipments.schemas import TrackingInfo
from didero.utils.date_utils import parse_datestring_to_datetime

logger = structlog.get_logger(__name__)

# Mapping of carrier types to their Stagehand job types
CARRIER_TO_JOB_TYPE = {
    CarrierType.ZIM: "zim.getTrackingInfo",
    CarrierType.ONE: "one.getTrackingInfo",
    CarrierType.MSC: "msc.getTrackingInfo",
    CarrierType.COSCO: "cosco.getTrackingInfo",
    CarrierType.EVERGREEN: "evergreen.getTrackingInfo",
}


def submit_stagehand_tracking_job(
    carrier_type: CarrierType, tracking_number: str
) -> Optional[str]:
    """
    Submit a tracking job to Stagehand and return the job ID.

    Args:
        carrier_type: The carrier type (must be in CARRIER_TO_JOB_TYPE mapping)
        tracking_number: The tracking number (BOL number for ocean carriers)

    Returns:
        Job ID if submission successful, None otherwise
    """
    if carrier_type not in CARRIER_TO_JOB_TYPE:
        logger.error(f"Unsupported carrier type for Stagehand tracking: {carrier_type}")
        return None

    job_type = CARRIER_TO_JOB_TYPE[carrier_type]

    try:
        logger.info(
            f"Submitting {carrier_type} tracking job for {tracking_number}",
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            job_type=job_type,
        )

        # Submit Stagehand job
        job_result = submit_stagehand_job_sync(
            customer="", job=job_type, params={"tracking_number": tracking_number}
        )

        logger.info(
            "Stagehand job submitted successfully",
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            job_id=job_result.job_id,
        )

        return job_result.job_id

    except Exception as e:
        logger.error(
            f"Error submitting {carrier_type} tracking job to Stagehand",
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            error=str(e),
        )
        return None


def check_stagehand_job_result(
    job_id: str, carrier_type: CarrierType, tracking_number: str
) -> Optional[TrackingInfo]:
    """
    Check the status of a Stagehand job and return tracking info if complete.

    Args:
        job_id: The Stagehand job ID to check
        carrier_type: The carrier type (for logging)
        tracking_number: The tracking number (for logging and fallback)

    Returns:
        TrackingInfo if job is complete and successful, None otherwise

    Note:
        Stagehand job statuses:
        - Non-terminal: 'waiting', 'active', 'delayed'
        - Terminal: 'completed', 'failed'
    """
    try:
        # Get job status
        job_result = get_stagehand_job_status_sync(job_id)

        # Check if job is still pending (non-terminal states)
        if job_result.status in ["waiting", "active", "delayed"]:
            return None

        # Check if job failed
        if job_result.status == "failed":
            logger.error(
                "Stagehand job failed",
                job_id=job_id,
                carrier_type=carrier_type,
                tracking_number=tracking_number,
                error=job_result.result.get("error") if job_result.result else None,
            )
            return None

        # Check if job completed successfully
        if job_result.status != "completed":
            logger.error(
                "Stagehand job has unexpected status",
                job_id=job_id,
                carrier_type=carrier_type,
                tracking_number=tracking_number,
                status=job_result.status,
            )
            return None

        # Job succeeded - extract result
        if not job_result.is_success:
            logger.error(
                "Stagehand job returned unsuccessful result",
                job_id=job_id,
                carrier_type=carrier_type,
                tracking_number=tracking_number,
                status=job_result.status,
            )
            return None

        # Extract tracking data
        data = job_result.result.get("data", {})
        if not data:
            logger.error(
                "Stagehand job completed but no data returned",
                job_id=job_id,
                carrier_type=carrier_type,
                tracking_number=tracking_number,
            )
            return None

        # Parse dates
        eta_datetime = parse_datestring_to_datetime(data.get("eta"))
        shipment_datetime = parse_datestring_to_datetime(data.get("shipment_date"))
        arrival_datetime = parse_datestring_to_datetime(data.get("actual_arrival_date"))

        # Determine if delivered
        is_delivered = False
        if arrival_datetime:
            # Check if arrival date is in the past
            now = timezone.now()
            is_delivered = arrival_datetime <= now

        # Determine shipment status
        status = ShipmentStatus.RECEIVED if is_delivered else ShipmentStatus.SHIPPED

        # Create unified TrackingInfo directly
        tracking_info = TrackingInfo(
            tracking_number=data.get("tracking_number", tracking_number),
            eta=eta_datetime.date() if eta_datetime else None,
            shipment_date=shipment_datetime.date() if shipment_datetime else None,
            actual_arrival_date=arrival_datetime.date() if arrival_datetime else None,
            shipment_status=status,
            port_of_departure=data.get("port_of_departure") or None,
            port_of_arrival=data.get("port_of_arrival") or None,
            carrier=carrier_type,
        )

        logger.info(
            "Successfully extracted tracking info from completed job",
            job_id=job_id,
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            is_delivered=is_delivered,
        )

        return tracking_info

    except Exception as e:
        logger.error(
            "Error checking Stagehand job result",
            job_id=job_id,
            carrier_type=carrier_type,
            tracking_number=tracking_number,
            error=str(e),
        )
        return None
