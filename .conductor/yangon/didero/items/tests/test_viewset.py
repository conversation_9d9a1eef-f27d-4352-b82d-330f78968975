import json

from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from didero.common.models import CustomTag
from didero.items.models import Item, ItemFieldsDisplayConfig
from didero.items.serializers import ItemSerializer
from didero.testing.cases import TestCase
from didero.users.models.team_models import Team

ITEM_OPTIONAL_FIELDS = [
    "notes",
    "item_sku",
    "manufacturer_part_number",
    "supplier_part_number",
    "upc",
    "unspsc",
    "hs_code",
    "color",
    "weight_value",
    "weight_unit",
    "dimensions",
    "production_lead_time",
    "transit_lead_time",
    "total_lead_time",
    "thumbnail",
]

RANDOM_UUID = "d32bf6d2-738d-4619-9c4a-e4a3330586b3"


class TestSupplierItemsViewset(TestCase):
    def setUp(self):
        # create an item and its dependencies (user, team, supplier)
        super().setUp()
        user = self.create_user()
        self.auth_as_user(user)

        team = user.teams.first()
        self.team = team
        self.team_id = team.id
        self.supplier = self.create_supplier(team=team)

        self.item = Item.objects.create(
            description="Test Item",
            item_number="123",
            price=100.54,
            price_currency="USD",
            supplier=self.supplier,
            team_id=self.team_id,
        )
        self.serializer = ItemSerializer(self.item)

    def test_item_creation(self):
        self.maxDiff = None

        # Check that the item we created in the setup exists and verify its data
        self.assertEqual(Item.objects.count(), 1)
        expected_data = {
            "id": str(self.item.id),
            "team": self.team_id,
            "description": "Test Item",
            "item_number": "123",
            "price": 100.54,
            "price_currency": "USD",
            "supplier": {
                "id": self.supplier.id,
                "name": self.supplier.name,
                "website_url": self.supplier.website_url,
                "archived_at": None,
                "onboarding_status": self.supplier.onboarding_status,
            },
            "unit_of_measure": "Each",
            "units_per_measure": 1.0,
            "archived_at": None,
            "tags": [],
            "notes": None,
            "item_sku": None,
            "manufacturer_part_number": None,
            "supplier_part_number": None,
            "upc": None,
            "unspsc": None,
            "hs_code": None,
            "color": None,
            "weight_value": None,
            "weight_unit": None,
            "dimensions": None,
            "production_lead_time": None,
            "transit_lead_time": None,
            "total_lead_time": None,
            "thumbnail": None,
        }
        expected_data.update({field: None for field in ITEM_OPTIONAL_FIELDS})
        self.assertDictEqual(self.serializer.data, expected_data)

    def test_item_price_decimal_precision_with_more_than_8_decimals(self):
        self.item.price = 100.543456789
        self.item.save()
        self.item.refresh_from_db()
        self.assertEqual(self.serializer.data["price"], 100.54345679)

    def test_item_serializer_price_precision_truncates_trailing_zeroes(self):
        self.item.price = 0.25500000
        self.item.save()
        self.item.refresh_from_db()
        self.assertEqual(self.serializer.data["price"], 0.255)

    def test_item_list(self):
        # test the get all items endpoint - verify we get only 1 item
        url = reverse("items-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()["results"]), Item.objects.count())

    def test_item_details(self):
        self.maxDiff = None

        # test the get items
        # verify we get one item and that it is the item we created in the setup
        response = self.client.get(reverse("items-list"))
        response_dict = response.json()["results"][0]
        response_dict = {
            "id": response_dict["id"],
            "team": response_dict["team"],
            "description": response_dict["description"],
            "item_number": response_dict["itemNumber"],
            "price": response_dict["price"],
            "price_currency": response_dict["priceCurrency"],
            "supplier": {
                "id": self.supplier.id,
                "name": self.supplier.name,
                "website_url": self.supplier.website_url,
                "archived_at": None,
                "onboarding_status": self.supplier.onboarding_status,
            },
            "archived_at": None,
            "tags": response_dict["tags"],
            "notes": response_dict["notes"],
            "item_sku": response_dict["itemSku"],
            "manufacturer_part_number": response_dict["manufacturerPartNumber"],
            "supplier_part_number": response_dict["supplierPartNumber"],
            "upc": response_dict["upc"],
            "unspsc": response_dict["unspsc"],
            "hs_code": response_dict["hsCode"],
            "color": response_dict["color"],
            "weight_value": response_dict["weightValue"],
            "weight_unit": response_dict["weightUnit"],
            "dimensions": response_dict["dimensions"],
            "production_lead_time": response_dict["productionLeadTime"],
            "transit_lead_time": response_dict["transitLeadTime"],
            "total_lead_time": response_dict["totalLeadTime"],
            "unit_of_measure": response_dict["unitOfMeasure"],
            "units_per_measure": response_dict["unitsPerMeasure"],
            "thumbnail": response_dict["thumbnail"],
        }

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()["results"]), Item.objects.count())
        self.assertDictEqual(response_dict, self.serializer.data)

    def test_filter_by_name_sku(self):
        search_results = Item.objects.filter(description="Test Item", item_number="123")
        no_results = Item.objects.filter(description="fake name")

        self.assertEqual(len(search_results), 1)
        self.assertEqual(len(no_results), 0)

    def test_filter_fuzzy_name(self):
        search_results = Item.objects.filter(description__icontains="test")
        self.assertEqual(len(search_results), 1)

    def test_archived_supplier_and_items(self):
        self.supplier = self.supplier.archive()

        self.assertTrue(self.supplier.is_archived())
        self.assertTrue(self.item.is_archived())

    def test_retrieve_existing_item(self):
        url = reverse("items-detail", args=[self.item.id])
        response = self.client.get(url)
        serializer = ItemSerializer(self.item)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, serializer.data)

    def test_retrieve_nonexistent_item(self):
        url = reverse("items-detail", args=[RANDOM_UUID])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        expected_error_response = {
            "errors": [{"attr": None, "code": "not_found", "detail": "Not found."}],
            "type": "client_error",
        }
        self.assertDictEqual(response.data, expected_error_response)

    def test_retrieve_item_not_in_user_team(self):
        # Create an item that does not belong to the user's teams
        other_team = Team.objects.create(
            name="Team B",
        )
        supplier = self.create_supplier(website_url="https://team-b-supplier.com")
        item_not_in_user_team = Item.objects.create(
            description="Unrelated Item",
            item_number="456",
            price=50.00,
            price_currency="USD",
            team=other_team,
            supplier_id=supplier.id,
        )
        url = reverse("items-detail", args=[item_not_in_user_team.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        expected_error_response = {
            "errors": [{"attr": None, "code": "not_found", "detail": "Not found."}],
            "type": "client_error",
        }
        self.assertDictEqual(response.data, expected_error_response)

    def test_create_item_success(self):
        url = reverse("items-list")
        data = {
            "description": "New Item",
            "item_number": "124",
            "price": 150.00,
            "price_currency": "USD",
            "supplier_id": self.supplier.id,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)

    def test_create_item_invalid_data(self):
        url = reverse("items-list")
        data = {
            "description": "",
            "item_number": "124",
            "price": -150.00,
            "price_currency": "USD",
            "supplier_id": self.supplier.id,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 400)

    def test_item_update_success(self):
        url = reverse("items-detail", args=[self.item.id])
        data = json.dumps({"price": 15.00})
        response = self.client.put(url, data, content_type="application/json")
        response_data = response.json()

        print("Response Data:", response_data)

        self.assertEqual(response.status_code, 200)
        self.item.refresh_from_db()
        self.assertEqual(float(self.item.price.amount), 15.00)

    def test_item_update_price_more_than_8_decimals(self):
        url = reverse("items-detail", args=[self.item.id])
        data = json.dumps({"price": 0.25551234567890})
        response = self.client.put(url, data, content_type="application/json")
        self.assertEqual(response.status_code, 400)

    def test_item_tagging(self):
        # first, create tags for the team
        CustomTag.objects.create(name="fancy", team=self.team, color="#F0F0F0")
        CustomTag.objects.create(name="laboratory", team=self.team, color="#00FFFF")
        url = reverse("items-detail", args=[self.item.id])
        data = json.dumps(
            {
                "tags": [
                    {
                        "name": "fancy",
                    },
                    {
                        "name": "laboratory",
                    },
                ]
            }
        )
        response = self.client.put(url, data, content_type="application/json")
        self.assertEqual(response.status_code, 200)
        self.item.refresh_from_db()
        self.assertEqual(self.item.tags.count(), 2)
        self.assertEqual(self.item.tags.first().name, "fancy")
        self.assertEqual(self.item.tags.first().color, "#F0F0F0")
        self.assertEqual(self.item.tags.last().name, "laboratory")
        self.assertEqual(self.item.tags.last().color, "#00FFFF")

    def test_item_update_nonexistent(self):
        url = reverse("items-detail", args=[RANDOM_UUID])
        data = {"description": "Updated Name"}
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 404)

    def test_item_archive_success(self):
        url = reverse("items-detail", args=[self.item.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, 200)
        self.item.refresh_from_db()
        self.assertIsNotNone(self.item.archived_at)

    def test_item_already_archived(self):
        self.item.archived_at = timezone.now()
        self.item.save()
        url = reverse("items-detail", args=[self.item.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, 200)
        self.assertIn("previously archived", response.json()["message"])

    def test_items_count(self):
        url = reverse("items-count")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["count"], Item.objects.count())

    def test_item_partial_update(self):
        # Test partial update for unarchiving
        self.item.archived_at = timezone.now()
        self.item.save()
        url = reverse("items-detail", args=[self.item.id])
        data = {"archived_at": None}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, 200)
        self.item.refresh_from_db()
        self.assertIsNone(self.item.archived_at)
        self.assertEqual(self.item.description, response.json()["description"])

        # Test partial update for archiving
        data = {
            "archived_at": timezone.now().isoformat(),
            "description": "new description",
        }
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, 200)
        self.item.refresh_from_db()
        self.assertIsNotNone(self.item.archived_at)
        self.assertEqual(response.json()["description"], "new description")

    def test_access_with_different_team(self):
        # Create a new user belonging to a different team
        user2 = self.create_user(email="<EMAIL>")
        self.auth_as_user(user2)

        # test get items list
        url = reverse("items-list")
        response = self.client.get(url)

        # The new user should not have access to the original team's data and their team has no items
        # expect a successful call with no data
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 0)

        # test getting a particular item owner by self.team
        # expect a 404
        url = reverse("items-detail", args=[self.item.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class TestItemFieldsDisplayConfigViewset(TestCase):
    def setUp(self):
        # create an item and its dependencies (user, team, supplier)
        super().setUp()
        user = self.create_user()
        self.auth_as_user(user)
        team = user.teams.first()
        self.team_id = team.id

    def test_item_fields_display_config_created_by_default(self):
        item_fields_display_config = ItemFieldsDisplayConfig.objects.get(
            team_id=self.team_id
        )
        self.assertIsNotNone(item_fields_display_config)

        for field in ITEM_OPTIONAL_FIELDS:
            self.assertFalse(getattr(item_fields_display_config, field))

    def test_item_fields_display_config_update_success(self):
        url = reverse("item-fields-display-config-detail")
        data = {field: True for field in ITEM_OPTIONAL_FIELDS}

        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 200)

        item_fields_display_config = ItemFieldsDisplayConfig.objects.get(
            team_id=self.team_id
        )
        self.assertIsNotNone(item_fields_display_config)
        for field, value in data.items():
            self.assertEqual(getattr(item_fields_display_config, field), value)

    def test_access_with_different_team(self):
        # Create a new user belonging to a different team
        user2 = self.create_user(email="<EMAIL>")
        self.auth_as_user(user2)

        # try to get configs
        url = reverse("item-fields-display-config-detail")
        response = self.client.get(url)

        # make sure the config belongs to user 2 and not the initial user
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["team"], user2.teams.first().pk)
