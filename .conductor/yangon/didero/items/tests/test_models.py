from decimal import Decimal

from didero.items.models import Item
from didero.testing.cases import TestCase


class TestItemModel(TestCase):
    def setUp(self):
        # create an item and its dependencies (user, team, supplier)
        super().setUp()
        user = self.create_user()
        self.auth_as_user(user)

        team = user.teams.first()
        self.team_id = team.id
        self.supplier = self.create_supplier(team=team)

    def test_item_price_decimal_precision(self):
        price = 100.54342132
        item = Item.objects.create(
            description="Test Item",
            item_number="123",
            price=price,
            price_currency="USD",
            supplier=self.supplier,
            team_id=self.team_id,
        )
        self.assertEqual(item.price.amount, Decimal(str(price)))

    def test_item_price_decimal_precision_with_more_than_8_decimals(self):
        id = Item.objects.create(
            description="Test Item",
            item_number="123",
            price=100.543456789,
            price_currency="USD",
            supplier=self.supplier,
            team_id=self.team_id,
        ).id
        item = Item.objects.get(
            id=id
        )  # the price is truncated to 8 decimal places at model retrieval from DB time.
        self.assertEqual(item.price.amount, Decimal("100.54345679"))
