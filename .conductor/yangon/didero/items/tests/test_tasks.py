import csv
import io
from typing import List

from django.test import TestCase

from didero.bulk_uploads.models import (
    BulkUploadChunk,
    BulkUploadJob,
    BulkUploadType,
    ChunkStatus,
)
from didero.bulk_uploads.views import validate_csv_headers
from didero.items.models import Item
from didero.items.tasks import process_chunk_items, validate_row
from didero.suppliers.models import Supplier
from didero.testing.cases import TestCase as DideroTestCase


class TestBulkUploadItemsParsing(DideroTestCase):
    def setUp(self):
        super().setUp()

        self.user = self.create_user(
            email="<EMAIL>", first_name="Test", last_name="User"
        )
        self.team = self.user.teams.first()

        self.supplier = Supplier.objects.create(name="Test Supplier", team=self.team)

    def test_csv_header_validation_valid_headers(self):
        """Test that valid CSV headers pass validation"""
        valid_headers = [
            "number",
            "description",
            "price",
            "price_currency",
            "supplier_name",
        ]
        is_valid, errors = validate_csv_headers(valid_headers, BulkUploadType.ITEMS)

        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

    def test_csv_header_validation_missing_required(self):
        """Test that missing required headers fail validation"""
        invalid_headers = [
            "number",
            "description",
            "price",
        ]  # Missing price_currency, supplier_name
        is_valid, errors = validate_csv_headers(invalid_headers, BulkUploadType.ITEMS)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
        self.assertIn("Missing required fields", errors[0])
        self.assertIn("price_currency", errors[0])
        self.assertIn("supplier_name", errors[0])

    def test_csv_header_validation_unknown_fields(self):
        """Test that unknown fields fail validation for items"""
        headers_with_unknown = [
            "number",
            "description",
            "price",
            "price_currency",
            "supplier_name",
            "unknown_field",
        ]
        is_valid, errors = validate_csv_headers(
            headers_with_unknown, BulkUploadType.ITEMS
        )

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
        self.assertIn("Unknown fields in header", errors[0])
        self.assertIn("unknown_field", errors[0])

    def test_csv_header_validation_with_optional_fields(self):
        """Test that valid optional fields pass validation"""
        headers_with_optional = [
            "number",
            "description",
            "price",
            "price_currency",
            "supplier_name",
            "color",
            "dimensions",
        ]
        is_valid, errors = validate_csv_headers(
            headers_with_optional, BulkUploadType.ITEMS
        )

        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

    def test_row_validation_valid_row(self):
        """Test that a valid row passes validation"""
        valid_row = {
            "number": "ITEM001",
            "description": "Test Item",
            "price": "10.00",
            "price_currency": "USD",
            "supplier_name": "Test Supplier",
        }
        is_valid, error_msg = validate_row(valid_row)

        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")

    def test_row_validation_missing_required_fields(self):
        """Test that rows missing required fields fail validation"""
        invalid_row = {
            "number": "ITEM001",
            "description": "Test Item",
            # Missing price, price_currency, supplier_name
        }
        is_valid, error_msg = validate_row(invalid_row)

        self.assertFalse(is_valid)
        self.assertIn("Missing or empty required fields", error_msg)

    def test_process_chunk_items_successful(self):
        """Test successful processing of an items chunk"""
        job = BulkUploadJob.objects.create(
            team=self.team,
            upload_type=BulkUploadType.ITEMS,
            filename="test.csv",
            total_rows=2,
        )

        chunk_data = [
            {
                "number": "ITEM001",
                "description": "Test Item 1",
                "price": "10.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",
            },
            {
                "number": "ITEM002",
                "description": "Test Item 2",
                "price": "20.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",
            },
        ]

        chunk = BulkUploadChunk.objects.create(
            job=job, chunk_number=1, data=chunk_data, start_row=1, end_row=2
        )

        result = process_chunk_items(job, chunk)

        self.assertEqual(result["processed_count"], 2)
        self.assertEqual(len(result["errors"]), 0)

        self.assertEqual(Item.objects.filter(team=self.team).count(), 2)
        item1 = Item.objects.get(item_number="ITEM001")
        self.assertEqual(item1.description, "Test Item 1")
        self.assertEqual(float(item1.price.amount), 10.00)

    def test_process_chunk_items_with_errors(self):
        """Test processing of an items chunk with validation errors"""
        job = BulkUploadJob.objects.create(
            team=self.team,
            upload_type=BulkUploadType.ITEMS,
            filename="test.csv",
            total_rows=3,
        )

        chunk_data = [
            {
                "number": "ITEM001",
                "description": "Test Item 1",
                "price": "10.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",  # Valid
            },
            {
                "number": "ITEM002",
                "description": "Test Item 2",
                "price": "20.00",
                "price_currency": "USD",
                "supplier_name": "Nonexistent Supplier",  # Invalid - supplier doesn't exist
            },
            {
                "number": "",  # Invalid - missing required field
                "description": "Test Item 3",
                "price": "30.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",
            },
        ]

        chunk = BulkUploadChunk.objects.create(
            job=job, chunk_number=1, data=chunk_data, start_row=1, end_row=3
        )

        result = process_chunk_items(job, chunk)

        self.assertEqual(result["processed_count"], 1)  # Only 1 successful item
        self.assertEqual(len(result["errors"]), 2)  # 2 errors

        self.assertEqual(Item.objects.filter(team=self.team).count(), 1)
        item = Item.objects.get(item_number="ITEM001")
        self.assertEqual(item.description, "Test Item 1")

        self.assertIn(2, result["errors"])  # Row 2 error (nonexistent supplier)
        self.assertIn(3, result["errors"])  # Row 3 error (missing number)

    def test_process_chunk_items_whitespace_handling(self):
        """Test that whitespace is properly handled in CSV data"""
        job = BulkUploadJob.objects.create(
            team=self.team,
            upload_type=BulkUploadType.ITEMS,
            filename="test.csv",
            total_rows=1,
        )

        chunk_data = [
            {
                "number": "  ITEM001  ",
                "description": "  Test Item  ",
                "price": "  10.00  ",
                "price_currency": "  USD  ",
                "supplier_name": "  Test Supplier  ",
            }
        ]

        chunk = BulkUploadChunk.objects.create(
            job=job, chunk_number=1, data=chunk_data, start_row=1, end_row=1
        )

        result = process_chunk_items(job, chunk)

        self.assertEqual(result["processed_count"], 1)
        self.assertEqual(len(result["errors"]), 0)

        item = Item.objects.get(item_number="ITEM001")
        self.assertEqual(item.description, "Test Item")  # Whitespace stripped

    def test_process_chunk_items_update_existing(self):
        """Test that existing items are updated rather than creating duplicates"""
        existing_item = Item.objects.create(
            team=self.team,
            supplier=self.supplier,
            item_number="ITEM001",
            description="Old Description",
            price=5.00,
            price_currency="USD",
        )

        job = BulkUploadJob.objects.create(
            team=self.team,
            upload_type=BulkUploadType.ITEMS,
            filename="test.csv",
            total_rows=1,
        )

        chunk_data = [
            {
                "number": "ITEM001",
                "description": "Updated Description",
                "price": "15.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",
            }
        ]

        chunk = BulkUploadChunk.objects.create(
            job=job, chunk_number=1, data=chunk_data, start_row=1, end_row=1
        )

        result = process_chunk_items(job, chunk)

        self.assertEqual(result["processed_count"], 1)
        self.assertEqual(len(result["errors"]), 0)

        self.assertEqual(Item.objects.filter(team=self.team).count(), 1)
        updated_item = Item.objects.get(item_number="ITEM001")
        self.assertEqual(updated_item.description, "Updated Description")
        self.assertEqual(float(updated_item.price.amount), 15.00)
        self.assertEqual(updated_item.id, existing_item.id)  # Same item, not new one

    def test_process_chunk_items_row_count_issue(self):
        """Test the specific row count issue: 3 rows with 1 error should show 2 successful"""
        job = BulkUploadJob.objects.create(
            team=self.team,
            upload_type=BulkUploadType.ITEMS,
            filename="test.csv",
            total_rows=3,
        )

        chunk_data = [
            {
                "number": "ITEM001",
                "description": "Test Item 1",
                "price": "10.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",  # Valid
            },
            {
                "number": "ITEM002",
                "description": "Test Item 2",
                "price": "20.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",  # Valid
            },
            {
                "number": "",  # Invalid - missing required field
                "description": "Test Item 3",
                "price": "30.00",
                "price_currency": "USD",
                "supplier_name": "Test Supplier",
            },
        ]

        chunk = BulkUploadChunk.objects.create(
            job=job, chunk_number=1, data=chunk_data, start_row=1, end_row=3
        )

        result = process_chunk_items(job, chunk)

        self.assertEqual(len(chunk_data), 3)
        self.assertEqual(result["processed_count"], 2)
        self.assertEqual(len(result["errors"]), 1)
