import structlog
from django_opensearch_dsl import Document, fields
from django_opensearch_dsl.registries import registry

from didero.items.models import Item
from didero.suppliers.models import Supplier
from didero.users.models.team_models import Team

logger = structlog.get_logger(__name__)


@registry.register_document
class ItemDocument(Document):
    team_uuid = fields.KeywordField()
    supplier = fields.ObjectField(
        properties={
            "name": fields.TextField(),
            "id": fields.KeywordField(),
            "archived_at": fields.DateField(),
        }
    )
    uuid = fields.KeywordField()
    tags = fields.ObjectField(properties={"name": fields.TextField()})

    class Index:
        name = "items"
        settings = {"number_of_shards": 1, "number_of_replicas": 0}

    class Django:
        model = Item

        fields = [
            "description",
            "item_sku",
            "item_number",
            "archived_at",
        ]
        related_models = [Team, Supplier]

    def prepare_uuid(self, instance):
        return instance.id

    def prepare_team_uuid(self, instance):
        return instance.team.uuid

    def prepare_tags(self, instance):
        return [{"name": tag.name} for tag in instance.tags.all()]

    def get_instances_from_related(self, related_instance):
        if isinstance(related_instance, Team):
            return Item.objects.filter(team__uuid=related_instance.uuid)

        if isinstance(related_instance, Supplier):
            return Item.objects.filter(supplier__pk=related_instance.pk)
