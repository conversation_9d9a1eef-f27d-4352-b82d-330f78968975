import csv
import io
from typing import Any, Dict, List, Tuple

import structlog
from celery import shared_task
from celery_singleton import Singleton
from django.db import IntegrityError

from didero.bulk_uploads.schemas import BulkUploadProcessResult
from didero.integrations.models import TeamIntegratedAppCredential
from didero.integrations.schemas import IntegratedApp, IntegrationProvider
from didero.integrations.utils.alloy import get_alloy_items
from didero.items.models import ERPItem, Item
from didero.suppliers.models import Supplier
from didero.users.models.team_models import Team

logger = structlog.get_logger(__name__)

LOCK_EXPIRE = 60 * 60  # 1 hour

REQUIRED_ITEM_FIELDS = [
    "number",
    "description",
    "price",
    "price_currency",
    "supplier_name",
]
OPTIONAL_ITEM_FIELDS = [
    "color",
    "dimensions",
    "hs_code",
    "item_sku",
    "manufacturer_part_number",
    "notes",
    "supplier_part_number",
    "thumbnail",
    "unspsc",
    "upc",
    "weight_unit",
    "weight_value",
    "production_lead_time",
    "transit_lead_time",
    "total_lead_time",
]


@shared_task(base=Singleton, lock_expiry=LOCK_EXPIRE, queue="bulk_supplier_imports")
def process_and_save_erp_items(team, items):
    logger.info("Processing Alloy items:")
    for item in items:
        try:
            item = ERPItem.objects.get(ipaas_item_id=item["id"])
            logger.info(f"ERPItem {item.id} already exists")
        except ERPItem.DoesNotExist:
            # Every Netsuite item should have a name, but for some reason there
            # are sometimes empty ones; skip them
            if not item["itemName"]:
                continue
            price = item["unitPrice"] if item["unitPrice"] else 0.0
            didero_item = Item.objects.create(
                team=team,
                description=item["itemName"],
                # item_number is supposed to be a unique identifier, but Alloy
                # just uses a UUUID, so we just take the first 30 characters
                item_number=item["id"][:30],
                supplier=item["companyId"],
                price=price,
            )

            erp_item = ERPItem.objects.create(
                item=didero_item,
                erp_provider=IntegrationProvider.ALLOY,
                erp_app=IntegratedApp.NETSUITE,
                ipaas_item_id=item["id"],
                remote_item_id=item["remoteId"],
            )

            try:
                logger.info(f"Didero Item {didero_item.id} created")
                didero_item.save()
                logger.info(f"ERPItem {erp_item.id} created")
                erp_item.save()
            except IntegrityError as e:
                if "unique_item_number_per_team_supplier" in str(e):
                    return False, None
    return True, None


# Default: start at page 1. Then call children with increasing page number
@shared_task(base=Singleton, lock_expiry=LOCK_EXPIRE, queue="bulk_supplier_imports")
def bulk_import_erp_items(app: str, team_id: int, page_number: int = 1) -> None:
    team = Team.objects.get(id=team_id)
    alloy_credential = TeamIntegratedAppCredential.objects.get(team=team, app_name=app)
    logger.info("doing page %s", page_number)
    items = get_alloy_items(alloy_credential.credential_id, page_number=page_number)
    logger.info(f"Alloy items: {items}")
    if items:
        success, error = process_and_save_erp_items(team, items)
        if success:
            bulk_import_erp_items.delay(
                app=app, team_id=team_id, page_number=page_number + 1
            )


def validate_row(row: Dict[str, Any]) -> Tuple[bool, str]:
    missing_required = [field for field in REQUIRED_ITEM_FIELDS if not row.get(field)]
    if missing_required:
        return False, f"Missing or empty required fields: {', '.join(missing_required)}"
    return True, ""


class InvalidCSVHeaderError(Exception):
    """Exception raised for errors in the header of a file."""

    def __init__(self, message: str) -> None:
        self.message = message
        super().__init__(self.message)


def process_chunk_items(job: Any, chunk: Any) -> BulkUploadProcessResult:
    """
    Process a chunk of items for the new bulk upload system
    """
    from didero.bulk_uploads.models import BulkUploadChunk, BulkUploadJob

    team = job.team
    rows = chunk.data
    items = []
    errors = {}

    logger.info(f"Processing items chunk {chunk.chunk_number} for team {team.id}")

    # Cache suppliers for performance
    suppliers_cache = {
        supplier.name: supplier for supplier in Supplier.objects.filter(team=team)
    }
    logger.info(f"Cached {len(suppliers_cache)} suppliers for team {team.id}")

    for idx, row in enumerate(rows):
        row_number = chunk.start_row + idx
        try:
            row = {
                key.strip(): value.strip()
                for key, value in row.items()
                if isinstance(value, str) and value.strip()
            }

            row_valid, row_errors = validate_row(row)
            if not row_valid:
                errors[row_number] = f"Row validation error: {str(row_errors)}"
                continue

            supplier_name = row.pop("supplier_name", "")
            supplier = suppliers_cache.get(supplier_name)
            if not supplier:
                errors[row_number] = (
                    f"Could not find the supplier {supplier_name} for row {row_number}."
                )
                continue

            try:
                item_number = row.pop("number", "")
                item, created = Item.objects.update_or_create(
                    item_number=item_number,
                    team=team,
                    supplier=supplier,
                    defaults=row,
                )
                items.append(item)
            except IntegrityError as e:
                errors[row_number] = f"Row {row_number} encountered an error: {str(e)}"
                continue

        except Exception as e:
            error_msg = f"Failed to process row {row_number}: {str(e)}"
            logger.error(
                error_msg,
                row_number=row_number,
                row_data=row,
                error_type=type(e).__name__,
            )
            errors[row_number] = error_msg
            continue

    logger.info(
        f"Processed items chunk {chunk.chunk_number}: {len(items)} items, {len(errors)} errors"
    )
    return BulkUploadProcessResult(processed_count=len(items), errors=errors)
