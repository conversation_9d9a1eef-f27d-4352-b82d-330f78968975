# Generated by Django 4.2.7 on 2024-07-12 20:21

from django.db import migrations
import taggit.managers


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0001_initial"),
        ("items", "0013_remove_item_items_item_item_na_18a110_idx_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="item",
            name="tags",
            field=taggit.managers.TaggableManager(
                help_text="A comma-separated list of tags.",
                through="common.TaggedItem",
                to="common.CustomTag",
                verbose_name="Tags",
            ),
        ),
    ]
