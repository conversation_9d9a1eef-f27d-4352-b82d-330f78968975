# Generated by Django 4.2.7 on 2024-07-11 15:27

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone

"""
For any existing team, we create a default ItemConfig for them.
"""


def create_default_item_config(apps, schema_editor):
    ItemConfig = apps.get_model("items", "ItemConfig")
    Team = apps.get_model("users", "Team")
    for team in Team.objects.all():
        if not ItemConfig.objects.filter(team=team).exists():
            ItemConfig.objects.create(team=team)


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0019_remove_team_default_address"),
        ("items", "0013_remove_item_items_item_item_na_18a110_idx_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ItemConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("notes", models.BooleanField(default=False)),
                ("item_sku", models.BooleanField(default=False)),
                ("manufacturer_part_number", models.BooleanField(default=False)),
                ("supplier_part_number", models.BooleanField(default=False)),
                ("upc", models.BooleanField(default=False)),
                ("unspsc", models.BooleanField(default=False)),
                ("hs_code", models.BooleanField(default=False)),
                ("color", models.BooleanField(default=False)),
                ("weight_value", models.BooleanField(default=False)),
                ("weight_unit", models.BooleanField(default=False)),
                ("dimensions", models.BooleanField(default=False)),
                ("production_lead_time", models.BooleanField(default=False)),
                ("transit_lead_time", models.BooleanField(default=False)),
                ("total_lead_time", models.BooleanField(default=False)),
                ("unit_of_measure", models.BooleanField(default=False)),
                ("units_per_measure", models.BooleanField(default=False)),
                ("thumbnail", models.BooleanField(default=False)),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.team",
                        unique=True,
                        related_name="item_config",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.RunPython(
            create_default_item_config, reverse_code=migrations.RunPython.noop
        ),
    ]
