# Generated by Django 4.2.7 on 2024-09-19 13:52

from decimal import Decimal
from django.db import migrations
import djmoney.models.fields
import djmoney.models.validators


class Migration(migrations.Migration):
    dependencies = [
        ("items", "0022_alter_item_unit_of_measure_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="item",
            name="price",
            field=djmoney.models.fields.MoneyField(
                decimal_places=8,
                default=Decimal("0E-8"),
                default_currency="USD",
                max_digits=16,
                validators=[djmoney.models.validators.MinMoneyValidator(Decimal("0"))],
            ),
        ),
    ]
