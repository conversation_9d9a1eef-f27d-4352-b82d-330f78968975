# Generated by Django 4.2.7 on 2024-07-10 19:02

import didero.items.models
from django.db import migrations, models


def populate_item_number(apps, schema_editor):
    Item = apps.get_model("items", "Item")
    for item in Item.objects.all():
        item.item_number = item.item_sku
        item.save()


class Migration(migrations.Migration):
    dependencies = [
        ("items", "0010_remove_item_items_item_team_id_68adc0_idx_and_more"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="item",
            name="unique_item_sku_per_team_supplier",
        ),
        migrations.AddField(
            model_name="item",
            name="color",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="dimensions",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="hs_code",
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True),
        ),
        # First create the field, where it's allowed to be null. (Since there are
        # existing items, which won't have item_number)
        migrations.AddField(
            model_name="item",
            name="item_number",
            field=models.CharField(max_length=30, null=True),
        ),
        # Then populate item_number with the item_sku
        migrations.RunPython(
            populate_item_number, reverse_code=migrations.RunPython.noop
        ),
        # Now make the field required
        migrations.AlterField(
            model_name="item",
            name="item_number",
            field=models.CharField(max_length=30),
        ),
        migrations.AddField(
            model_name="item",
            name="manufacturer_part_number",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="production_lead_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="supplier_part_number",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="thumbnail",
            field=models.ImageField(
                blank=True,
                null=True,
                upload_to=didero.items.models.get_thumbnail_upload_path,
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="total_lead_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="transit_lead_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="unit_of_measure",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="units_per_measure",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="unspsc",
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="upc",
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
        migrations.AddField(
            model_name="item",
            name="weight_unit",
            field=models.CharField(
                blank=True,
                choices=[
                    ("milligram", "MILLIGRAM"),
                    ("gram", "GRAM"),
                    ("kilogram", "KILOGRAM"),
                    ("tonne", "TONNE"),
                    ("grain", "GRAIN"),
                    ("dram", "DRAM"),
                    ("ounce", "OUNCE"),
                    ("pound", "POUND"),
                    ("stone", "STONE"),
                    ("hundredweight", "HUNDREDWEIGHT"),
                    ("us_ton", "US_TON"),
                    ("imperial_ton", "IMPERIAL_TON"),
                    ("kilotonne", "KILOTONNE"),
                ],
                max_length=20,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="weight_value",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="item",
            name="item_name",
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name="item",
            name="item_sku",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["item_number"], name="items_item_item_nu_7f6e11_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["created_at"], name="items_item_created_3cdd06_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="item",
            constraint=models.UniqueConstraint(
                fields=("team", "supplier", "item_number"),
                name="unique_item_number_per_team_and_supplier",
            ),
        ),
    ]
