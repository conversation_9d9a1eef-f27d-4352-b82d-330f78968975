# Generated by Django 4.2.7 on 2024-07-10 19:47

from django.db import migrations, models


def populate_item_description(apps, schema_editor):
    Item = apps.get_model("items", "Item")
    for item in Item.objects.all():
        item.item_description = item.item_name
        item.save()


class Migration(migrations.Migration):
    dependencies = [
        ("items", "0011_revamp_item_model"),
    ]
    # First create the field, where it's allowed to be null. (Since there are
    # existing items, which won't have item_description)
    operations = [
        migrations.AddField(
            model_name="item",
            name="item_description",
            field=models.TextField(null=True),
        ),
        # Then populate item_description with the item_name
        migrations.RunPython(
            populate_item_description, reverse_code=migrations.RunPython.noop
        ),
        # Now make the field required
        migrations.AlterField(
            model_name="item",
            name="item_description",
            field=models.TextField(),
        ),
    ]
