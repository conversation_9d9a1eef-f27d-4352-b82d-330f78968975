# Generated by Django 4.2.7 on 2024-07-25 15:57

from django.db import migrations
import taggit.managers


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0002_alter_taggeditem_object_id"),
        ("items", "0018_merge_0017_erpitem_0017_merge_20240722_1516"),
    ]

    operations = [
        migrations.AlterField(
            model_name="item",
            name="tags",
            field=taggit.managers.TaggableManager(
                blank=True,
                help_text="A comma-separated list of tags.",
                through="common.TaggedItem",
                to="common.CustomTag",
                verbose_name="Tags",
            ),
        ),
    ]
