# Generated by Django 4.2.7 on 2024-12-10 19:02

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("items", "0023_alter_item_price"),
    ]

    operations = [
        migrations.CreateModel(
            name="ItemCustomField",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("custom_fields", models.JSONField()),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_fields",
                        to="items.item",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["item"], name="items_itemc_item_id_2f62a4_idx"
                    ),
                    models.Index(
                        fields=["custom_fields"], name="items_itemc_custom__14c060_idx"
                    ),
                    models.Index(
                        fields=["item", "custom_fields"],
                        name="items_itemc_item_id_438f17_idx",
                    ),
                ],
            },
        ),
        migrations.AddConstraint(
            model_name="itemcustomfield",
            constraint=models.UniqueConstraint(
                fields=("item",), name="unique_per_item"
            ),
        ),
    ]
