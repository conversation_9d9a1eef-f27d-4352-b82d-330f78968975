# Generated by Django 4.2.7 on 2024-07-18 19:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0039_alter_supplier_unique_together_and_more"),
        ("items", "0015_rename_itemconfig_itemfieldsdisplayconfig"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="item",
            name="unique_item_number_per_team_and_supplier",
        ),
        migrations.AlterField(
            model_name="item",
            name="supplier",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="suppliers.supplier",
            ),
        ),
        migrations.AddConstraint(
            model_name="item",
            constraint=models.UniqueConstraint(
                condition=models.Q(("supplier__isnull", False)),
                fields=("team", "supplier", "item_number"),
                name="unique_item_number_per_team_and_supplier",
            ),
        ),
    ]
