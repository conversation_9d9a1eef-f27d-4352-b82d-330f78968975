# Generated by Django 4.2.7 on 2024-05-23 16:17

from django.db import migrations, models


# There are currently no items in staging or prod that have null names/skus.
# But for best practice, we check if there are, and bulk edit them to just
# use their uuid as the name/sku.
def set_default_item_name_and_sku(apps, schema_editor):
    Item = apps.get_model("items", "Item")
    items_to_update = []

    for item in Item.objects.filter(item_name=""):
        item.item_name = str(item.id)
        items_to_update.append(item)

    for item in Item.objects.filter(item_sku=""):
        item.item_sku = str(item.id)
        items_to_update.append(item)

    Item.objects.bulk_update(items_to_update, ["item_name", "item_sku"])


class Migration(migrations.Migration):
    dependencies = [
        ("items", "0007_remove_item_items_item_itemnam_9dfc34_idx_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="item",
            name="item_name",
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="item",
            name="item_sku",
            field=models.CharField(max_length=255),
        ),
        migrations.AddConstraint(
            model_name="item",
            constraint=models.UniqueConstraint(
                fields=("team", "supplier", "item_sku"),
                name="unique_item_sku_per_team_supplier",
            ),
        ),
        migrations.RunPython(set_default_item_name_and_sku),
    ]
