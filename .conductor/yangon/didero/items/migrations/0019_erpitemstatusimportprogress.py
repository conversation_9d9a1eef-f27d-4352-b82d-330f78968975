# Generated by Django 4.2.7 on 2024-07-24 17:24

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0020_teamsetting_teamsetting_unique_user_team_name"),
        ("items", "0018_merge_0017_erpitem_0017_merge_20240722_1516"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPItemStatusImportProgress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("integration", models.CharField(choices=[("alloy", "ALLOY")])),
                (
                    "app",
                    models.CharField(
                        choices=[
                            ("netsuite", "NETSUITE"),
                            ("business_central", "BUSINESS_CENTRAL"),
                        ],
                        max_length=64,
                    ),
                ),
                (
                    "last_synced_at",
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
                (
                    "sync_status",
                    models.CharField(
                        choices=[
                            ("in_progress", "IN_PROGRESS"),
                            ("complete", "COMPLETE"),
                            ("failed", "FAILED"),
                        ],
                        max_length=64,
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="erp_item_status_import_progress",
                        to="users.team",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["team"], name="items_erpit_team_id_581c80_idx"
                    ),
                    models.Index(
                        fields=["integration"], name="items_erpit_integra_73068a_idx"
                    ),
                ],
            },
        ),
    ]
