# Generated by Django 4.2.7 on 2024-07-18 19:44

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("items", "0016_remove_item_unique_item_number_per_team_and_supplier_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("erp_provider", models.CharField(choices=[("alloy", "ALLOY")])),
                (
                    "erp_app",
                    models.CharField(
                        choices=[
                            ("netsuite", "NETSUITE"),
                            ("business_central", "BUSINESS_CENTRAL"),
                        ],
                        max_length=64,
                    ),
                ),
                ("ipaas_item_id", models.Char<PERSON>ield(max_length=64)),
                ("remote_item_id", models.CharField(max_length=64)),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="erp_items",
                        to="items.item",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["item"], name="items_erpit_item_id_688c50_idx"
                    ),
                    models.Index(
                        fields=["ipaas_item_id"], name="items_erpit_ipaas_i_22d5c2_idx"
                    ),
                ],
            },
        ),
    ]
