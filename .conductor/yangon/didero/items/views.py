import structlog
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters import rest_framework as filters
from django_filters.rest_framework import BaseInFilter, DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound
from rest_framework.filters import OrderingFilter
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.ai.item_extraction import extract_item_details_from_url
from didero.common.utils import handle_tag_update_from_request
from didero.filters import (
    ArchiveStatus,
    BaseFilter,
    ChoiceInFilter,
    NumberInFilter,
    get_enum_choices,
)
from didero.integrations.models import TeamIntegratedAppCredential
from didero.integrations.utils.alloy import get_alloy_item_count
from didero.items.models import ERPItem, Item, ItemFieldsDisplayConfig
from didero.items.serializers import (
    AIItemSerializer,
    ERPItemSerializer,
    ItemFieldsDisplayConfigSerializer,
    ItemSerializer,
)
from didero.items.tasks import bulk_import_erp_items
from didero.suppliers.models import Supplier
from didero.views import APIView, PagesPagination, TeamOwnerFilteredModelView

logger = structlog.get_logger()


# todo
class ItemFilter(BaseFilter):
    supplier_name = filters.CharFilter(
        field_name="supplier__name", lookup_expr="icontains"
    )
    supplier_id = filters.CharFilter(field_name="supplier__id", lookup_expr="exact")
    supplier_ids = NumberInFilter(field_name="supplier__id")
    description = filters.CharFilter(field_name="description", lookup_expr="exact")
    description_contains = filters.CharFilter(
        field_name="description", lookup_expr="icontains"
    )
    item_number = filters.CharFilter(field_name="item_number", lookup_expr="exact")
    item_number_contains = filters.CharFilter(
        field_name="item_number", lookup_expr="icontains"
    )
    item_sku = filters.CharFilter(field_name="item_sku", lookup_expr="exact")
    item_sku_contains = filters.CharFilter(
        field_name="item_sku", lookup_expr="icontains"
    )
    color = filters.CharFilter(field_name="color", lookup_expr="exact")
    hs_code = filters.CharFilter(field_name="hs_code", lookup_expr="icontains")
    manufacturer_part_number = filters.CharFilter(
        field_name="manufacturer_part_number", lookup_expr="icontains"
    )
    supplier_part_number = filters.CharFilter(
        field_name="supplier_part_number", lookup_expr="icontains"
    )
    tags = BaseInFilter(field_name="tags__name")
    upc = filters.CharFilter(field_name="upc", lookup_expr="icontains")
    unspsc = filters.CharFilter(field_name="unspsc", lookup_expr="icontains")
    weight_value_gte = filters.NumberFilter(
        field_name="weight_value", lookup_expr="gte"
    )
    weight_value_lte = filters.NumberFilter(
        field_name="weight_value", lookup_expr="lte"
    )
    archived = ChoiceInFilter(
        method="filter_archived", choices=get_enum_choices(ArchiveStatus)
    )

    # Todo should ensure that either both or neither of {price_gte/price_lte} and price_currency have to be included
    price_gte = filters.NumberFilter(field_name="price", lookup_expr="gte")
    price_lte = filters.NumberFilter(field_name="price", lookup_expr="lte")
    price_currency = filters.CharFilter(
        field_name="price_currency", lookup_expr="iexact"
    )

    class Meta:
        model = Item
        fields = {"supplier": ["exact"]}

    def multi_field_search(self, queryset, name, value):
        return queryset.filter(
            Q(supplier__name__icontains=value)
            | Q(description__icontains=value)
            | Q(item_number__icontains=value)
            | Q(manufacturer_part_number__icontains=value)
            | Q(supplier_part_number__icontains=value)
        ).distinct()

    def filter_queryset(self, queryset):
        # If request specifies archived, then we take that into account
        # Otherwise by default, we do not show contacts that are archived
        if "archived" not in self.data:
            queryset = queryset.filter(archived_at__isnull=True)
        return super().filter_queryset(queryset)


class ItemsViewset(APIView, TeamOwnerFilteredModelView):
    serializer_class = ItemSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_class = ItemFilter
    # The fields we can sort by:
    ordering_fields = [
        "description",
        "item_number",
        "price",
        "supplier",
    ]
    ordering = ["-created_at"]
    lookup_field = "id"
    lookup_url_kwarg = "uuid"
    model = Item

    def get_queryset(self):
        # Only return items that belong to {suppliers that aren't archived}
        queryset = super().get_queryset().filter(supplier__archived_at__isnull=True)
        return self.filterset_class(self.request.GET, queryset=queryset).qs.distinct()

    def retrieve(self, request, uuid=None):
        """
        Get a specific item.
        """
        try:
            item = get_object_or_404(super().get_queryset(), pk=uuid)
            serializer = self.serializer_class(item)
            return Response(serializer.data)
        except ValidationError as _:
            return Response(
                {"error": "Resource not found or access denied."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def get_supplier(self):
        team = self.request.team
        supplier_id = self.request.data.get("supplier_id")
        if not supplier_id:
            raise NotFound("Supplier ID is required")

        supplier = team.suppliers.filter(pk=supplier_id).first()
        if not supplier:
            raise NotFound("Your team has no matching supplier")
        return supplier

    def create(self, request):
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except ValueError as e:
            return Response(
                {"error": "Invalid data provided.", "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            item = serializer.save(supplier=self.get_supplier(), team=request.team)
        except IntegrityError as e:
            if "unique_item_number_per_team_and_supplier" in str(e):
                return self.single_error_response(
                    detail="Item number already exists for this supplier.",
                    attr="itemNumber",
                    status=status.HTTP_409_CONFLICT,
                )
            else:
                # shouldn't happen but the constraint name changed once already
                # so let's have this here so at least we return something that makes sense to the FE
                return self.single_error_response(
                    detail=f"There was an unique constraint violation: {e}",
                    attr="root",
                    status=status.HTTP_409_CONFLICT,
                )
        serializer = self.serializer_class(instance=item)
        return Response(serializer.data)

    # Override the partial_update so we can get archived items
    def partial_update(self, request, *args, **kwargs):
        item_id = kwargs.get("uuid")
        item = get_object_or_404(super().get_queryset(), id=item_id)
        item_serializer = ItemSerializer(item, data=request.data, partial=True)

        if item_serializer.is_valid():
            item_serializer.save()
            return Response(data=item_serializer.data, status=status.HTTP_200_OK)
        else:
            logger.error(
                f"Tried to partially update but the serialized object is invalid (uuid: {item_id})"
            )
            return Response(item_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, uuid=None):
        """
        Update an item.
        """
        item = get_object_or_404(
            super().get_queryset(), pk=uuid, team=self.request.team
        )
        serializer = self.serializer_class(
            instance=item, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)

        if "supplier_id" in request.data:
            supplier_id = request.data["supplier_id"]
            if not Supplier.objects.filter(pk=supplier_id, team=item.team).exists():
                return Response(
                    {"error": "Supplier does not belong to this team."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        # The serializer doesn't work with tags, so we add those separately, remove
        # from the update dict, and then save the other fields normally
        try:
            tag_update_success = handle_tag_update_from_request(item, request)
            if not tag_update_success:
                return self.single_error_response(
                    detail="Tag does not exist for this team.",
                    attr="tags",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if "thumbnail" in request.data:
                fileobj = request.data["thumbnail"]
                if not fileobj:
                    raise ValidationError(
                        "There was no file sent as part of the update call."
                    )
                item.thumbnail = fileobj
                item.save(update_fields=["thumbnail"])
                del request.data["thumbnail"]
            item.__dict__.update(**request.data)
            item.save(update_fields=request.data.keys())
        except IntegrityError as e:
            if "unique_item_number_per_team_supplier" in str(e):
                return self.single_error_response(
                    detail="Item number already exists for this supplier.",
                    attr="itemNumber",
                    status=status.HTTP_409_CONFLICT,
                )
        except ValueError as e:
            return Response(
                {"error": "Invalid data provided.", "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Serialize and return the updated item
        serializer = self.serializer_class(instance=item)
        return Response(serializer.data)

    def destroy(self, request, uuid=None):
        try:
            item = get_object_or_404(super().get_queryset(), pk=uuid)
            force = request.query_params.get("force", "").lower() == "true"

            if force:
                # Hard delete: permanently remove item and all related data
                item.delete()
                return Response(status=204)
            else:
                # Soft delete: archive the item (existing behavior)
                if not item.archived_at:
                    item.archived_at = timezone.now()
                    item.save(update_fields=["archived_at"])
                    return Response(
                        {"message": f"Item {item.id} archived successfully"},
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "message": f"Item {item.id} was previously archived at {item.archived_at}"
                        },
                        status=status.HTTP_200_OK,
                    )
        except Exception as e:
            logger.error(f"Error deleting item {uuid}: {str(e)}", exc_info=True)
            raise


class ItemsCountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        queryset = Item.objects.filter(team=self.request.team)
        return Response({"count": queryset.count()})


class ItemFieldsDisplayConfigView(APIView, TeamOwnerFilteredModelView):
    serializer_class = ItemFieldsDisplayConfigSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = "id"
    lookup_url_kwarg = "uuid"
    model = ItemFieldsDisplayConfig

    def get_object(self):
        return get_object_or_404(super().get_queryset())

    def retrieve(self, request):
        """
        Get the ItemFieldsDisplayConfigView for the requestor's team
        """
        try:
            item_config = get_object_or_404(
                ItemFieldsDisplayConfig, team=self.request.team
            )
            serializer = self.serializer_class(item_config)
            return Response(serializer.data)
        except ValidationError as _:
            return Response(
                {"error": "Resource not found or access denied."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def update(self, request):
        """
        Update the ItemFieldsDisplayConfigView for the requestor's team
        """
        instance = self.get_object()
        serializer = self.serializer_class(instance, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ERPItemViewSet(APIView, viewsets.ModelViewSet):
    serializer_class = ERPItemSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = "id"
    lookup_url_kwarg = "uuid"

    def get_object(self):
        return get_object_or_404(ERPItem, item=self.request.team)

    def bulk_import(self, request):
        """
        Kick off bulk import for ERP Items.
        We first get the app from the request, then we get the relevant TeamIntegratedAppCredential
        for that app. We then get the relevant TeamIntegratedAppCredential.credential_id. Using that,
        we get all Alloy items for that credential_id. For each of those, we create a Didero Item.
        For each of those, we link / create a corresponding ERPItem.
        """
        bulk_import_erp_items.delay(
            app=request.query_params["app"], team_id=self.request.team.id
        )
        return Response(status=status.HTTP_200_OK)


class ERPItemCountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if "app" not in request.query_params:
            return Response({"error": "app is required"}, status=400)

        erp_app = request.query_params["app"]
        alloy_credential = TeamIntegratedAppCredential.objects.get(
            team=request.team, app_name=erp_app
        )

        res = get_alloy_item_count(alloy_credential.credential_id)
        return Response(res)


# This view doesn't create nor modify Item objects.
# It only provides suggestions for Item fields using AI.
# The view is purely to expose the endpoint to trigger the AI workflow.
class ItemAIImportView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AIItemSerializer

    def post(self, request):
        url = request.data.get("url")
        if not url:
            return Response(
                {"error": "URL is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        item_details = extract_item_details_from_url(url, team=request.team)
        if item_details is None:
            return self.single_error_response(
                detail="Could not extract Item Details for URL",
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(item_details)
        return Response(serializer.data, status=status.HTTP_200_OK)
