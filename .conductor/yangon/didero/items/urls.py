from django.urls import path
from rest_framework.routers import DefaultRouter

from didero.documents.views import DocumentsViewset
from didero.items.views import (
    ERPItemCountView,
    ERPItemViewSet,
    ItemAIImportView,
    ItemFieldsDisplayConfigView,
    ItemsCountView,
    ItemsViewset,
)

router = DefaultRouter()

urlpatterns = [
    path(
        "api/items",
        ItemsViewset.as_view({"post": "create", "get": "list"}),
        name="items-list",
    ),
    path(
        "api/items/ai-import",
        ItemAIImportView.as_view(),
        name="item-ai-create",
    ),
    path(
        "api/items/count",
        ItemsCountView.as_view(),
        name="items-count",
    ),
    path(
        "api/items/<str:uuid>",
        ItemsViewset.as_view(
            {
                "get": "retrieve",
                "delete": "destroy",
                "put": "update",
                "patch": "partial_update",
            }
        ),
        name="items-detail",
    ),
    path(
        # named the string item_uuid so it's clear whose uuid we're working with on the DocumentsViewset
        "api/items/<str:item_uuid>/docs",
        DocumentsViewset.as_view({"get": "list", "post": "create"}),
        name="item-docs",
    ),
    path(
        "api/item-fields-display-configs",
        ItemFieldsDisplayConfigView.as_view({"get": "retrieve", "put": "update"}),
        name="item-fields-display-config-detail",
    ),
    path(
        "api/erpitems",
        ERPItemViewSet.as_view(
            {
                "get": "retrieve",
            }
        ),
        name="erp-item-detail",
    ),
    path(
        "api/erpitems/bulk-import",
        ERPItemViewSet.as_view({"post": "bulk_import"}),
        name="erp-item-bulk-import",
    ),
    path(
        "api/erpitems/count",
        ERPItemCountView.as_view(),
        name="erp-item-count",
    ),
]
