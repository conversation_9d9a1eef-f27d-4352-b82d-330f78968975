import os
import uuid

from auditlog.registry import auditlog
from django.db.models import (
    CASCADE,
    BooleanField,
    CharField,
    DateTimeField,
    FloatField,
    ForeignKey,
    ImageField,
    Index,
    JSONField,
    Q,
    TextField,
    UniqueConstraint,
    UUIDField,
)
from djmoney.models.fields import Decimal, MoneyField
from djmoney.models.validators import MinMoneyValidator
from taggit.managers import TaggableManager

from didero.common.models import TaggedUUIDItem
from didero.integrations.schemas import ERP_APPS, ERPItemSyncStatus, IntegrationProvider
from didero.items.schemas import WeightUnit
from didero.models import BaseModel, BaseModelWithWebsocketPublishing
from didero.suppliers.models import Supplier
from didero.users.models.team_models import Team


def get_thumbnail_upload_path(instance, filename):
    team_id = instance.team.uuid
    ext = os.path.splitext(filename)[1]
    new_filename = f"{instance.id}{ext}"
    return os.path.join(f"{team_id}/item_thumbnails", new_filename)


class Item(BaseModelWithWebsocketPublishing):
    """
    Making model changes? Make sure you update the ItemFieldsDisplayConfig below as well! They must be kept in sync.
    """

    # Confusingly: TaggedItem is the generic intermediary, that is used for all
    # models that need tags. It is not specific to the Item model.
    tags = TaggableManager(through=TaggedUUIDItem, blank=True)

    id = UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    team = ForeignKey(Team, on_delete=CASCADE)
    supplier = ForeignKey(
        Supplier, on_delete=CASCADE, null=True, blank=True, related_name="items"
    )
    archived_at = DateTimeField(null=True, default=None, db_index=True, blank=True)

    # These fields are mandatory
    description = TextField()
    item_number = CharField(max_length=30)
    price = MoneyField(
        max_digits=16,
        decimal_places=8,
        default_currency="USD",
        default=Decimal("0.00000000"),
        validators=[MinMoneyValidator(Decimal(0.00000000))],
    )
    unit_of_measure = CharField(
        max_length=30, default="Each"
    )  # e.g. "case". There are too many choices to have a strict list
    units_per_measure = FloatField(default=1)  # e.g. 5

    """
    These fields are optional: team admins can choose to enable them or not. But the full
    Item model will have them. So if an admin doesn't enable them, they will just be null
    in the table. If an admin enables them, users populate data, and then the admin disables
    them, we will retain the data.
    The admin config simply determines what the UI shows to the user.
    """
    color = CharField(max_length=30, null=True, blank=True)
    dimensions = CharField(max_length=50, null=True, blank=True)
    hs_code = CharField(
        max_length=20, null=True, blank=True
    )  # HS codes can be up to 10 digits
    item_sku = CharField(max_length=30, null=True, blank=True)
    manufacturer_part_number = CharField(max_length=30, null=True, blank=True)
    notes = TextField(null=True, blank=True)
    supplier_part_number = CharField(max_length=30, null=True, blank=True)
    thumbnail = ImageField(upload_to=get_thumbnail_upload_path, null=True, blank=True)
    unspsc = CharField(max_length=16, null=True, blank=True)  # UNSPSC code is 8 digits
    upc = CharField(max_length=25, null=True, blank=True)  # UPCs are 12 digits long

    weight_unit = CharField(
        max_length=20,
        null=True,
        blank=True,
        choices=[(weight_unit.value, weight_unit.name) for weight_unit in WeightUnit],
    )
    weight_value = FloatField(null=True, blank=True)
    # Lead times will be in days
    production_lead_time = FloatField(null=True, blank=True)
    transit_lead_time = FloatField(null=True, blank=True)
    total_lead_time = FloatField(null=True, blank=True)

    class Meta:
        # index for all fields we want to filter by
        indexes = [
            Index(fields=["supplier"]),
            Index(fields=["description"]),
            Index(fields=["item_number"]),
            Index(fields=["item_sku"]),
            # Any time we GET items, we check both the team and if the item is archived.
            Index(fields=["team", "archived_at"]),
            Index(fields=["created_at"]),  # efficient ordering by created_at
        ]
        # add default ordering to avoid pagination issues
        ordering = ["created_at"]

        # The combination of team+supplier+item_number has to be unique
        # However, suppliers can be null. In that case, don't apply the constraint - allow duplicate
        # item numbers.
        constraints = [
            UniqueConstraint(
                fields=["team", "supplier", "item_number"],
                name="unique_item_number_per_team_and_supplier",
                condition=Q(supplier__isnull=False),
            )
        ]

    def __str__(self):
        return self.description

    # function to check if the item is archived or belongs to an archived supplier
    def is_archived(self) -> bool:
        return self.archived_at is not None or (
            self.supplier and self.supplier.archived_at is not None
        )


auditlog.register(Item)


"""
This model is a config that keeps track of which item fields we will display
on the UI, both for reading and writing.
Any time item fields are changed, they should be updated here as well.
"""


class ItemFieldsDisplayConfig(BaseModelWithWebsocketPublishing):
    """
    This is a config that keeps track of which item fields we will display
    on the UI, both for reading and writing.
    Any time item fields are changed, they should be updated here as well.
    This does not mean which fields are in the Item model - all of these fields
    will be in the Item model, they just may be null unless the admin toggles them on.
    """

    team = ForeignKey(Team, on_delete=CASCADE, unique=True, related_name="item_config")
    color = BooleanField(default=False)
    dimensions = BooleanField(default=False)
    hs_code = BooleanField(default=False)
    item_sku = BooleanField(default=False)
    manufacturer_part_number = BooleanField(default=False)
    notes = BooleanField(default=False)
    supplier_part_number = BooleanField(default=False)
    thumbnail = BooleanField(default=False)
    unit_of_measure = BooleanField(default=False)
    units_per_measure = BooleanField(default=False)
    unspsc = BooleanField(default=False)
    upc = BooleanField(default=False)
    weight_unit = BooleanField(default=False)
    weight_value = BooleanField(default=False)
    production_lead_time = BooleanField(default=False)
    transit_lead_time = BooleanField(default=False)
    total_lead_time = BooleanField(default=False)

    def __str__(self):
        return f"{self.team.name} Item Config"


auditlog.register(ItemFieldsDisplayConfig)


class ERPItem(BaseModel):
    """
    Keep track of items imported from ERPs. They will be mapped to Didero Items
    via the SupplierItem model.
    """

    item = ForeignKey(
        "items.Item",
        on_delete=CASCADE,
        related_name="erp_items",
    )
    erp_provider = CharField(
        choices=[(provider.value, provider.name) for provider in IntegrationProvider],
    )
    erp_app = CharField(
        choices=[(app.value, app.name) for app in ERP_APPS],
        max_length=64,
    )
    ipaas_item_id = CharField(max_length=64)
    remote_item_id = CharField(max_length=64)

    class Meta:
        indexes = [
            Index(fields=["item"]),
            Index(fields=["ipaas_item_id"]),
        ]


class ERPItemStatusImportProgress(BaseModel):
    """
    Keep track of the status of the sync between the items in the ERP, and Didero.
    This is an overall status: it should track all items (bulk import).
    """

    team = ForeignKey(
        "users.Team",
        on_delete=CASCADE,
        related_name="erp_item_status_import_progress",
    )
    integration = CharField(
        choices=[(provider.value, provider.name) for provider in IntegrationProvider],
    )
    app = CharField(
        choices=[(app.value, app.name) for app in ERP_APPS],
        max_length=64,
    )
    last_synced_at = DateTimeField(null=True, default=None, blank=True)
    sync_status = CharField(
        choices=[(status.value, status.name) for status in ERPItemSyncStatus],
        max_length=64,
    )

    class Meta:
        indexes = [
            Index(fields=["team"]),
            Index(fields=["integration"]),
        ]


class ItemCustomField(BaseModelWithWebsocketPublishing):
    item = ForeignKey(Item, on_delete=CASCADE, related_name="custom_fields")
    # JSON map of all the extra fields
    custom_fields = JSONField()

    class Meta:
        indexes = [
            Index(fields=["item"]),
            # you can index JSON and it performs pretty well in postgres (Brien did some research :bless:):
            # https://www.postgresql.org/docs/current/datatype-json.html#JSON-INDEXING
            Index(fields=["custom_fields"]),
            Index(fields=["item", "custom_fields"]),
        ]

        constraints = [
            UniqueConstraint(
                fields=["item"],
                name="unique_per_item",
            )
        ]
