from decimal import Decimal

from rest_framework import serializers

from didero.common.serializers import (
    CustomTaggitSerializer,
    CustomTagListSerializerField,
)
from didero.items.models import ERPItem, Item, ItemFieldsDisplayConfig
from didero.suppliers.serializers import BasicSupplierSerializer


class ItemSerializer(CustomTaggitSerializer, serializers.ModelSerializer):
    supplier = BasicSupplierSerializer(read_only=True)
    tags = CustomTagListSerializerField(required=False)
    thumbnail = serializers.ImageField(required=False)

    class Meta:
        model = Item
        read_only_fields = ("id", "team", "supplier")
        fields = read_only_fields + (
            "tags",
            "description",
            "item_number",
            "price",
            "price_currency",
            "notes",
            "item_sku",
            "manufacturer_part_number",
            "supplier_part_number",
            "upc",
            "unspsc",
            "hs_code",
            "color",
            "weight_value",
            "weight_unit",
            "dimensions",
            "production_lead_time",
            "transit_lead_time",
            "total_lead_time",
            "unit_of_measure",
            "units_per_measure",
            "thumbnail",
            "archived_at",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["price"] = float(Decimal(instance.price.amount).normalize())
        return representation


class ItemFieldsDisplayConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = ItemFieldsDisplayConfig
        read_only_fields = ("id", "team")
        fields = read_only_fields + (
            "notes",
            "item_sku",
            "manufacturer_part_number",
            "supplier_part_number",
            "upc",
            "unspsc",
            "hs_code",
            "color",
            "weight_value",
            "weight_unit",
            "dimensions",
            "production_lead_time",
            "transit_lead_time",
            "total_lead_time",
            "unit_of_measure",
            "units_per_measure",
            "thumbnail",
        )


class ERPItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ERPItem
        read_only_fields = (
            "id",
            "item",
            "erp_provider",
            "erp_app",
            "ipaas_item_id",
            "remote_item_id",
        )
        fields = read_only_fields + (
            "item",
            "erp_provider",
            "erp_app",
            "ipaas_item_id",
            "remote_item_id",
        )


class AIItemSerializer(serializers.Serializer):
    supplier = BasicSupplierSerializer()
    item_description = serializers.CharField()
    price = serializers.IntegerField()
