from typing import TYPE_CHECKING

from django.contrib import admin

from didero.items.models import (
    ERPItem,
    ERPItemStatusImportProgress,
    Item,
    ItemCustomField,
    ItemFieldsDisplayConfig,
)

if TYPE_CHECKING:
    TypedItemAdmin = admin.ModelAdmin[Item]
    TypedItemCustomFieldAdmin = admin.ModelAdmin[ItemCustomField]
    TypedItemFieldsDisplayConfigAdmin = admin.ModelAdmin[ItemFieldsDisplayConfig]
    TypedERPItemAdmin = admin.ModelAdmin[ERPItem]
    TypedERPItemStatusImportProgressAdmin = admin.ModelAdmin[
        ERPItemStatusImportProgress
    ]
else:
    TypedItemAdmin = admin.ModelAdmin
    TypedItemCustomFieldAdmin = admin.ModelAdmin
    TypedItemFieldsDisplayConfigAdmin = admin.ModelAdmin
    TypedERPItemAdmin = admin.ModelAdmin
    TypedERPItemStatusImportProgressAdmin = admin.ModelAdmin


class ItemAdmin(TypedItemAdmin):
    list_display = (
        "description",
        "item_number",
        "team",
        "supplier",
        "archived_at",
    )
    search_fields = ("description", "item_number", "supplier__name", "team__name")
    list_filter = ("supplier", "team")
    autocomplete_fields = ["supplier", "team"]
    list_per_page = 100
    list_select_related = ["supplier", "team"]


admin.site.register(Item, ItemAdmin)


class ItemCustomFieldAdmin(TypedItemCustomFieldAdmin):
    list_display = (
        "item",
        "custom_fields",
    )
    search_fields = ("item__description", "item__item_number")
    list_filter = (
        "item__team",
    )  # Filter by team instead of item for better performance
    autocomplete_fields = ["item"]
    list_per_page = 100
    list_select_related = ["item", "item__team", "item__supplier"]


admin.site.register(ItemCustomField, ItemCustomFieldAdmin)


# add help text to the admin
class ItemFieldsDisplayConfigAdmin(TypedItemFieldsDisplayConfigAdmin):
    list_display = (
        "team",
        "notes",
        "item_sku",
        "manufacturer_part_number",
        "supplier_part_number",
        "upc",
        "unspsc",
        "hs_code",
        "color",
        "weight_value",
        "weight_unit",
        "dimensions",
        "production_lead_time",
        "transit_lead_time",
        "total_lead_time",
        "thumbnail",
    )
    search_fields = ("team__name",)
    list_filter = ("team",)
    autocomplete_fields = ["team"]
    list_per_page = 100
    list_select_related = ["team"]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "team",
                    "notes",
                    "item_sku",
                    "manufacturer_part_number",
                    "supplier_part_number",
                    "upc",
                    "unspsc",
                    "hs_code",
                    "color",
                    "weight_value",
                    "weight_unit",
                    "dimensions",
                    "production_lead_time",
                    "transit_lead_time",
                    "total_lead_time",
                    "thumbnail",
                ),
                "description": "This section allows you to manage item configurations, including SKUs, part numbers, and other detailed attributes.",
            },
        ),
    )


admin.site.register(ItemFieldsDisplayConfig, ItemFieldsDisplayConfigAdmin)


class ERPItemAdmin(TypedERPItemAdmin):
    list_display = (
        "item",
        "erp_provider",
        "erp_app",
        "ipaas_item_id",
        "remote_item_id",
    )
    search_fields = ("item__description", "item__item_number")
    autocomplete_fields = ["item"]
    list_per_page = 100
    list_select_related = ["item", "item__team", "item__supplier"]


admin.site.register(ERPItem, ERPItemAdmin)


class ERPItemStatusImportProgressAdmin(TypedERPItemStatusImportProgressAdmin):
    list_display = (
        "team",
        "integration",
        "app",
        "last_synced_at",
        "sync_status",
    )
    search_fields = ("team__name", "integration", "app")
    autocomplete_fields = ["team"]
    list_per_page = 100
    list_select_related = ["team"]


admin.site.register(ERPItemStatusImportProgress, ERPItemStatusImportProgressAdmin)
