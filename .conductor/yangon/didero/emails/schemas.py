from enum import Enum, StrEnum
from typing import List

from pydantic import BaseModel, Field


class NylasGrantStatus(str, Enum):
    VALID = "valid"
    INVALID = "invalid"


# High-level email domain categories for the waterfall approach
class HighLevelEmailCategory(StrEnum):
    PO_RELATED = "PO_RELATED"
    QUOTATION_RELATED = "QUOTATION_RELATED"
    INVOICE_RELATED = "INVOICE_RELATED"
    SHIPPING_DOCUMENT_RELATED = "SHIPPING_DOCUMENT_RELATED"
    OTHER = "OTHER"


# Descriptions for high-level email categories
class HighLevelEmailCategoryDescription(StrEnum):
    PO_RELATED = """Any email related to purchase orders (new orders, order acknowledgments, shipment notifications, delivery confirmations)"""
    QUOTATION_RELATED = """Emails containing price quotes, estimates, or proposals for products/services"""
    INVOICE_RELATED = (
        """Emails containing invoices, payment requests, or billing information"""
    )
    SHIPPING_DOCUMENT_RELATED = """Emails containing shipping documents like Bill of Lading, Packing Lists, Advance Shipping Notices, Goods Receipt Notes, or Delivery Notes"""
    OTHER = """Emails that don't fit into any of the above categories"""


# When adding a new category, make sure to update the EmailCategoryDescription enum
class EmailCategory(StrEnum):
    QUOTATION = "QUOTATION"
    PURCHASE_ORDER = "PURCHASE_ORDER"
    PURCHASE_ORDER_ACKNOWLEDGEMENT = "PURCHASE_ORDER_ACKNOWLEDGEMENT"
    ORDER_SHIPPED = "ORDER_SHIPPED"
    ORDER_RECEIVED = "ORDER_RECEIVED"
    INVOICE = "INVOICE"
    BILL_OF_LADING = "BILL_OF_LADING"
    PACKING_LIST = "PACKING_LIST"
    ADVANCE_SHIPPING_NOTICE = "ADVANCE_SHIPPING_NOTICE"
    GOODS_RECEIPT_NOTE = "GOODS_RECEIPT_NOTE"
    DELIVERY_NOTE = "DELIVERY_NOTE"
    SHIPPING_DOCUMENT = "SHIPPING_DOCUMENT"  # Generic shipping document
    OTHER = "OTHER"


# TODO: Add more keywords for each category
class EmailCategoryDescription(StrEnum):
    QUOTATION = """This category is used for emails containing a quotation from a supplier. 
    These emails typically provide pricing, terms, and details about requested goods or services.

    **What the email might contain:**
    - **Product Details:** Item name, item number, units of measurement, product specifications, certifications (e.g., ISO 9001).
    - **Pricing Information:** Price per unit, bulk discounts, currency, and validity period of the quote.
    - **Payment Terms:** Accepted payment methods and conditions (e.g., Net 30, 50% upfront).
    - **Lead Times:** Estimated production and dispatch time.
    - **Shipment Details:** Shipping method, estimated duration, and freight terms (FOB, CIF).
    - **Supplier Contact Information:** Name, email, phone number, and company details.
    - **Additional Conditions:** Warranty, return policies, or minimum order quantities (MOQ).

    **Example content keywords (not exhaustive, but a guidance):** 
    "Price per unit," "Quotation valid until," "Bulk discount," "MOQ requirement."
    """

    PURCHASE_ORDER = """This category is used for emails that contain a purchase order (PO), 
    which is a formal request to procure goods or services.

    **What the email might contain:**
    - **Order Details:** PO number, item name(s), item number(s), ordered quantity, unit price, total price.
    - **Buyer Information:** Company name, contact name, and delivery or billing address.
    - **Payment Instructions:** Reference to agreed payment terms (e.g., "Net 30 days").
    - **Shipping Instructions:** Requested delivery date, shipping method, and freight terms.

    **Example content keywords (not exhaustive, but a guidance):** 
    "Purchase Order #," "Please process this order," "Net payment terms."
    """

    PURCHASE_ORDER_ACKNOWLEDGEMENT = """This category is used for emails confirming that a purchase order has been received and accepted by the supplier.

    **What the email might contain:**
    - **Reference to the Purchase Order (PO) Number.**
    - **Acknowledgment of the received order (Sales Order Confirmation).**
    - **Confirmation of Item(s), Quantity, and Agreed Price.**
    - **Additional Notes:** Changes in availability, revised terms, or order adjustments.
    - **Reference to future updates (e.g., tracking information to follow).**

    **Example content keywords (not exhaustive, but a guidance):** 
    "Thank you for your order," "Attached is our acknowledgment," "Sales order confirmation," "PO Confirmation,"
    """

    ORDER_SHIPPED = """This category is used for emails notifying that an order has been shipped.

    **What the email might contain:**
    - **Order Details:** PO number, item name(s), item number(s), quantities shipped.
    - **Tracking Number & Estimated Time of Arrival (ETA).**
    - **Freight Details (FOB, CIF, etc.).**

    **Example content keywords (not exhaustive, but a guidance):** 
    "Your order has shipped," "Tracking number," "Expected delivery date,"
    """

    ORDER_RECEIVED = """This category is used for emails confirming that the order has been received by the buyer.

    **What the email might contain:**
    - **Reference to PO Number & Items Received.**
    - **Quantity Checked & Verified.**
    - **Delivery Confirmation or Inspection Status.**
    - **Bill of Lading Details.**

    **Example content keywords (not exhaustive, but a guidance):** 
    "Goods received," "Delivery confirmation," "Received in good condition," "Bill of landing attached."
    """

    INVOICE = """This category is used for emails containing an invoice or billing request from the supplier.

    **What the email might contain:**
    - **Invoice Number & Date.**
    - **Reference to PO Number & Items Billed.**
    - **Total Amount Due & Payment Terms.**
    - **Due Date & Payment Instructions.**
    - **Tax Details (VAT, GST, etc.).**
    - **Reference to account or remittance address.**
    - **Option for digital invoicing or file import (if applicable).**

    **Example content keywords (not exhaustive, but a guidance):** 
    "Invoice #," "Amount due," "Attached are your invoices," "Billing statement," "Remit payments to," "Account number," "Payment instructions."
    """

    BILL_OF_LADING = """This category is used for emails containing a Bill of Lading (BOL) document.

    **What the email might contain:**
    - **BOL Number:** Unique identifier for the bill of lading
    - **Vessel/Container Information:** Ship name, voyage number, container numbers
    - **Port Details:** Port of loading, port of discharge
    - **Cargo Details:** Description of goods, quantities, weights
    - **Consignee Information:** Recipient details
    - **Shipping Dates:** Loading date, estimated arrival

    **Example content keywords (not exhaustive, but a guidance):** 
    "Bill of Lading," "BOL#," "Container," "Vessel," "Port," "Consignee"
    """

    PACKING_LIST = """This category is used for emails containing a Packing List document.

    **What the email might contain:**
    - **Package Details:** Number of packages, dimensions, weights
    - **Item Breakdown:** Individual items packed, quantities per package
    - **Packaging Method:** Boxes, pallets, crates, etc.
    - **Shipping Information:** Total weight, volume, handling instructions

    **Example content keywords (not exhaustive, but a guidance):** 
    "Packing List," "Package," "Carton," "Pallet," "Gross Weight," "Net Weight"
    """

    ADVANCE_SHIPPING_NOTICE = """This category is used for emails containing an Advance Shipping Notice (ASN).

    **What the email might contain:**
    - **Shipment Details:** Ship date, estimated arrival, tracking number
    - **Carrier Information:** Shipping company, contact details
    - **Route Information:** Origin, destination, transit details
    - **Item Information:** What's being shipped, quantities

    **Example content keywords (not exhaustive, but a guidance):** 
    "Advance Shipping Notice," "ASN," "Ship Date," "ETA," "Carrier," "Tracking"
    """

    GOODS_RECEIPT_NOTE = """This category is used for emails containing a Goods Receipt Note (GRN).

    **What the email might contain:**
    - **Receipt Details:** GRN number, receipt date, location
    - **Inspection Information:** Quality check results, condition notes
    - **Quantity Verification:** Received vs ordered quantities
    - **Storage Information:** Where goods are stored

    **Example content keywords (not exhaustive, but a guidance):** 
    "Goods Receipt," "GRN," "Received," "Inspection," "Quality," "Storage"
    """

    DELIVERY_NOTE = """This category is used for emails containing a Delivery Note document.

    **What the email might contain:**
    - **Delivery Details:** Delivery date, time, address
    - **Recipient Information:** Who received the goods, signature
    - **Delivery Instructions:** Special handling, access requirements
    - **Proof of Delivery:** POD number, confirmation details

    **Example content keywords (not exhaustive, but a guidance):** 
    "Delivery Note," "Delivered," "POD," "Recipient," "Signature," "Delivery Address"
    """

    SHIPPING_DOCUMENT = """This category is used for emails containing generic shipping documents that don't fit specific types.

    **What the email might contain:**
    - Any shipping-related documentation
    - Transport documents, customs papers
    - Shipping confirmations or receipts

    **Example content keywords (not exhaustive, but a guidance):** 
    "Shipping Document," "Transport," "Shipment," "Cargo," "Freight"
    """

    OTHER = """This category is used for emails that do not fit into any of the predefined procurement-related categories.

    **What the email might contain:**
    - General inquiries, promotional emails, internal communications, or vendor updates.

    **Example content keywords (not exhaustive, but a guidance):** 
    Any content that does not match procurement-related transactions.
    """


class ExtractedPurchaseOrder(BaseModel):
    """Individual purchase order extracted from email."""

    po_number: str = Field(description="The purchase order number in format PO-XXXXXXX")
    confidence: float = Field(
        description="Confidence score for this extraction (0-100)", ge=0, le=100
    )


class PurchaseOrderExtractionResponse(BaseModel):
    """Response model for LLM-based purchase order extraction from emails."""

    purchase_orders: List[ExtractedPurchaseOrder] = Field(
        description="List of purchase order numbers found in the email",
        default_factory=list,
    )
    reasoning: str = Field(
        description="Brief explanation of how the PO numbers were identified",
        default="",
    )
