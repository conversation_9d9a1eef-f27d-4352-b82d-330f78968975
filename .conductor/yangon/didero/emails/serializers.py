from django.contrib.contenttypes.models import ContentType
from rest_framework import serializers

from didero.documents.models import Document, DocumentLink
from didero.emails.models import EmailCredentialNylas, EmailTemplate
from didero.serializers import PoppableModelSzlr


class AttachmentSummarySerializer(PoppableModelSzlr):
    """Simplified serializer for email attachments that returns only essential fields."""

    id = serializers.CharField(source="uuid")
    size = serializers.IntegerField(source="filesize_bytes")

    class Meta:
        model = Document
        fields = ("id", "name", "size", "content_type")
        read_only_fields = fields


class EmailCredentialNylasSerializer(PoppableModelSzlr):
    class Meta:
        model = EmailCredentialNylas
        fields = (
            "id",
            "user_id",
            "email_address",
            "provider",
            "status",
            "expires_at",
        )
        read_only_fields = fields


class EmailTemplateSerializer(PoppableModelSzlr):
    attachments = serializers.SerializerMethodField()

    class Meta:
        model = EmailTemplate
        fields = (
            "id",
            "team",
            "supplier",
            "template_name",
            "email_type",
            "email_to",
            "email_cc",
            "email_bcc",
            "subject",
            "body",
            "attachments",
            "created_at",
            "modified_at",
        )
        read_only_fields = (
            "id",
            "created_at",
            "modified_at",
            "attachments",
        )

    def get_attachments(self, obj):
        content_type = ContentType.objects.get_for_model(EmailTemplate)
        document_links = DocumentLink.objects.filter(
            parent_object_type=content_type, parent_object_id=obj.id
        ).select_related("document")

        # Use AttachmentSummarySerializer for consistent attachment structure
        documents = [link.document for link in document_links]
        return AttachmentSummarySerializer(documents, many=True).data
