from django.urls import path
from rest_framework.routers import DefaultRout<PERSON>

from didero.emails.views import (
    CompleteNylasOAuthView,
    CreateEmailTemplateView,
    DeleteEmailTemplateView,
    EmailCredentialViewset,
    EmailDraftViewSet,
    EmailSendSuggestionView,
    EmailThreadViewSet,
    GetEmailTemplateByIdView,
    GetEmailTemplatesView,
    GetTemplateVariablesView,
    RenderEmailTemplateView,
    SendEmailNylasView,
    UpdateEmailTemplateView,
)

router = DefaultRouter()
router.register(r"api/imap", EmailCredentialViewset, basename="imap-creds")
router.register(
    r"api/emails/connections",
    EmailCredentialViewset,
    basename="email-creds",
)
router.register(r"api/emails/drafts", EmailDraftViewSet, basename="email-drafts")

# We typically want no trailing slashes.
# However, we wrote the ones above (and had the FE call those endpoints) with trailing slashes,
# and for now are not going to revert those. In general / for future ones,
# we should use the router below - no trailing slash
no_trailing_slash_router = DefaultRouter(trailing_slash=False)
no_trailing_slash_router.register(
    r"api/emailthreads", EmailThreadViewSet, basename="email-threads"
)

urlpatterns = (
    router.urls
    + no_trailing_slash_router.urls
    + [
        path(
            "api/emails/send",
            SendEmailNylasView.as_view(),
            name="email-send",
        ),
        path(
            "api/email-threads/<str:thread_id>",
            EmailThreadViewSet.as_view({"get": "retrieve", "post": "link_po"}),
            name="email-thread-details",
        ),
        path(
            "api/emails/suggestions",
            EmailSendSuggestionView.as_view(),
            name="email-send-suggestions",
        ),
        path(
            "api/emails/nylas/oauth",
            CompleteNylasOAuthView.as_view(),
            name="nylas-oauth",
        ),
        path(
            "api/emails/templates/",
            GetEmailTemplatesView.as_view(),
            name="get-email-templates",
        ),
        path(
            "api/emails/templates/create",
            CreateEmailTemplateView.as_view(),
            name="create-email-template",
        ),
        path(
            "api/emails/templates/<int:template_id>",
            GetEmailTemplateByIdView.as_view(),
            name="get-email-template-by-id",
        ),
        path(
            "api/emails/templates/<int:template_id>/update",
            UpdateEmailTemplateView.as_view(),
            name="update-email-template",
        ),
        path(
            "api/emails/templates/<int:template_id>/delete",
            DeleteEmailTemplateView.as_view(),
            name="delete-email-template",
        ),
        path(
            "api/emails/templates/render",
            RenderEmailTemplateView.as_view(),
            name="render-email-template",
        ),
        path(
            "api/emails/template-variables",
            GetTemplateVariablesView.as_view(),
            name="get-template-variables",
        ),
    ]
)
