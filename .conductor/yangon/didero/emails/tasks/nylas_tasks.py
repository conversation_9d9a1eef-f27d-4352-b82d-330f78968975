import hashlib
import time
from datetime import datetime
from typing import Union

import structlog
from celery import shared_task
from celery_singleton import Singleton
from django.contrib.admin.options import get_content_type_for_model
from django.core.files.base import ContentFile
from django.db import IntegrityError, transaction
from django.utils import timezone

import didero.emails.utils.nylas as nylas
from didero.documents.models import Document, DocumentLink
from didero.documents.schemas import DocumentType
from didero.emails.models import (
    EmailCredentialNylas,
    EmailThread,
    EmailThreadToPurchaseOrderLink,
)
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import (
    Communication,
    CommunicationEmailRecipient,
    Supplier,
    SupplierContact,
)
from didero.users.models.team_models import Team

FILETYPE_BLOCKLIST = [
    "message/rfc822",  # These are attachments when we get message undeliverable
    "application/ics",
    "text/calendar",
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/webp",
    "image/heic",
]

log = structlog.get_logger(__name__)


def create_hash(*args: Union[str, int]) -> str:
    concatenated_string = "<>".join(map(str, args))
    hash_object = hashlib.sha256()
    hash_object.update(concatenated_string.encode("utf-8"))
    return hash_object.hexdigest()


def discover_and_create_contacts_from_message(
    supplier: Supplier, message: dict, message_direction: Communication
) -> list[SupplierContact]:
    """
    If email is sent to the team, check if sender is a new contact
    If email is sent from the team, check if recipients are from a supplier domain
    and add them to supplier contacts
    """
    if "draft" in [folder.lower() for folder in message.get("folders", [])]:
        return []

    supplier_domains = list(supplier.domains.values_list("domain", flat=True))

    if message_direction == Communication.DIRECTION_INCOMING:
        potential_contacts = message["from"]
    else:
        potential_contacts = message["to"]

    contacts = []
    for contact_data in potential_contacts:
        contact_email = contact_data["email"].lower()
        contact_name = contact_data.get("name", contact_email)

        existing_contact = SupplierContact.objects.filter(
            email=contact_email, supplier=supplier
        ).first()
        if existing_contact:
            contacts.append(existing_contact)
            continue

        contact_domain = contact_email.split("@")[-1]
        if not any([contact_domain.endswith(d) for d in supplier_domains]):
            continue

        contact = SupplierContact.objects.create(
            name=contact_name,
            email=contact_email,
            supplier=supplier,
        )
        contacts.append(contact)

        log.info(
            "Discovered and added new contact from supplier domain",
            contact_id=contact.id,
            contact_name=contact_name,
            contact_email=contact_email,
        )

    return contacts


def create_communication_and_documents(
    cred: EmailCredentialNylas,
    supplier: Supplier,
    message: dict,
    purchase_orders: list[PurchaseOrder] = [],
):
    """
    Main function to create a communication from a nylas message.

    - Parses the message, converts to our format, and associates it with the
      appropriate supplier, team, and purchase order.
    - Downloads attachments and creates a document for each attachment.
    - Finds new contacts and adds them to the communication.
    """
    team = cred.team
    inbox_address = cred.email_address
    from_address = message["from"][0]["email"]
    to_addresses = [addr["email"] for addr in message["to"]]

    if Communication.objects.filter(email_message_id=message["id"]).exists():
        return

    if set(to_addresses).intersection(supplier.email_blocklist) or set(
        [from_address]
    ).intersection(supplier.email_blocklist):
        log.info(
            "Found comm with blocked email, skipping creation",
            message_nylas_id=message["id"],
            credential_id=cred.id,
            team_id=team.id,
            supplier_id=supplier.id,
        )
        return

    direction = (
        Communication.DIRECTION_OUTGOING
        if from_address == inbox_address
        else Communication.DIRECTION_INCOMING
    )

    email_thread, _ = EmailThread.objects.get_or_create(
        thread_id=message["thread_id"], team=team
    )

    for purchase_order in purchase_orders:
        EmailThreadToPurchaseOrderLink.objects.get_or_create(
            email_thread=email_thread, purchase_order=purchase_order
        )

    email_reference_hash = create_hash(
        team.id, from_address, message["subject"], message["body"]
    )

    # Import the new hash generator for canonical body hash
    from didero.emails.ingestion import generate_canonical_body_hash

    canonical_body_hash = generate_canonical_body_hash(message["body"])

    with transaction.atomic():
        try:
            comm = Communication.objects.create(
                team=team,
                supplier=supplier,
                email_credential=cred,
                comm_type=Communication.TYPE_EMAIL,
                email_from=from_address,
                email_message_id=message["id"],
                comm_time=(
                    datetime.fromtimestamp(message["date"], tz=timezone.utc)
                    if "date" in message
                    else datetime.now()
                ),
                direction=direction,
                email_content=message["body"],
                email_reference_hash=email_reference_hash,
                canonical_body_hash=canonical_body_hash,  # Add the new hash
                email_subject=message["subject"],
                email_folder=message["folders"][0] if message["folders"] else "",
                email_thread=email_thread,
            )

        except IntegrityError as e:
            log.warning(
                f"Duplicate message found for {message['id']}, likely due to concurrent ingestion. Skipping.",
                exc=e,
                message_id=message["id"],
                grant_id=message["grant_id"],
            )
            return

        for address in to_addresses:
            CommunicationEmailRecipient(
                email_address=address, communication_to=comm
            ).save()

        cc_addresses = [pair["email"] for pair in message.get("cc", [])]
        for address in cc_addresses:
            CommunicationEmailRecipient(
                email_address=address, communication_cc=comm
            ).save()

        bcc_addresses = [pair["email"] for pair in message.get("bcc", [])]
        for address in bcc_addresses:
            CommunicationEmailRecipient(
                email_address=address, communication_bcc=comm
            ).save()

        # attachments value can be null
        for attachment_meta in message.get("attachments", []) or []:
            # content_type in nylas can look like this: text/calendar; name=invite.ics
            content_type = attachment_meta.get("content_type", "").split(";")[0]
            filename = attachment_meta.get("filename", "")
            filesize_bytes = attachment_meta.get("size", 0)

            if content_type in FILETYPE_BLOCKLIST:
                continue

            attachment_content = nylas.download_attachment(
                message["grant_id"], attachment_meta["id"], message["id"]
            )

            file = ContentFile(attachment_content, name=filename)
            hash_obj = hashlib.sha256()
            hash_obj.update(filename.encode("utf-8"))
            hash_obj.update(file.read())
            file_hash = hash_obj.hexdigest()

            # Team-scoped document deduplication to prevent conflicts when multiple teams
            # upload the same document (e.g., same PDF from a supplier)
            doc = Document.objects.filter(content_hash=file_hash, team=team).first()
            if not doc:
                doc = Document.objects.create(
                    team=team,
                    name=filename,
                    filesize_bytes=filesize_bytes,
                    document=file,
                    content_hash=file_hash,
                    content_type=content_type,
                    upload_date=comm.comm_time,
                    doc_type=DocumentType.PROCESSING,
                )

            # create a document link from the document to the communication
            DocumentLink.objects.create(
                document=doc,
                parent_object=comm,
            )

            for purchase_order in purchase_orders:
                if not DocumentLink.objects.filter(
                    document=doc,
                    parent_object_type=get_content_type_for_model(PurchaseOrder),
                    parent_object_id=purchase_order.id,
                ).exists():
                    DocumentLink.objects.create(
                        document=doc,
                        parent_object=purchase_order,
                    )

    contacts = discover_and_create_contacts_from_message(supplier, message, direction)
    comm.contacts.set(contacts)

    log.info("Created communication and documents", comm_id=comm.id)
    return comm


################################################################################
# Backfill functions below
# Can be run as part of a reconciliatiatory workflow or manually to catch emails up
# if any have been missed, or upon new addition of a supplier/credential/contact.
# Backfills are idempotent, so can be run multiple times without issue.
################################################################################


@shared_task(
    base=Singleton,
    lock_expiry=60 * 5,
    queue="email_backfills",
    key="backfill-domains-%(cred_id)s",
)
def backfill_supplier_emails_from_inbox(
    cred_id: int,
    supplier_id: int,
    supplier_domains: list[str],
    backfill_time_window: int = 365 * 24 * 60 * 60,  # one year
):
    cred = EmailCredentialNylas.objects.get(pk=cred_id)
    supplier = Supplier.objects.get(pk=supplier_id)
    supplier_domains = [domain for domain in supplier_domains if domain]

    log.info(
        "Querying inbox for supplier",
        credential_id=cred_id,
        supplier_id=supplier.id,
    )

    min_time = time.time() - backfill_time_window

    def handle_messages_page(page_token=None):
        messages, next_cursor = nylas.get_messages_from_domains(
            cred.grant_id, cred.provider, supplier_domains, page_token
        )

        log.info(
            f"Found {len(messages)} messages in emails page",
            credential_id=cred_id,
            supplier_id=supplier.id,
            page_token=page_token,
        )

        for message in messages:
            # Stop when we reach messages that were created more than a year ago
            # Nylas doesn't explicitly state that it returns messages in reverse
            # chronological order, but assume that it does based on observation.
            linux_time_created = message["date"]
            if linux_time_created < min_time:
                return None

            create_communication_and_documents(
                cred,
                supplier,
                message,
            )
        return next_cursor

    next_cursor = handle_messages_page()
    while next_cursor:
        next_cursor = handle_messages_page(page_token=next_cursor)


@shared_task(
    base=Singleton,
    lock_expiry=60 * 5,
    queue="email_backfills",
    key="%(cred_id)s",
)
def backfill_supplier_domains(
    team_id: int,
    supplier_id: int,
    supplier_domains: list[str],
    backfill_time_window=365 * 24 * 60 * 60,  # one year
):
    log.info(
        "backfilling on new supplier domains",
        team_id=team_id,
        supplier_id=supplier_id,
        supplier_domains=supplier_domains,
    )

    team = Team.objects.get(pk=team_id)
    supplier = Supplier.objects.get(pk=supplier_id)

    for cred in team.valid_email_credentials:
        backfill_supplier_emails_from_inbox.delay(
            cred.id, supplier.id, supplier_domains, backfill_time_window
        )


@shared_task(
    base=Singleton,
    lock_expiry=60 * 5,
    queue="email_backfills",
    key="%(cred_id)s",
)
def backfill_supplier_contacts(
    team_id: int,
    supplier_id: int,
    contact_emails: list[str],
    backfill_time_window=365 * 24 * 60 * 60,  # one year
):
    log.info(
        "backfilling on new supplier domains",
        team_id=team_id,
        supplier_id=supplier_id,
        contact_emails=contact_emails,
    )

    team = Team.objects.get(pk=team_id)
    supplier = Supplier.objects.get(pk=supplier_id)

    for cred in team.valid_email_credentials:
        backfill_supplier_emails_from_inbox.delay(
            cred.id, supplier.id, contact_emails, backfill_time_window
        )


@shared_task(
    base=Singleton,
    lock_expiry=60 * 5,
    queue="email_backfills",
    key="%(cred_id)s",
)
def backfill_inbox(cred_id: int, backfill_time_window: int = 365 * 24 * 60 * 60):
    cred = EmailCredentialNylas.objects.get(pk=cred_id)

    log.info("backfilling from inbox", credential_id=cred_id)

    # For each supplier, backfill emails and contacts which aren't in their domains
    # stagger requests and avoid rate limits
    for cnt, supplier in enumerate(cred.team.active_suppliers):
        supplier_domains = list(supplier.domains.values_list("domain", flat=True))
        if supplier_domains:
            backfill_supplier_emails_from_inbox.apply_async(
                args=(cred_id, supplier.id, supplier_domains, backfill_time_window),
                countdown=0.5 * cnt,
            )

        contact_emails = [
            email
            for email in list(supplier.contacts.values_list("email", flat=True))
            if email and not any([email.endswith(d) for d in supplier_domains])
        ]
        if contact_emails:
            backfill_supplier_emails_from_inbox.apply_async(
                args=(cred.id, supplier.id, contact_emails, backfill_time_window),
                countdown=0.5 * cnt,
            )


def backfill_supplier(supplier_id: int, backfill_time_window: int = 365 * 24 * 60 * 60):
    """
    Utility function to backfill all emails to/from a supplier
    """
    supplier = Supplier.objects.get(pk=supplier_id)
    team = supplier.team

    supplier_domains = list(supplier.domains.values_list("domain", flat=True))
    contact_emails = [
        email
        for email in list(supplier.contacts.values_list("email", flat=True))
        if email and not any([email.endswith(d) for d in supplier_domains])
    ]

    backfill_supplier_domains.delay(
        team.id, supplier.id, supplier_domains, backfill_time_window
    )
    backfill_supplier_contacts.delay(
        team.id, supplier.id, contact_emails, backfill_time_window
    )


@shared_task(
    base=Singleton,
    lock_expiry=60 * 12,
    queue="email_backfills",
)
def backfill_team_emails(team_id: int, backfill_time_window: int = 365 * 24 * 60 * 60):
    """
    Util function to backfill all emails to/from a team
    Should only be used to backfill when something has gone wrong with live emails
    """
    team = Team.objects.get(pk=team_id)

    for cred in team.valid_email_credentials:
        backfill_inbox.delay(cred.id, backfill_time_window)


@shared_task(
    base=Singleton,
    lock_expiry=60 * 12,
    queue="periodic_tasks",
)
def backfill_all_teams_emails(
    backfill_time_window: int = 36 * 60 * 60,  # default to last 36 hours
):
    for team in Team.objects.all().iterator():
        backfill_team_emails.delay(team.id, backfill_time_window)
