# Generated by Django 4.2.7 on 2024-07-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0020_teamsetting_teamsetting_unique_user_team_name"),
        ("emails", "0032_remove_emailcredential_nylas_access_code_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="emailcredentialnylas",
            name="team",
            field=models.ForeignKey(
                default=9,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="nylas_email_creds",
                to="users.team",
            ),
            preserve_default=False,
        ),
    ]
