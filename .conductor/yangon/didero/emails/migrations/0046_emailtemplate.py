# Generated by Django 4.2.7 on 2025-03-04 23:10

import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0086_populate_supplier_embedding_metadata"),
        ("users", "0041_alter_teamsetting_name"),
        ("emails", "0045_alter_emailreference_unique_together_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "email_to",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.EmailField(max_length=254),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "email_cc",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.EmailField(max_length=254),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "email_bcc",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.EmailField(max_length=254),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("QUOTATION", "QUOTATION"),
                            ("PURCHASE_ORDER", "PURCHASE_ORDER"),
                            (
                                "PURCHASE_ORDER_ACKNOWLEDGEMENT",
                                "PURCHASE_ORDER_ACKNOWLEDGEMENT",
                            ),
                            ("ORDER_SHIPPED", "ORDER_SHIPPED"),
                            ("ORDER_RECEIVED", "ORDER_RECEIVED"),
                            ("INVOICE_MATCHED", "INVOICE_MATCHED"),
                            ("OTHER", "OTHER"),
                        ],
                        max_length=255,
                    ),
                ),
                ("subject", models.CharField(max_length=255)),
                ("body", models.TextField()),
                (
                    "supplier",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="suppliers.supplier",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.team"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
