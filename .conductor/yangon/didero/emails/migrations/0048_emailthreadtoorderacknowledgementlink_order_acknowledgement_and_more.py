# Generated by Django 4.2.7 on 2025-04-17 19:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0047_emailthreadtoinvoicelink_and_more"),
        ("orders", "0073_invoice_orderacknowledgement_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="emailthreadtoorderacknowledgementlink",
            name="order_acknowledgement",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="orders.orderacknowledgement",
            ),
        ),
        migrations.AddField(
            model_name="emailthreadtoinvoicelink",
            name="email_thread",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="emails.emailthread"
            ),
        ),
        migrations.AddField(
            model_name="emailthreadtoinvoicelink",
            name="invoice",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="orders.invoice"
            ),
        ),
        migrations.AddIndex(
            model_name="emailthreadtoorderacknowledgementlink",
            index=models.Index(
                fields=["email_thread"], name="emails_emai_email_t_8c0485_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emailthreadtoorderacknowledgementlink",
            index=models.Index(
                fields=["order_acknowledgement"], name="emails_emai_order_a_fa9240_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="emailthreadtoorderacknowledgementlink",
            unique_together={("email_thread", "order_acknowledgement")},
        ),
        migrations.AddIndex(
            model_name="emailthreadtoinvoicelink",
            index=models.Index(
                fields=["email_thread"], name="emails_emai_email_t_b9f23f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emailthreadtoinvoicelink",
            index=models.Index(
                fields=["invoice"], name="emails_emai_invoice_f4e0fb_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="emailthreadtoinvoicelink",
            unique_together={("email_thread", "invoice")},
        ),
    ]
