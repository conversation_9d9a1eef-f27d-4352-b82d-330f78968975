# Generated by Django 4.2.7 on 2024-09-06 17:24

from django.db import migrations, models
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0039_alter_emailcredentialnylas_user"),
    ]

    operations = [
        migrations.CreateModel(
            name="PineconeReference",
            fields=[
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "email_body_hash",
                    models.CharField(db_index=True, max_length=64, unique=True),
                ),
                ("reference_count", models.PositiveIntegerField(default=1)),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
