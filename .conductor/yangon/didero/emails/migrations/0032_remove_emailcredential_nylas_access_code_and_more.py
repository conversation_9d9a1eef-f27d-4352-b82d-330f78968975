# Generated by Django 4.2.7 on 2024-07-29 22:46

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("emails", "0031_emailcredential_nylas_access_code"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="emailcredential",
            name="nylas_access_code",
        ),
        migrations.AlterField(
            model_name="emailcredential",
            name="connection",
            field=models.CharField(
                choices=[
                    ("manual", "CONN_MANUAL"),
                    ("imap", "CONN_IMAP"),
                    ("google", "CONN_GOOGLE"),
                    ("microsoft", "CONN_MICROSOFT"),
                ],
                default=("imap", "CONN_IMAP"),
                max_length=12,
            ),
        ),
        migrations.CreateModel(
            name="EmailCredentialNylas",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("email_address", models.EmailField(max_length=254)),
                ("provider", models.CharField(max_length=255)),
                ("scope", models.CharField(max_length=255)),
                ("access_token", models.CharField(max_length=255)),
                ("refresh_token", models.CharField(max_length=255)),
                ("expires_at", models.DateTimeField()),
                ("grant_id", models.CharField(max_length=255)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
