# Generated by Django 4.2.7 on 2024-08-27 20:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion

# Sets user field to null on deletion of the user


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("emails", "0038_emailcredentialnylas_status"),
    ]

    operations = [
        migrations.AlterField(
            model_name="emailcredentialnylas",
            name="user",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
