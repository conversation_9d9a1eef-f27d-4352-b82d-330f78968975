# Generated by Django 4.2.7 on 2024-10-10 21:07

import django.db.models.deletion
import structlog
from django.db import migrations, models

logger = structlog.get_logger(__name__)


def backfill_emailthread_team(apps, schema_editor):
    ### For historical purposes: we originally ran migration with direct import
    ### (since otherwise, we get decrypt error while accessing emails)
    # from didero.emails.models import EmailThread
    # from didero.suppliers.models import Communication
    ### Now that migrations are in prod, we can change to this version,
    ### which will work with historical model
    EmailThread = apps.get_model("emails", "EmailThread")
    Communication = apps.get_model("suppliers", "Communication")

    for thread in EmailThread.objects.all():
        logger.info(f"processing thread {thread.id}")
        email = Communication.objects.filter(email_thread=thread).first()
        if email and email.team:
            thread.team = email.team
            thread.save()
        else:
            logger.warning(f"No valid team found for EmailThread {thread.id}")


def noop(apps, schema_editor):
    pass


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0027_alter_teamsetting_name"),
        ("emails", "0042_remove_emailthread_purchase_order_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="emailthread",
            name="team",
            field=models.ForeignKey(
                to="users.team", on_delete=django.db.models.deletion.CASCADE, null=True
            ),
        ),
        migrations.RunPython(backfill_emailthread_team, noop),
        migrations.AlterField(
            model_name="emailthread",
            name="team",
            field=models.ForeignKey(
                to="users.team", on_delete=django.db.models.deletion.CASCADE
            ),
        ),
    ]
