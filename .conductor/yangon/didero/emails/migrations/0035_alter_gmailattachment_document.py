# Generated by Django 4.2.7 on 2024-08-12 20:47

from django.db import migrations, models
import django.db.models.deletion

from didero.documents.models import Document  # noqa: F401


# This migration is part of the deletion of Supplier.Document (since we have Document.Document)
# GmailAttachments were still being saved as Supplier.Documents, so we need to
# migrate them - similar to how documents.0001_initial did it
def migrate_gmail_attachments(apps, schema_editor):
    GmailAttachment = apps.get_model("emails", "GmailAttachment")
    Document = apps.get_model("documents", "Document")  # noqa: F811
    # DocumentLink = apps.get_model("documents", "DocumentLink")

    for gmail_attachment in GmailAttachment.objects.all():
        supplier_document = gmail_attachment.document
        if supplier_document:
            # Check if the document already exists in the new documents table
            document, created = Document.objects.get_or_create(
                uuid=supplier_document.uuid,
                defaults={
                    "name": supplier_document.name,
                    "doc_type": supplier_document.doc_type,
                    "text_content": supplier_document.text_content,
                    "description": supplier_document.description,
                    "description_meta": supplier_document.description_meta,
                    "csv_data": supplier_document.csv_data,
                    "csv_meta": supplier_document.csv_meta,
                    "document": supplier_document.document,
                    "content_type": supplier_document.content_type,
                    "content_hash": supplier_document.content_hash,
                    "filesize_bytes": supplier_document.filesize_bytes,
                    "team": supplier_document.team,
                },
            )
            # Update the gmail attachment to point to the new document
            gmail_attachment.new_document = document
            gmail_attachment.save()


# The following part worked locally. Staging there were no gmailattachments so it probably wasn't run
# in prod, we somehow got `TypeError: DocumentLink() got unexpected keyword arguments: 'parent_object'`
# which makes no sense, so commenting out for now. All that means is that gmail attachments won't be
# linked to suppliers, but this is a legacy model, and if we ever want to, we can go back and
# link all attachments to their email's supplier.
#            DocumentLink.objects.create(
#                document=document,
#                parent_object=supplier_document.supplier,
#            )


# Without the two functions below, kept getting:
# django.db.utils.OperationalError: cannot ALTER TABLE "documents_document" because it has pending trigger events


def set_constraints_deferred(apps, schema_editor):
    schema_editor.execute("SET CONSTRAINTS ALL DEFERRED;")


def set_constraints_immediate(apps, schema_editor):
    schema_editor.execute("SET CONSTRAINTS ALL IMMEDIATE;")


# We originally ran the below functions, but those require superuser privileges,
# which we have locally, but aws rds doesn't. So we switched to the constraints commands above
# def disable_triggers(apps, schema_editor):
#     schema_editor.execute("ALTER TABLE documents_document DISABLE TRIGGER ALL;")


# def enable_triggers(apps, schema_editor):
#     schema_editor.execute("ALTER TABLE documents_document ENABLE TRIGGER ALL;")


"""
The pattern for this migration is:
Create a new field ("new_document"), that will link to the documents.Document model
Migrate the existing documents to the new field
Delete the old field (which linked to suppliers.Document)
Rename the new field (->normal "document)
"""


class Migration(migrations.Migration):
    dependencies = [
        ("documents", "0001_initial"),
        ("emails", "0034_alter_emailcredentialnylas_grant_id"),
    ]

    operations = [
        migrations.RunPython(set_constraints_deferred),
        migrations.AddField(
            model_name="gmailattachment",
            name="new_document",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="gmail_attachments_new",
                to="documents.document",
            ),
        ),
        migrations.RunPython(migrate_gmail_attachments),
        migrations.RemoveField(
            model_name="gmailattachment",
            name="document",
        ),
        migrations.RenameField(
            model_name="gmailattachment",
            old_name="new_document",
            new_name="document",
        ),
        migrations.RunPython(set_constraints_immediate),
    ]
