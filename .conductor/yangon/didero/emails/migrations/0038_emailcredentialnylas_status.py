# Generated by Django 4.2.7 on 2024-08-21 20:35

import didero.emails.schemas
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0037_alter_gmailattachment_document"),
    ]

    operations = [
        migrations.AddField(
            model_name="emailcredentialnylas",
            name="status",
            field=models.CharField(
                choices=[("valid", "VALID"), ("invalid", "INVALID")],
                db_index=True,
                default=didero.emails.schemas.NylasGrantStatus["VALID"],
                max_length=12,
            ),
        ),
    ]
