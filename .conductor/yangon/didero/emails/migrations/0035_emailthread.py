# Generated by Django 4.2.7 on 2024-08-13 20:55

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0038_purchaseorder_tags"),
        ("emails", "0034_alter_emailcredentialnylas_grant_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailThread",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "thread_id",
                    models.CharField(db_index=True, max_length=255, unique=True),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="email_threads",
                        to="orders.purchaseorder",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
