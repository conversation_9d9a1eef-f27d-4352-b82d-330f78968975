# Generated by Django 4.2.7 on 2024-02-25 19:04

from django.db import migrations, models
import django_cryptography.fields


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0026_backfill_backfilled_until_timestamp"),
    ]

    operations = [
        migrations.AddField(
            model_name="googleoauthcredential",
            name="identity_json",
            field=django_cryptography.fields.encrypt(
                models.CharField(default=None, max_length=4096, null=True)
            ),
        ),
        migrations.AlterField(
            model_name="imapfolderimportprogress",
            name="backfilled_until",
            field=models.DateTimeField(blank=True, default=None, null=True),
        ),
    ]
