# Generated by Django 4.2.7 on 2024-03-04 00:56

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("suppliers", "0030_alter_document_doc_type"),
        ("emails", "0027_googleoauthcredential_identity_json_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="GmailAttachment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("attachment_id", models.CharField(max_length=404)),
                ("gmail_message_id", models.CharField(max_length=255)),
                ("filename", models.Char<PERSON>ield(max_length=255)),
                ("content_type", models.Char<PERSON>ield(max_length=255)),
                ("size_bytes", models.PositiveIntegerField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("waiting", "Waiting"),
                            ("working", "Working"),
                            ("skipped", "Skipped"),
                            ("failed", "Failed"),
                            ("imported", "Imported"),
                        ],
                        db_index=True,
                        default="waiting",
                        max_length=8,
                    ),
                ),
                (
                    "status_updated",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "communication",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="suppliers.communication",
                    ),
                ),
                (
                    "credential",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="emails.emailcredential",
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gmail_attachments",
                        to="suppliers.document",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="suppliers.supplier",
                    ),
                ),
            ],
            options={
                "unique_together": {("attachment_id", "gmail_message_id", "supplier")},
            },
        ),
    ]
