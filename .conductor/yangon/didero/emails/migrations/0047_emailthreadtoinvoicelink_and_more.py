# Generated by Django 4.2.7 on 2025-04-17 19:32

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0046_emailtemplate"),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailThreadToInvoiceLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="EmailThreadToOrderAcknowledgementLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "email_thread",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="emails.emailthread",
                    ),
                ),
            ],
        ),
    ]
