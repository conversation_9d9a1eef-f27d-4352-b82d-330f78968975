# Generated by Django 4.2.7 on 2025-06-10 18:23

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0051_emailtemplate_email_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="emailtemplate",
            name="body",
            field=models.TextField(
                help_text="Email body content. Can contain HTML markup for rich formatting. Nylas will automatically handle MIME encoding for HTML/plain text versions."
            ),
        ),
        migrations.AlterField(
            model_name="emailtemplate",
            name="email_bcc",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.TextField(),
                blank=True,
                help_text="Email addresses. Supports template variables like {{ supplier_default_contact }}",
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="emailtemplate",
            name="email_cc",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.TextField(),
                blank=True,
                help_text="Email addresses. Supports template variables like {{ supplier_default_contact }}",
                null=True,
                size=None,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="emailtemplate",
            name="email_to",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.TextField(),
                blank=True,
                help_text="Email addresses. Supports template variables like {{ supplier_default_contact }}",
                null=True,
                size=None,
            ),
        ),
    ]
