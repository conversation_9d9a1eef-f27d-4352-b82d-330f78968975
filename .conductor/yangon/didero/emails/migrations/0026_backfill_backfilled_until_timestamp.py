# Generated by Django 4.2.7 on 2024-02-18 14:28

from datetime import UTC
from django.db import migrations
from django.db.models import Min


def forwards_func(apps, schema_editor):
    # We get the model from the versioned app registry;
    # if we directly import it, it'll be the wrong version
    ImapFolderImportProgress = apps.get_model("emails", "ImapFolderImportProgress")
    Communication = apps.get_model("suppliers", "Communication")
    for progress in ImapFolderImportProgress.objects.filter(
        backfilled_until__isnull=True
    ):
        oldest_email = Communication.objects.filter(
            supplier=progress.supplier_id,
            comm_type="email",
            email_folder=progress.imap_folder,
        ).aggregate(time=Min("comm_time"))["time"]
        if oldest_email:
            progress.backfilled_until = oldest_email.replace(
                tzinfo=oldest_email.tzinfo or UTC
            )
            progress.save(update_fields=["backfilled_until"])


def reverse_func(apps, schema_editor):
    pass


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0025_imapfolderimportprogress_backfilled_until"),
        ("suppliers", "0029_backfill_domains"),
    ]

    operations = [migrations.RunPython(forwards_func, reverse_func)]
