# Generated by Django 4.2.7 on 2024-09-20 20:44

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


def migrate_email_threads_to_email_thread_to_purchase_order_links(apps, schema_editor):
    EmailThread = apps.get_model("emails", "EmailThread")
    EmailThreadToPurchaseOrderLink = apps.get_model(
        "emails", "EmailThreadToPurchaseOrderLink"
    )

    for email_thread in EmailThread.objects.all():
        purchase_order = email_thread.purchase_order
        if purchase_order:
            EmailThreadToPurchaseOrderLink.objects.create(
                email_thread=email_thread,
                purchase_order=purchase_order,
            )


# TODO: Add this back in later.
def reverse_migrate_email_threads_to_email_thread_to_purchase_order_links(
    apps, schema_editor
):
    EmailThreadToPurchaseOrderLink = apps.get_model(
        "emails", "EmailThreadToPurchaseOrderLink"
    )

    for link in EmailThreadToPurchaseOrderLink.objects.all():
        email_thread = link.email_thread
        email_thread.purchase_order = link.purchase_order
        email_thread.save()
        link.delete()


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0052_alter_orderitem_quantity"),
        (
            "emails",
            "0041_rename_email_body_hash_pineconereference_email_reference_hash",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailThreadToPurchaseOrderLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "email_thread",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="emails.emailthread",
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.purchaseorder",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["email_thread"], name="emails_emai_email_t_41ea0b_idx"
                    ),
                    models.Index(
                        fields=["purchase_order"], name="emails_emai_purchas_cf44ed_idx"
                    ),
                ],
                "unique_together": {("email_thread", "purchase_order")},
            },
        ),
        migrations.RunPython(
            code=migrate_email_threads_to_email_thread_to_purchase_order_links,
            reverse_code=reverse_migrate_email_threads_to_email_thread_to_purchase_order_links,
        ),
        migrations.RemoveField(
            model_name="emailthread",
            name="purchase_order",
        ),
    ]
