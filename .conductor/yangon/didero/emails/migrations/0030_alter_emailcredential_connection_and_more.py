# Generated by Django 4.2.7 on 2024-06-28 15:48
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0029_alter_gmailattachment_communication"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="emailcredential",
            name="connection",
            field=models.CharField(
                choices=[
                    ("manual", "CONN_MANUAL"),
                    ("google", "CONN_GOOGLE"),
                    ("msoft", "CONN_MICROSOFT"),
                ],
                default=("manual", "CONN_MANUAL"),
                max_length=6,
            ),
        ),
        migrations.AlterField(
            model_name="emailcredential",
            name="imap_status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("server_error", "STATUS_SERVER_ERROR"),
                    ("auth_error", "STATUS_AUTH_ERROR"),
                    ("active", "STATUS_ACTIVE"),
                    ("unconnected", "STATUS_NOT_CONNECTED"),
                    ("processing", "STATUS_PROCESSING"),
                ],
                db_index=True,
                default=("active", "STATUS_ACTIVE"),
                max_length=12,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="emailcredential",
            name="smtp_status",
            field=models.CharField(
                choices=[
                    ("server_error", "STATUS_SERVER_ERROR"),
                    ("auth_error", "STATUS_AUTH_ERROR"),
                    ("active", "STATUS_ACTIVE"),
                    ("unconnected", "STATUS_NOT_CONNECTED"),
                    ("processing", "STATUS_PROCESSING"),
                ],
                db_index=True,
                default=("unconnected", "STATUS_NOT_CONNECTED"),
                max_length=12,
            ),
        ),
    ]
