# Generated by Django 4.2.7 on 2024-10-15 20:38

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("emails", "0044_emailcredentialnylas_alert_ops_team"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="emailreference",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="emailreference",
            name="communication",
        ),
        migrations.AlterUniqueTogether(
            name="gmailattachment",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="gmailattachment",
            name="communication",
        ),
        migrations.RemoveField(
            model_name="gmailattachment",
            name="credential",
        ),
        migrations.RemoveField(
            model_name="gmailattachment",
            name="document",
        ),
        migrations.RemoveField(
            model_name="gmailattachment",
            name="supplier",
        ),
        migrations.RemoveField(
            model_name="googleoauthcredential",
            name="imap_credential",
        ),
        migrations.RemoveField(
            model_name="googleoauthcredential",
            name="user",
        ),
        migrations.AlterUniqueTogether(
            name="imapfolderimportprogress",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="imapfolderimportprogress",
            name="imap_account",
        ),
        migrations.RemoveField(
            model_name="imapfolderimportprogress",
            name="supplier",
        ),
        migrations.RemoveField(
            model_name="microsoftoauthcredential",
            name="imap_credential",
        ),
        migrations.RemoveField(
            model_name="microsoftoauthcredential",
            name="user",
        ),
        migrations.DeleteModel(
            name="EmailCredential",
        ),
        migrations.DeleteModel(
            name="EmailReference",
        ),
        migrations.DeleteModel(
            name="GmailAttachment",
        ),
        migrations.DeleteModel(
            name="GoogleOauthCredential",
        ),
        migrations.DeleteModel(
            name="ImapFolderImportProgress",
        ),
        migrations.DeleteModel(
            name="MicrosoftOauthCredential",
        ),
    ]
