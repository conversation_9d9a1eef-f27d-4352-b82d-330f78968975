from __future__ import annotations  # skips evaluation of type hints (PEP 563)

from os import environ
from typing import TYPE_CHECKING, List

import structlog
from django.conf import settings
from django.utils import timezone
from postmarker.core import PostmarkClient

from didero.notifications.models import Notification
from didero.orders.utils.order_util import OrderHelper
from didero.tasks.models import Task, TaskStatus
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums
from didero.utils.utils import (
    is_admin_email,
    is_test_data_email,
    pluralise,
)

if TYPE_CHECKING:
    from didero.orders.models import OrderApproval, PurchaseOrder
    from didero.orders.schemas import PurchaseOrderStatus
from didero.users.models.user_models import User

logger = structlog.get_logger(__name__)

# Postmark Template IDs for our emails
SIGN_IN_TEMPLATE_ID = ********
CONFIRM_ACCOUNT_TEMPLATE_ID = ********
PO_STATUS_UPDATE_TEMPLATE_ID = ********
PO_APPROVAL_REQUEST_TEMPLATE_ID = ********
PO_APPROVAL_REQUEST_NO_BUTTONS_TEMPLATE_ID = ********
UNREAD_NOTIFICATIONS_TEMPLATE_ID = ********
PENDING_APPROVALS_REMINDER_TEMPLATE_ID = ********
UNREAD_TASKS_TEMPLATE_ID = ********

TASKS_URL = f"{settings.APP_URL}/tasks"


##
# Postmark email service: central class to send emails. you can send templated
# or raw text emails.
##
class PostmarkEmailService:
    token = environ.get("POSTMARK_SERVER_API_TOKEN")
    if token:
        client = PostmarkClient(server_token=token)

    @staticmethod
    def send_sign_in_email(to: str, bounce_url: str):
        return PostmarkEmailService.send_email_with_template(
            TemplateId=SIGN_IN_TEMPLATE_ID,
            TemplateModel={"bounce_url": bounce_url},
            From=settings.DEFAULT_FROM_EMAIL,
            To=to,
        )

    @staticmethod
    def send_confirm_account_email(to: str, bounce_url: str):
        return PostmarkEmailService.send_email_with_template(
            TemplateId=CONFIRM_ACCOUNT_TEMPLATE_ID,
            TemplateModel={"bounce_url": bounce_url},
            From=settings.DEFAULT_FROM_EMAIL,
            To=to,
        )

    @staticmethod
    def send_po_status_update_email(
        to: str,
        po: PurchaseOrder,
        old_status: PurchaseOrderStatus,
        new_status: PurchaseOrderStatus,
    ):
        if PostmarkEmailService.user_email_setting_enabled(
            po.placed_by, TeamSettingEnums.USER_EMAIL_PO_UPDATE_ENABLED
        ):
            return PostmarkEmailService.send_email_with_template(
                TemplateId=PO_STATUS_UPDATE_TEMPLATE_ID,
                TemplateModel={
                    "po_number": po.po_number,
                    "po_url": f"{settings.APP_URL}/orders/{po.pk}/overview",
                    "old_status": old_status.human_readable(),
                    "new_status": new_status.human_readable(),
                },
                From=settings.DEFAULT_FROM_EMAIL,
                To=to,
            )

    @staticmethod
    def send_po_approval_request_email(
        requestor: User, approver: User, po: PurchaseOrder
    ):
        if PostmarkEmailService.user_email_setting_enabled(
            approver, TeamSettingEnums.USER_EMAIL_PO_APPROVAL_REQUEST_ENABLED
        ):
            po_item_data = OrderHelper.get_po_items_for_email(po)

            return PostmarkEmailService.send_email_with_template(
                # Using the no buttons template for now; whenever we decide to, we can use the other
                # template and uncomment the lines below
                TemplateId=PO_APPROVAL_REQUEST_NO_BUTTONS_TEMPLATE_ID,
                TemplateModel={
                    "name": approver.display_name,
                    "requestor": requestor.display_name,
                    "po_number": po.po_number,
                    "po_items": [item for item in po_item_data["po_items"].values()],
                    "order_total_price": po_item_data["order_total_price"],
                    "po_url": f"{settings.APP_URL}/orders/{po.pk}/overview",
                    # "po_approval_url": OrderApprovalsHelper.generate_approval_url(
                    #     po, approver, True
                    # ),
                    # "po_rejection_url": OrderApprovalsHelper.generate_approval_url(
                    #     po, approver, False
                    # ),
                },
                From=settings.DEFAULT_FROM_EMAIL,
                To=approver.email,
            )

    @staticmethod
    def send_unread_tasks_email(user: User):
        # Filter for only tasks using the v2 task type
        tasks = Task.objects.filter(
            user=user, status=TaskStatus.PENDING.value, task_type_v2__isnull=False
        )

        if not tasks:
            logger.info(
                f"User {user.pk} has no PENDING tasks; skipping send_unread_tasks_email"
            )
            return

        num_tasks = tasks.count()
        logger.info(f"User {user.pk} has {num_tasks} tasks; sending email now")

        return PostmarkEmailService.send_email_with_template(
            TemplateId=UNREAD_TASKS_TEMPLATE_ID,
            TemplateModel={
                "title": f"You have {num_tasks} pending {pluralise(num_tasks, 'task')}",
                "task_previews": [
                    {
                        "description": task.task_config["title"],
                        "url": f"{TASKS_URL}?taskId={task.pk}",
                    }
                    for task in tasks
                ],
                "all_tasks_url": TASKS_URL,
            },
            From=settings.DEFAULT_FROM_EMAIL,
            To=user.email,
        )

    @staticmethod
    def send_unread_notifications_email(user: User):
        if PostmarkEmailService.user_email_setting_enabled(
            user, TeamSettingEnums.USER_EMAIL_NOTIFICATIONS_REMINDER_ENABLED
        ):
            unreads = list(
                user.get_notifications(
                    team=user.teams.first().pk,
                    unread_only=True,
                )
                .filter(email_sent_at__isnull=True)
                .select_related("actor")
            )

            if not unreads:
                # there's no new notifications, skip this user
                return

            # get the preview strings and send the email
            notification_previews = [
                {"preview": n.build_preview_string()} for n in unreads
            ]
            num_notifications = len(notification_previews)
            success = PostmarkEmailService.send_email_with_template(
                TemplateId=UNREAD_NOTIFICATIONS_TEMPLATE_ID,
                TemplateModel={
                    "title": f"You have {num_notifications} unread  {pluralise(num_notifications, 'notification')}",
                    # get the first 4 only
                    "notification_previews": notification_previews[:4],
                    # there is no notifications page; sent to homepage for now
                    "notifications_url": settings.APP_URL,
                },
                From=settings.DEFAULT_FROM_EMAIL,
                To=user.email,
            )

            if success:
                Notification.objects.filter(id__in=[n.pk for n in unreads]).update(
                    email_sent_at=timezone.now()
                )
            return success

    @staticmethod
    def send_pending_approvals_reminder_email(
        user: User, pending_approvals: List[OrderApproval]
    ):
        if PostmarkEmailService.user_email_setting_enabled(
            user, TeamSettingEnums.USER_EMAIL_PO_APPROVAL_REMINDER_ENABLED
        ):
            num_approvals = len(pending_approvals)
            data = [
                {
                    "po_number": approval.purchase_order.po_number,
                    "approval_url": f"{settings.APP_URL}/orders/{approval.purchase_order.pk}/overview",
                }
                for approval in pending_approvals
            ]
            # get the title string
            if num_approvals < 2:
                title = "A purchase order is waiting for your approval"
            else:
                title = f"There are {num_approvals} purchase orders waiting for your approval"
            # send the email
            return PostmarkEmailService.send_email_with_template(
                TemplateId=PENDING_APPROVALS_REMINDER_TEMPLATE_ID,
                TemplateModel={
                    "title": title,
                    "pending_approvals": data,
                },
                From=settings.DEFAULT_FROM_EMAIL,
                To=user.email,
            )

    def user_email_setting_enabled(user: User, setting_name: TeamSettingEnums):
        # the setting should always exist and be unique; the value is a boolean
        email_enabled = (
            TeamSetting.get_user_settings(user, setting_name.value).get().value
        )
        logger.info(
            f"{'Sending' if email_enabled else 'Skipping'} email with setting {setting_name} for user {user.pk}."
        )
        return email_enabled

    # Check if the recipient is a valid account to which to send emails
    # We skip:
    # - Hard-coded demo account emails (i.e. @ecotech)
    # - Custom demo account emails (emails of users that are flagged as demo accounts)
    # - Didero admin accounts (i.e. "contractors+" ones)
    @staticmethod
    def should_send_email(email: str) -> bool:
        # Try to match email to user, to check if demo account
        user = User.objects.filter(email=email).first()
        if user and user.is_demo_user:
            return False
        return (not is_test_data_email(email)) and not is_admin_email(email)

    @staticmethod
    def send_email_with_template(
        TemplateId: int, TemplateModel: dict, From: str, To: str
    ):
        if PostmarkEmailService.should_send_email(To):
            try:
                return PostmarkEmailService.handle_response(
                    PostmarkEmailService.client.emails.send_with_template(
                        TemplateId=TemplateId,
                        TemplateModel=TemplateModel,
                        From=From,
                        To=To,
                    )
                )
            except Exception as e:
                logger.error(
                    "PostmarkEmailService: Failed to send email",
                    error=str(e),
                    recipient=To,
                    template_id=TemplateId,
                    exc_info=True,
                )
                return False
        else:
            logger.info(
                f"{To} is an invalid email recipient (demo account); skipping postmark email"
            )
            return False

    # Handle the Postmark response; log and return false for errors; return true for successes
    def handle_response(response):
        if response["Message"] != "OK":
            # There was an error when sending the email, log it
            logger.error(
                f"PostmarkEmailService: {response['ErrorCode']}: {response['Message']}"
            )
            return False
        return True
