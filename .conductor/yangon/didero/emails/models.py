import uuid
from typing import Any

import structlog
from auditlog.registry import auditlog
from django.contrib.postgres.fields import Array<PERSON>ield
from django.db import models
from django.db.models.signals import post_delete
from django.dispatch import receiver
from jinja2 import Template

from didero.emails.schemas import (
    EmailCategory,
    NylasGrantStatus,
)
from didero.invoices.models import Invoice
from didero.models import BaseModel, BaseModelWithWebsocketPublishing
from didero.orders.models import OrderAcknowledgement, PurchaseOrder
from didero.suppliers.models import Communication, Supplier
from didero.users.models import Team

logger = structlog.get_logger(__name__)


class PineconeReference(BaseModel):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4)
    email_reference_hash = models.CharField(max_length=64, db_index=True, unique=True)
    reference_count = models.PositiveIntegerField(default=1)


# ==============================================================================
# Email Connections Using Nylas
# ==============================================================================
class EmailCredentialNylas(BaseModelWithWebsocketPublishing):
    user = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
    )
    team = models.ForeignKey(
        "users.Team",
        on_delete=models.CASCADE,
        related_name="nylas_email_creds",
    )
    email_address = models.EmailField()
    provider = models.CharField(max_length=255)
    scope = models.CharField(max_length=2047)
    access_token = models.CharField(max_length=255)
    refresh_token = models.CharField(max_length=255)
    expires_at = models.DateTimeField()
    grant_id = models.CharField(max_length=255, unique=True)
    status = models.CharField(
        choices=[(status.value, status.name) for status in NylasGrantStatus],
        max_length=12,
        default=NylasGrantStatus.VALID,
        db_index=True,
    )
    alert_ops_team = models.BooleanField(
        default=False,
        help_text="When toggled on, emails to this credential (that are sent to suppliers who are also toggled on), will go through Zapier->Slack for the ops team.",
    )

    def __str__(self):
        return self.email_address


@receiver(post_delete, sender=EmailCredentialNylas)
def delete_nylas_credential(
    sender: Any, instance: EmailCredentialNylas, **kwargs: Any
) -> None:
    import didero.emails.utils.nylas as nylas

    try:
        nylas.delete_grant(instance.grant_id)
    except Exception as exc:
        # If cannot find Nylas grant, then it's fine to just delete on our end anyways too
        if "grant.not_found" in str(exc):
            logger.warning(
                f"tried to delete grant {instance.grant_id} (user {instance.user.pk if instance.user else 'None'}), but it was not found in Nylas. Deleting Didero credential anyways"
            )
        logger.exception("Error deleting grant", exc=exc, grant_id=instance.grant_id)


auditlog.register(EmailCredentialNylas)


class EmailThread(BaseModel):
    team = models.ForeignKey("users.Team", on_delete=models.CASCADE)
    thread_id = models.CharField(max_length=255, unique=True, db_index=True)

    # Related names/fields:
    emails: models.QuerySet["Communication"]

    def __str__(self):
        return f"{self.pk} ({self.thread_id})"


auditlog.register(EmailThread)


class EmailThreadToPurchaseOrderLink(BaseModel):
    email_thread = models.ForeignKey(EmailThread, on_delete=models.CASCADE)
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE)

    class Meta(BaseModel.Meta):
        unique_together = ["email_thread", "purchase_order"]
        indexes = [
            models.Index(fields=["email_thread"]),
            models.Index(fields=["purchase_order"]),
        ]


auditlog.register(EmailThreadToPurchaseOrderLink)


class EmailThreadToOrderAcknowledgementLink(BaseModel):
    email_thread = models.ForeignKey(EmailThread, on_delete=models.CASCADE)
    order_acknowledgement = models.ForeignKey(
        OrderAcknowledgement, on_delete=models.CASCADE
    )

    class Meta(BaseModel.Meta):
        unique_together = ["email_thread", "order_acknowledgement"]
        indexes = [
            models.Index(fields=["email_thread"]),
            models.Index(fields=["order_acknowledgement"]),
        ]


auditlog.register(EmailThreadToOrderAcknowledgementLink)


class EmailThreadToInvoiceLink(BaseModel):
    email_thread = models.ForeignKey(EmailThread, on_delete=models.CASCADE)
    invoice = models.ForeignKey(
        "invoices.Invoice", on_delete=models.CASCADE, db_constraint=True
    )

    class Meta(BaseModel.Meta):
        unique_together = ["email_thread", "invoice"]
        indexes = [
            models.Index(fields=["email_thread"]),
            models.Index(fields=["invoice"]),
        ]


auditlog.register(EmailThreadToInvoiceLink)


class EmailTemplate(BaseModel):
    """
    Email template to pre-define messages to suppliers, for a given email category.
    Configured on a team level by default, but can also be specified per supplier.
    Takes the form of a Jinja2 template, with variables arbitrarily named.

    The body field supports HTML content. When rendered, Nylas will automatically
    handle the proper MIME formatting for both HTML and plain text versions.

    e.g. 'Hello {{ supplier_name }}, please see {{ po_number }} for the purchase order.'
    e.g. HTML: '<p>Hello {{ supplier_name }}, please see <strong>{{ po_number }}</strong> for the purchase order.</p>'
    """

    team = models.ForeignKey("users.Team", on_delete=models.CASCADE)
    supplier = models.ForeignKey(
        "suppliers.Supplier", on_delete=models.CASCADE, null=True, blank=True
    )
    template_name = models.CharField(max_length=255)
    email_type = models.CharField(max_length=255, null=True, blank=True)
    email_to = ArrayField(
        models.TextField(),
        null=True,
        blank=True,
        help_text="Email addresses. Supports template variables like {{ supplier_default_contact }}",
    )
    email_cc = ArrayField(
        models.TextField(),
        null=True,
        blank=True,
        help_text="Email addresses. Supports template variables like {{ supplier_default_contact }}",
    )
    email_bcc = ArrayField(
        models.TextField(),
        null=True,
        blank=True,
        help_text="Email addresses. Supports template variables like {{ supplier_default_contact }}",
    )
    subject = models.CharField(max_length=255)
    body = models.TextField(
        help_text="Email body content. Can contain HTML markup for rich formatting. "
        "Nylas will automatically handle MIME encoding for HTML/plain text versions."
    )

    def send_email_from_template(
        self,
        grant_id: str,
        to: list[str],
        cc: list[str],
        bcc: list[str],
        **template_vars: Any,
    ) -> None:
        from didero.emails.utils.nylas import send_message

        rendered_subject = Template(self.subject).render(**template_vars)
        rendered_body = Template(self.body).render(**template_vars)

        full_message = {
            "subject": rendered_subject,
            "body": rendered_body,  # Nylas automatically handles HTML vs plain text MIME formatting
            "from": [{"email": self.team.email_from}],
            "to": [{"email": email} for email in to],
            "cc": [{"email": email} for email in cc],
            "bcc": [{"email": email} for email in bcc],
        }

        send_message(grant_id, full_message)
