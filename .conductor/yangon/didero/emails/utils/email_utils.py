import re

import structlog
from django.contrib.contenttypes.models import ContentType

from didero.documents.models import Document, DocumentLink
from didero.emails.models import EmailThreadToPurchaseOrderLink
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication

logger = structlog.get_logger(__name__)


def propagate_documents_from_thread_to_po(email_thread, purchase_order):
    """
    Propagate all documents from email thread communications to purchase order.

    Args:
        email_thread: The EmailThread containing communications with documents
        purchase_order: The PurchaseOrder to link documents to

    Returns:
        int: Number of new document links created
    """

    documents_linked = 0

    # Get all communications in the thread
    for communication in email_thread.emails.all():
        # Get all document links for this communication
        document_links = DocumentLink.objects.filter(
            parent_object_type=ContentType.objects.get_for_model(Communication),
            parent_object_id=communication.id,  # Django handles the type conversion
        ).select_related("document")

        # Create links from documents to the purchase order
        for doc_link in document_links:
            _, created = DocumentLink.objects.get_or_create(
                parent_object_type=ContentType.objects.get_for_model(PurchaseOrder),
                parent_object_id=purchase_order.id,  # <PERSON><PERSON><PERSON> handles the type conversion
                document=doc_link.document,
            )
            if created:
                documents_linked += 1

    if documents_linked > 0:
        logger.info(
            f"Propagated {documents_linked} documents from email thread "
            f"{email_thread.id} to PO {purchase_order.po_number}"
        )

    return documents_linked


def get_po_related_communications(
    purchase_order: "PurchaseOrder",
) -> list["Communication"]:
    """
    Get all communications related to this PO through email thread links.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        list: List of Communication objects related to this PO
    """
    # Get email thread IDs linked to this PO
    email_thread_ids = EmailThreadToPurchaseOrderLink.objects.filter(
        purchase_order=purchase_order
    ).values_list("email_thread_id", flat=True)

    # Get all communications in these threads
    related_communications = Communication.objects.filter(
        email_thread__id__in=email_thread_ids
    ).order_by("-comm_time")

    return list(related_communications)


def get_po_email_context(purchase_order: "PurchaseOrder"):
    """
    Get core PO context including status, communications, and basic details.

    Args:
        purchase_order: PurchaseOrder instance

    Returns:
        POEmailContext: Core PO context with communications and basic details
    """
    from didero.workflows.core_workflows.follow_up.schemas import (
        EmailInfo,
        POEmailContext,
        POItem,
    )

    # Get all communications related to this PO through email threads
    related_communications = get_po_related_communications(purchase_order)

    # Get latest communication
    latest_email = related_communications[0] if related_communications else None

    # Get last 5 communications for context
    recent_communications = related_communications[:5]

    # Get PO items
    po_items = list(purchase_order.items.all())

    # Build typed items list
    items: list[POItem] = [
        POItem(
            item_name=item.item.description if item.item else "Unknown",
            quantity=item.quantity,
            price=float(item.price.amount) if item.price else 0.0,
            requested_date=item.requested_date,
        )
        for item in po_items
    ]

    # Build latest email info
    latest_email_info: EmailInfo | None = None
    if latest_email:
        latest_email_info = EmailInfo(
            subject=latest_email.email_subject,
            body=latest_email.email_content,
            comm_time=latest_email.comm_time,
            email_from=latest_email.email_from,
            direction=latest_email.direction,
        )

    # Build email history
    email_history: list[EmailInfo] = [
        EmailInfo(
            subject=comm.email_subject,
            body=comm.email_content,
            comm_time=comm.comm_time,
            email_from=comm.email_from,
            direction=comm.direction,
        )
        for comm in recent_communications
    ]

    context = POEmailContext(
        po_number=purchase_order.po_number,
        po_status=purchase_order.order_status,
        placement_time=purchase_order.placement_time,
        expected_delivery_date=getattr(purchase_order, "expected_delivery_date", None),
        supplier_name=purchase_order.supplier.name if purchase_order.supplier else None,
        supplier_email=purchase_order.supplier.default_email
        if purchase_order.supplier
        else None,
        total_cost=float(purchase_order.total_cost.amount)
        if purchase_order.total_cost
        else 0.0,
        items=items,
        latest_email=latest_email_info,
        email_history=email_history,
    )

    return context


def map_order_to_threads(order: PurchaseOrder):
    comms = (
        Communication.objects.filter(
            team=order.team,
            supplier=order.supplier,
        )
        .select_related("email_thread")
        .only("email_thread", "email_subject", "email_content")
    )

    for communication in comms:
        if order_number_in_email(order.po_number, communication):
            map_order_to_thread(order, communication)


def order_number_in_email(po_number: str, communication: Communication):
    # Check if PO number is in email subject or content
    if order_number_in_string(
        po_number, communication.email_subject
    ) or order_number_in_string(po_number, communication.email_content):
        return True

    # If not found in email text, check document attachment names
    return order_number_in_document_names(po_number, communication)


def order_number_in_string(po_number, string):
    return re.search(rf"(?<!\w){re.escape(po_number)}(?!\w)", string) is not None


def order_number_in_document_names(
    po_number: str, communication: Communication
) -> bool:
    """
    Check if the PO number exists in any document names linked to the communication.

    Args:
        po_number: The purchase order number to search for
        communication: The Communication object to check documents for

    Returns:
        bool: True if PO number is found in any linked document name, False otherwise
    """
    # Get all documents linked to this communication
    documents = Document.objects.filter(
        links__parent_object_type=ContentType.objects.get_for_model(Communication),
        links__parent_object_id=communication.pk,  # Django handles the type conversion
    )

    # Check each document name for the PO number
    for document in documents:
        if order_number_in_string(po_number, document.name):
            logger.info(
                f"Found PO number {po_number} in document attachment: {document.name}"
            )
            return True

    return False


def map_order_to_thread(order: PurchaseOrder, communication: Communication):
    link, created = EmailThreadToPurchaseOrderLink.objects.get_or_create(
        email_thread=communication.email_thread,
        purchase_order=order,
    )
    logger.info(
        f"Mapped {order.po_number} to thread with id: {communication.email_thread.pk}"
    )

    # Propagate documents whenever a link is created
    if created:
        propagate_documents_from_thread_to_po(communication.email_thread, order)
