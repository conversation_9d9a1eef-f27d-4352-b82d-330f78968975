import logging
import os

import requests
import structlog
from django.conf import settings
from rest_framework import status
from rest_framework.response import Response
from tenacity import before_log, retry, stop_after_attempt, wait_fixed

NYLAS_API_KEY = os.environ["NYLAS_API_KEY"]
NYLAS_CLIENT_ID = os.environ["NYLAS_CLIENT_ID"]
NYLAS_BASE_URL = "https://api.us.nylas.com/v3"

"""
Library functions for interacting with the Nylas API
https://developer.nylas.com/
"""

logger = structlog.get_logger(__name__)


def _send_nylas_request(method, endpoint, params=None, data=None):
    """
    Send a request to the Nylas API
    Note that we are a bit prone to hitting rate limits, so we retry a few times
    https://developer.nylas.com/docs/dev-guide/platform/rate-limits/#provider-rate-limits
    """
    url = f"{NYLAS_BASE_URL}/{endpoint}"
    headers = {
        "Authorization": f"Bearer {NYLAS_API_KEY}",
    }
    res = requests.request(
        method,
        url,
        headers=headers,
        params=params,
        json=data,
    )
    if res.status_code != 200:
        raise Exception(f"Error {res.status_code}: {res.text}")

    return res


@retry(
    wait=wait_fixed(2),
    stop=stop_after_attempt(6),
    before=before_log(logger, logging.INFO),
)
def _send_nylas_request_with_retry(method, endpoint, params=None, data=None):
    return _send_nylas_request(method, endpoint, params, data)


def get_supplier_query_by_provider(
    provider: str,
    domains: list[str],
):
    if not domains:
        raise ValueError("No domains provided!")

    domains = [domain.lower() for domain in domains if domain]
    if provider == "microsoft":
        # "to:example.com OR from:example.com"
        query_str = (
            '"'
            + " OR ".join([f"to:{domain} OR from:{domain}" for domain in domains])
            + '"'
        )
    elif provider == "google":
        # to or from a domain, or reply-to a domain because
        # gmail groups redirect from the group address and move sender to reply-to
        query_str = (
            '"'
            + " OR ".join(
                [
                    f"to:{domain} OR from:{domain} OR replyto:{domain}"
                    for domain in domains
                ]
            )
            + '"'
        )
    else:
        raise ValueError(f"Unknown provider {provider}")

    # The requests library will automatically encode this string when passed
    # If we stop using the requests library, please keep in mind you may need to encode this string.
    return query_str


def connect_mailbox_with_code(code: str, user=None):
    # TODO: unhack this
    base_url = settings.DEMO_APP_URL if user and user.is_demo_user else settings.APP_URL
    try:
        return _send_nylas_request(
            "POST",
            "connect/token",
            data={
                "client_id": NYLAS_CLIENT_ID,
                "client_secret": NYLAS_API_KEY,
                "grant_type": "authorization_code",
                "code": code,
                "code_verifier": "nylas",
                "redirect_uri": f"{base_url}/auth/oauth/on_email_authorized",
            },
        )
    except Exception as exc:
        if "maximum_number_of_sandbox_grants_reached" in str(exc):
            logger.error("Maximum number of sandbox grants reached")
            response = Response(
                {"error": "maximum_number_of_sandbox_grants_reached"},
                status=status.HTTP_403_FORBIDDEN,
            )
            return response
        else:
            logger.error(f"Error connecting mailbox with code: {exc}")
            response = Response({"error": str(exc)}, status=status.HTTP_400_BAD_REQUEST)
    return None


def delete_grant(grant_id: str):
    return _send_nylas_request(
        "DELETE",
        f"grants/{grant_id}",
    )


def get_messages_from_domains(
    grant_id: str, provider: str, domains: list[str], page_token: str = None
):
    if not domains:
        return [], None

    query = get_supplier_query_by_provider(provider, domains)
    params = {
        "limit": 50,  # min 50, max 200,
        "search_query_native": query,
    }
    if page_token:
        params["page_token"] = page_token

    req = _send_nylas_request_with_retry(
        "GET",
        f"grants/{grant_id}/messages",
        params=params,
    )

    data = req.json()
    return (data["data"], data["next_cursor"] if "next_cursor" in data else None)


def get_messages_from_contacts(
    grant_id: str, contact_emails: list[str], page_token: str = None
):
    if not contact_emails:
        return [], None

    params = {
        "limit": 50,  # min 50, max 200,
        "any_email": ",".join(contact_emails),
    }
    if page_token:
        params["page_token"] = page_token

    req = _send_nylas_request_with_retry(
        "GET",
        f"grants/{grant_id}/messages",
        params=params,
    )

    data = req.json()
    return (data["data"], data["next_cursor"] if "next_cursor" in data else None)


def download_attachment(grant_id: str, attachment_id: str, message_id: str):
    req = _send_nylas_request_with_retry(
        "GET",
        f"grants/{grant_id}/attachments/{attachment_id}/download",
        params={"message_id": message_id},
    )
    return req.content


def send_message(grant_id: str, message: dict):
    """
    message format here: https://developer.nylas.com/docs/api/v3/ecc/#post-/v3/grants/-grant_id-/messages/send
    WARNING: It is not documented that the API only accepts BASE64 encoded attachments
    but does not accept URL-SAFE BASE64
    """
    req = _send_nylas_request(
        "POST",
        f"grants/{grant_id}/messages/send",
        data=message,
    )
    return req.json()
