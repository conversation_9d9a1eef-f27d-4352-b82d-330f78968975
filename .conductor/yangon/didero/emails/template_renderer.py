"""
Email template rendering service.

This module provides a service for rendering email templates with
serialized entity data. Templates can access entity data using dot notation
like {{ supplier.name }} or {{ purchase_order.po_number }}.
"""

from datetime import date
from typing import Any, Dict, List, Optional, Union

import structlog
from jinja2 import TemplateError, Undefined, meta
from jinja2.sandbox import SandboxedEnvironment

from didero.emails.models import EmailTemplate
from didero.orders.models import PurchaseOrder
from didero.orders.serializers import PurchaseOrderSerializer
from didero.suppliers.models import Supplier
from didero.suppliers.serializers import SupplierSerializer
from didero.users.models import Team, User
from didero.users.serializers import TeamSerializer, UserSerializer

logger = structlog.get_logger(__name__)


class TemplateValidationError(Exception):
    """Exception raised when template input validation fails."""

    pass


class GracefulUndefined(Undefined):
    """
    Custom Undefined class that returns the original template syntax instead of raising an error
    when a template variable is not available in the context.
    """

    def __str__(self) -> str:
        return f"{{{{ {self._undefined_name} }}}}"

    def __repr__(self) -> str:
        return f"{{{{ {self._undefined_name} }}}}"

    def __html__(self) -> str:
        """Return HTML-safe version with original template syntax."""
        return f"{{{{ {self._undefined_name} }}}}"

    def __getattr__(self, name: str) -> Any:
        if name == "__html__":
            return self.__html__
        return GracefulUndefined(name=f"{self._undefined_name}.{name}")

    def __getitem__(self, key: Any) -> "GracefulUndefined":
        return GracefulUndefined(name=f"{self._undefined_name}[{key}]")

    def __call__(self, *args: Any, **kwargs: Any) -> "GracefulUndefined":
        return GracefulUndefined(name=f"{self._undefined_name}()")

    def __iter__(self) -> "GracefulUndefined":
        return self

    def __next__(self) -> "GracefulUndefined":
        raise StopIteration


class EmailTemplateRenderer:
    """
    Service for rendering email templates with serialized entity data.
    Uses Jinja2 SandboxedEnvironment to prevent code injection.
    """

    ENTITY_REGISTRY = {
        "purchase_order": {
            "model": PurchaseOrder,
            "serializer": PurchaseOrderSerializer,
        },
        "supplier": {
            "model": Supplier,
            "serializer": SupplierSerializer,
        },
    }

    def __init__(self):
        """Initialize the sandboxed Jinja2 environment."""
        self.jinja_env = SandboxedEnvironment(
            autoescape=True,
            trim_blocks=True,
            lstrip_blocks=True,
            undefined=GracefulUndefined,
        )

    def render_template(
        self,
        template_id: int,
        team_id: int,
        user: User,
        entities: Optional[Dict[str, Union[str, int]]] = None,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Union[str, List[str], List[Dict[str, str]]]]:
        """
        Render an email template with entity data.

        Returns:
            Dictionary with rendered template parts and any errors:
            {
                'subject': 'Rendered subject',
                'body': 'Rendered body content',
                'email_to': ['<EMAIL>'],
                'email_cc': ['<EMAIL>'],
                'email_bcc': ['<EMAIL>'],
                'errors': [{'type': 'missing_variable', 'message': '...'}]
            }
        """
        errors = []

        try:
            self._validate_inputs(template_id, team_id, entities)
            template = EmailTemplate.objects.get(id=template_id, team_id=team_id)

            # Build context with global variables
            context = {
                "current_date": date.today().strftime("%m/%d/%Y"),
            }

            if entities:
                entity_context, entity_errors = self._build_entity_context(
                    entities, team_id
                )
                context.update(entity_context)
                errors.extend(entity_errors)

            user_team_context, user_team_errors = self._build_user_team_context(
                user, team_id
            )
            context.update(user_team_context)
            errors.extend(user_team_errors)

            if additional_context:
                context.update(additional_context)

            # Render template parts
            rendered_subject, subject_errors = self._render_template_part(
                template.subject, context
            )
            rendered_body, body_errors = self._render_template_part(
                template.body, context
            )
            rendered_to, to_errors = self._render_email_list(
                template.email_to or [], context
            )
            rendered_cc, cc_errors = self._render_email_list(
                template.email_cc or [], context
            )
            rendered_bcc, bcc_errors = self._render_email_list(
                template.email_bcc or [], context
            )

            errors.extend(
                subject_errors + body_errors + to_errors + cc_errors + bcc_errors
            )

            return {
                "subject": rendered_subject,
                "body": rendered_body,
                "email_to": rendered_to,
                "email_cc": rendered_cc,
                "email_bcc": rendered_bcc,
                "errors": errors,
            }

        except EmailTemplate.DoesNotExist:
            return {
                "subject": "",
                "body": "",
                "email_to": [],
                "email_cc": [],
                "email_bcc": [],
                "errors": [
                    {
                        "type": "template_not_found",
                        "message": f"Template {template_id} not found or access denied",
                    }
                ],
            }

        except TemplateValidationError as e:
            return {
                "subject": "",
                "body": "",
                "email_to": [],
                "email_cc": [],
                "email_bcc": [],
                "errors": [{"type": "validation_error", "message": str(e)}],
            }

        except Exception as e:
            logger.error(
                "Failed to render email template",
                template_id=template_id,
                team_id=team_id,
                error=str(e),
            )
            return {
                "subject": "",
                "body": "",
                "email_to": [],
                "email_cc": [],
                "email_bcc": [],
                "errors": [
                    {
                        "type": "rendering_error",
                        "message": f"Unexpected error: {str(e)}",
                    }
                ],
            }

    def _build_entity_context(
        self, entities: Dict[str, Union[str, int]], team_id: int
    ) -> tuple[Dict[str, Any], List[Dict[str, str]]]:
        """Build template context by serializing requested entities."""
        context = {}
        errors = []

        for entity_type, entity_id in entities.items():
            if entity_type not in self.ENTITY_REGISTRY:
                errors.append(
                    {
                        "type": "unsupported_entity",
                        "message": f"Unsupported entity type: {entity_type}",
                    }
                )
                continue

            registry_entry = self.ENTITY_REGISTRY[entity_type]
            model_class = registry_entry["model"]
            serializer_class = registry_entry["serializer"]

            try:
                # Fetch entity with team security filtering
                entity = model_class.objects.get(id=entity_id, team_id=team_id)
                serializer = serializer_class(entity)
                entity_data = serializer.data

                # Format total_cost with dollar sign for purchase orders
                if entity_type == "purchase_order" and "total_cost" in entity_data:
                    if entity_data["total_cost"] is not None:
                        entity_data["total_cost"] = f"${entity_data['total_cost']}"

                # Add po_line_items for purchase orders - simple text list
                if entity_type == "purchase_order":
                    po_line_items_lines = []

                    # Add items (with quantities)
                    for item in entity_data.get("items", []):
                        item_info = item.get("item", {})
                        quantity = item.get("quantity", 0)
                        unit_of_measure = item.get("unit_of_measure", "Each")
                        description = (
                            item_info.get("description", "") if item_info else ""
                        )

                        line = f"{description} (Qty: {quantity} {unit_of_measure})"
                        po_line_items_lines.append(line)

                    # Add line items (fees/adjustments)
                    for line_item in entity_data.get("line_items", []):
                        amount = line_item.get("amount", 0)
                        description = (
                            line_item.get("description")
                            or line_item.get("category", "").title()
                        )

                        line = f"{description}: ${amount}"
                        po_line_items_lines.append(line)

                    entity_data["po_line_items"] = "\n".join(po_line_items_lines)

                context[entity_type] = entity_data

            except model_class.DoesNotExist:
                context[entity_type] = {}
                errors.append(
                    {
                        "type": "entity_not_found",
                        "message": f"{entity_type.title()} with ID {entity_id} not found",
                    }
                )

            except Exception as e:
                context[entity_type] = {}
                errors.append(
                    {
                        "type": "entity_error",
                        "message": f"Failed to load {entity_type}: {str(e)}",
                    }
                )

        return context, errors

    def _validate_inputs(
        self,
        template_id: int,
        team_id: int,
        entities: Optional[Dict[str, Union[str, int]]],
    ):
        """Validate all inputs for security and correctness."""
        if not isinstance(template_id, int) or template_id <= 0:
            raise TemplateValidationError(f"Invalid template_id: {template_id}")

        if not isinstance(team_id, int) or team_id <= 0:
            raise TemplateValidationError(f"Invalid team_id: {team_id}")

        if entities is not None:
            if not isinstance(entities, dict):
                raise TemplateValidationError("entities must be a dictionary")

            for entity_type, entity_id in entities.items():
                if not isinstance(entity_type, str):
                    raise TemplateValidationError(
                        f"Entity type must be string: {entity_type}"
                    )

                if entity_type not in self.ENTITY_REGISTRY:
                    raise TemplateValidationError(
                        f"Unsupported entity type: {entity_type}"
                    )

                try:
                    entity_id = int(entity_id)
                    if entity_id <= 0:
                        raise ValueError()
                except (ValueError, TypeError):
                    raise TemplateValidationError(
                        f"Invalid entity_id for {entity_type}: {entity_id}"
                    )

    def _render_template_part(
        self, template_string: str, context: Dict[str, Any]
    ) -> tuple[str, List[Dict[str, str]]]:
        """Render a template string and return both result and any errors."""
        if not template_string:
            return "", []

        errors = []

        try:
            # Check for undefined variables
            ast = self.jinja_env.parse(template_string)
            undefined_vars = meta.find_undeclared_variables(ast)

            for var in undefined_vars:
                if var not in context:
                    errors.append(
                        {
                            "type": "missing_variable",
                            "message": f"Variable '{var}' not available in template context",
                        }
                    )

            # Render template
            compiled_template = self.jinja_env.from_string(template_string)
            rendered = compiled_template.render(**context)

            return rendered, errors

        except TemplateError as e:
            errors.append(
                {
                    "type": "template_syntax",
                    "message": f"Template syntax error: {str(e)}",
                }
            )
            return template_string, errors

        except Exception as e:
            errors.append(
                {"type": "rendering_error", "message": f"Rendering error: {str(e)}"}
            )
            return template_string, errors

    def _render_email_list(
        self, email_list: List[str], context: Dict[str, Any]
    ) -> tuple[List[str], List[Dict[str, str]]]:
        """Render a list of email addresses and return both results and errors."""
        rendered_emails = []
        all_errors = []

        for email_template in email_list:
            rendered_email, errors = self._render_template_part(email_template, context)
            all_errors.extend(errors)
            if rendered_email.strip():
                rendered_emails.append(rendered_email.strip())

        return rendered_emails, all_errors

    def _build_user_team_context(
        self, user: User, team_id: int
    ) -> tuple[Dict[str, Any], List[Dict[str, str]]]:
        """Build context with current user and team data."""
        context = {}
        errors = []

        try:
            user_serializer = UserSerializer(user)
            context["user"] = user_serializer.data

            team = Team.objects.get(id=team_id)
            team_serializer = TeamSerializer(team)
            context["team"] = team_serializer.data

        except Exception as e:
            errors.append(
                {
                    "type": "context_error",
                    "message": f"Failed to load user/team context: {str(e)}",
                }
            )

        return context, errors

    @staticmethod
    def get_supported_entities() -> List[str]:
        """Get list of supported entity types."""
        return list(EmailTemplateRenderer.ENTITY_REGISTRY.keys())


# Global instance for easy access
email_template_renderer = EmailTemplateRenderer()
