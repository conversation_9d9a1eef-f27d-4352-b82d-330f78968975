from datetime import timedelta

from django.urls import reverse
from django.utils import timezone

from didero.emails.models import EmailCredentialNylas
from didero.testing.cases import TestCase


class TestEmailDraftView(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(
            email="<EMAIL>", first_name="Bam", last_name="Adebayo"
        )
        self.auth_as_user(self.user)

        self.cred = EmailCredentialNylas.objects.create(
            user=self.user,
            team=self.user.teams.first(),
            email_address="<EMAIL>",
            provider="test",
            access_token="test",
            refresh_token="test",
            expires_at=timezone.now() + timedelta(days=1),
            grant_id="test",
        )

        self.supplier = self.create_supplier(team=self.user.teams.first())

    def test_create_email_draft(self):
        url = reverse("email-drafts-list")

        response = self.client.post(
            url,
            {
                "email_to": [
                    {
                        "email_address": "<EMAIL>",
                    }
                ],
                "email_subject": "Test Email",
                "email_content": "Test Email Content",
                "credential": self.cred.id,
                "supplier": self.supplier.id,
            },
            format="json",
        )

        # Check if request was successful
        self.assertEqual(response.status_code, 200)

        response_data = response.json()

        self.assertEqual(response_data["emailSubject"], "Test Email")
        self.assertEqual(response_data["emailContent"], "Test Email Content")
        self.assertEqual(response_data["emailTo"][0]["emailAddress"], "<EMAIL>")
