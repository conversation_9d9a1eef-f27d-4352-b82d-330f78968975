from django.contrib.admin.options import get_content_type_for_model
from django.urls import reverse_lazy as reverse
from django.utils.timezone import now
from rest_framework import status

from didero.documents.models import Document, DocumentLink
from didero.emails.models import EmailThread, EmailThreadToPurchaseOrderLink
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication, SupplierContact
from didero.suppliers.serializers import CommunicationSerializer
from didero.testing.cases import TestCase


class TestEmailThreadViewset(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(
            email="<EMAIL>", first_name="Bam", last_name="<PERSON>ebay<PERSON>"
        )
        self.auth_as_user(self.user)
        team = self.user.teams.first()
        self.supplier = self.create_supplier(team=team)
        self.purchase_order = PurchaseOrder.objects.create(
            po_number="PO-12345",
            team=team,
            supplier=self.supplier,
            placed_by=self.user,
        )
        thread = EmailThread.objects.create(
            team=team,
            thread_id="test-thread",
        )
        self.communication = Communication.objects.create(
            team=team,
            supplier=self.supplier,
            comm_type=Communication.TYPE_EMAIL,
            comm_time=now(),
            email_thread=thread,
            email_subject="Test Subject",
            email_content="<html><body><h1>Welcome to Our Amazing Service</h1><p>Dear Amazing User,</p><p>We are sooooooooooooooooooooooooo excited to have you on board. Please let us know if you have any questions whatsoever.</p><p>Best regards and muchas gracias,<br>Customer Support Team</p></body></html>",
        )
        EmailThreadToPurchaseOrderLink.objects.create(
            email_thread=self.communication.email_thread,
            purchase_order=self.purchase_order,
        )

    def test_get_threads_for_supplier(self):
        url = reverse("email-threads-list") + f"?supplier_id={self.supplier.pk}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # now verify the email content
        data = response.json()
        most_recent_email = data["results"][0]["mostRecentEmail"]
        self.assertEqual(data["count"], 1)
        self.assertEqual(
            most_recent_email["emailSubject"], self.communication.email_subject
        )
        # make sure the email excerp is truncated to 200 characters
        self.assertGreater(
            len(CommunicationSerializer(self.communication).data["email_excerpt"]), 200
        )
        self.assertEqual(len(most_recent_email["emailPreview"]), 200)

    def test_get_threads_for_contact(self):
        contact = SupplierContact.objects.create(
            name="Test Contact",
            email="<EMAIL>",
            supplier=self.supplier,
        )
        self.communication.contacts.add(contact)

        url = reverse("email-threads-list") + f"?contact_id={contact.pk}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the email content
        data = response.json()
        most_recent_email = data["results"][0]["mostRecentEmail"]
        self.assertEqual(data["count"], 1)
        self.assertEqual(
            most_recent_email["emailSubject"], self.communication.email_subject
        )
        # Ensure the email excerpt is truncated to 200 characters
        self.assertGreater(
            len(CommunicationSerializer(self.communication).data["email_excerpt"]), 200
        )
        self.assertEqual(len(most_recent_email["emailPreview"]), 200)

    def test_link_po_to_email_thread(self):
        url = reverse(
            "email-thread-details",
            kwargs={"thread_id": self.communication.email_thread.thread_id},
        )
        data = {"purchase_order_id": self.purchase_order.id}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the response content
        response_data = response.json()
        self.assertIn("detail", response_data)
        self.assertIn(str(self.purchase_order.id), response_data["detail"])
        self.assertIn(
            self.communication.email_thread.thread_id, response_data["detail"]
        )

        # Verify the link was created in the database
        link_exists = EmailThreadToPurchaseOrderLink.objects.filter(
            email_thread=self.communication.email_thread,
            purchase_order=self.purchase_order,
        ).exists()
        self.assertTrue(link_exists)

    def test_link_po_to_email_thread_with_invalid_po(self):
        url = reverse(
            "email-thread-details",
            kwargs={"thread_id": self.communication.email_thread.thread_id},
        )
        data = {"purchase_order_id": 9999}  # Assuming 9999 is an invalid ID
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Verify the response content
        response_data = response.json()
        self.assertIn("detail", response_data)
        self.assertIn("was not found", response_data["detail"])

    def test_link_po_to_email_thread_with_invalid_thread(self):
        url = reverse("email-thread-details", kwargs={"thread_id": "invalid_thread_id"})
        data = {"purchase_order_id": self.purchase_order.id}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Verify the response content
        response_data = response.json()
        self.assertIn("detail", response_data)
        self.assertIn("was not found", response_data["detail"])

    def test_link_po_to_email_thread_without_po_id(self):
        url = reverse(
            "email-thread-details",
            kwargs={"thread_id": self.communication.email_thread.thread_id},
        )
        data = {}  # No purchase_order_id provided
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Verify the response content
        response_data = response.json()
        self.assertIn("detail", response_data)
        self.assertEqual(response_data["detail"], "purchase_order_id is required.")

    def test_link_po_to_email_thread_with_documents(self):
        # Create a test document
        document = Document.objects.create(
            name="test_document.pdf",
            team=self.user.teams.first(),
            content_type="application/pdf",
            filesize_bytes=1024,  # Required field
        )

        # Link the document to the communication
        DocumentLink.objects.create(
            parent_object_type=get_content_type_for_model(Communication),
            parent_object_id=self.communication.id,
            document=document,
        )

        # Link the PO to the email thread
        url = reverse(
            "email-thread-details",
            kwargs={"thread_id": self.communication.email_thread.thread_id},
        )
        data = {"purchase_order_id": self.purchase_order.id}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the document was linked to the PO
        po_doc_link = DocumentLink.objects.filter(
            parent_object_type=get_content_type_for_model(PurchaseOrder),
            parent_object_id=self.purchase_order.id,
            document=document,
        ).first()
        self.assertIsNotNone(po_doc_link)

    def test_get_threads_for_supplier_different_team(self):
        # Create a new user and auth as them
        user_2 = self.create_user(email="<EMAIL>")
        self.auth_as_user(user_2)

        # Attempt to get threads for the original supplier using the new user's team
        url = reverse("email-threads-list") + f"?supplier_id={self.supplier.pk}"
        response = self.client.get(url)

        # Verify that the response does not include threads from a different team
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 0)
