from django.contrib.contenttypes.models import ContentType
from django.core.files.uploadedfile import SimpleUploadedFile

from didero.documents.models import Document, DocumentLink
from didero.emails.models import EmailThread, EmailThreadToPurchaseOrderLink
from didero.emails.utils.email_utils import (
    map_communication_to_orders,
    map_order_to_threads,
    order_number_in_document_names,
    order_number_in_email,
    order_number_in_string,
)
from didero.suppliers.models import Communication
from didero.testing.cases import TestCase


class TestMapOrderToThreads(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(email="<EMAIL>")
        self.team = self.user.teams.first()
        self.supplier = self.create_supplier(team=self.team)
        self.order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
            po_number="PO-12345",
        )
        self.thread = EmailThread.objects.create(thread_id=1, team=self.team)

        # Create a communication with a similar but not exact PO number
        self.communication = Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_message_id="123",
            email_subject="PO-123456",  # Similar but not exact
        )
        Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_message_id="1234",
            email_subject="this is a notification for PO-123456",  # Similar but not exact
        )
        Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_message_id="1235",
            email_subject="PO-123456: you have been notified",  # Similar but not exact
        )

    def test_map_order_to_threads(self):
        # check and make sure the link does not exist for this PO
        map_order_to_threads(self.order)
        self.assertIsNone(EmailThreadToPurchaseOrderLink.objects.all().first())

        # Create a communication with the exact PO number in a different context
        exact_communication = Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_subject="Order confirmation for PO-12345. woooooo",  # Contains exact PO number
        )

        # make sure the link does exist for this one
        map_order_to_threads(self.order)
        link = EmailThreadToPurchaseOrderLink.objects.filter(
            email_thread=exact_communication.email_thread, purchase_order=self.order
        ).first()
        self.assertIsNotNone(link)
        self.assertEqual(link.email_thread, exact_communication.email_thread)
        self.assertEqual(link.purchase_order, self.order)

    def test_map_communication_to_orders(self):
        # check and make sure the link does not exist for this communication
        map_communication_to_orders(self.communication)
        self.assertIsNone(EmailThreadToPurchaseOrderLink.objects.all().first())

        # Create a communication with the exact PO number in a different context
        exact_communication = Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_subject="Order confirmation for PO-12345. woooooo",  # Contains exact PO number
        )

        # make sure the link does exist for this one
        map_communication_to_orders(exact_communication)
        link = EmailThreadToPurchaseOrderLink.objects.filter(
            email_thread=exact_communication.email_thread, purchase_order=self.order
        ).first()
        self.assertIsNotNone(link)
        self.assertEqual(link.email_thread, exact_communication.email_thread)
        self.assertEqual(link.purchase_order, self.order)


class TestOrderNumberInEmail(TestCase):
    def setUp(self):
        self.test_cases = [
            # Simple Numeric
            ("12345", "Order confirmation for 12345", True),
            ("12345", "Order confirmation for 123456", False),
            ("12345", "Your order 12345 has been shipped", True),
            ("12345", "Order 12345", True),
            ("12345", "Order 1234567", False),
            # Alphanumeric
            ("PO-12345", "Order confirmation for PO-12345", True),
            ("PO-12345", "Order confirmation for PO-123456", False),
            ("PO-12345", "Your order PO-12345 has been shipped", True),
            ("PO-12345", "Order PO-12345", True),
            ("PO-12345", "Order PO-1234567", False),
            # With Special Characters
            ("PO#12345", "Order confirmation for PO#12345", True),
            ("PO#12345", "Order confirmation for PO#123456", False),
            ("PO#12345", "Your order PO#12345 has been shipped", True),
            ("PO#12345", "Order PO#12345", True),
            ("PO#12345", "Order PO#1234567", False),
            # With Spaces
            ("PO 12345", "Order confirmation for PO 12345", True),
            ("PO 12345", "Order confirmation for PO 123456", False),
            ("PO 12345", "Your order PO 12345 has been shipped", True),
            ("PO 12345", "Order PO 12345", True),
            ("PO 12345", "Order PO 1234567", False),
            # Mixed Case
            ("Po-12345", "Order confirmation for Po-12345", True),
            ("Po-12345", "Order confirmation for Po-123456", False),
            ("Po-12345", "Your order Po-12345 has been shipped", True),
            ("Po-12345", "Order Po-12345", True),
            ("Po-12345", "Order Po-1234567", False),
            # With Prefix and Suffix
            ("INV-12345-2023", "Order confirmation for INV-12345-2023", True),
            ("INV-12345-2023", "Order confirmation for INV-12345-2024", False),
            ("INV-12345-2023", "Your order INV-12345-2023 has been shipped", True),
            ("INV-12345-2023", "Order INV-12345-2023", True),
            ("INV-12345-2023", "Order INV-12345-2024", False),
            # With Leading Zeros
            ("00012345", "Order confirmation for 00012345", True),
            ("00012345", "Order confirmation for 000123456", False),
            ("00012345", "Your order 00012345 has been shipped", True),
            ("00012345", "Order 00012345", True),
            ("00012345", "Order 0001234567", False),
            # With Dashes
            ("123-45-678", "Order confirmation for 123-45-678", True),
            ("123-45-678", "Order confirmation for 123-45-6789", False),
            ("123-45-678", "Your order 123-45-678 has been shipped", True),
            ("123-45-678", "Order 123-45-678", True),
            ("123-45-678", "Order 123-45-6789", False),
            # With Underscores
            ("PO_12345", "Order confirmation for PO_12345", True),
            ("PO_12345", "Order confirmation for PO_123456", False),
            ("PO_12345", "Your order PO_12345 has been shipped", True),
            ("PO_12345", "Order PO_12345", True),
            ("PO_12345", "Order PO_1234567", False),
            # With Multiple Parts
            ("PO-123-456-789", "Order confirmation for PO-123-456-789", True),
            ("PO-123-456-789", "Order confirmation for PO-123-456-7890", False),
            ("PO-123-456-789", "Your order PO-123-456-789 has been shipped", True),
            ("PO-123-456-789", "Order PO-123-456-789", True),
            ("PO-123-456-789", "Order PO-123-456-7890", False),
        ]

    def test_order_number_in_string(self):
        for po_number, email_subject, expected in self.test_cases:
            with self.subTest(po_number=po_number, email_subject=email_subject):
                self.assertEqual(
                    order_number_in_string(po_number, email_subject), expected
                )

    def test_order_number_in_email(self):
        # Case where PO number is in the subject
        communication_subject = Communication(
            email_subject="Order confirmation for PO-12345", email_content=""
        )
        self.assertTrue(order_number_in_email("PO-12345", communication_subject))

        # Case where PO number is in the body
        communication_body = Communication(
            email_subject="Order confirmation",
            email_content="Your order PO-12345 has been shipped",
        )
        self.assertTrue(order_number_in_email("PO-12345", communication_body))

        # Case where PO number is in neither
        communication_none = Communication(
            email_subject="Order confirmation",
            email_content="Your order has been shipped",
        )
        self.assertFalse(order_number_in_email("PO-12345", communication_none))


class TestOrderNumberInDocumentNames(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(email="<EMAIL>")
        self.team = self.user.teams.first()
        self.supplier = self.create_supplier(team=self.team)
        self.thread = EmailThread.objects.create(
            thread_id="test-thread-1", team=self.team
        )

        # Create a communication
        self.communication = Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_message_id="doc-test-123",
            email_subject="Invoice for your order",
            email_content="Please find the invoice attached",
            comm_type=Communication.TYPE_EMAIL,
            direction=Communication.DIRECTION_INCOMING,
        )

    def test_order_number_in_document_name(self):
        """Test finding PO number in document attachment name"""
        # Create a document with PO number in the name
        doc = Document.objects.create(
            name="Invoice-PO-12345.pdf",
            document=SimpleUploadedFile("test.pdf", b"test content"),
            filesize_bytes=12,
            team=self.team,
            user=self.user,
        )

        # Link document to communication
        DocumentLink.objects.create(
            document=doc,
            parent_object=self.communication,
            parent_object_type=ContentType.objects.get_for_model(Communication),
        )

        # Should find the PO number in document name
        self.assertTrue(order_number_in_document_names("PO-12345", self.communication))

        # Should not find a different PO number
        self.assertFalse(order_number_in_document_names("PO-67890", self.communication))

    def test_multiple_documents_with_different_po_numbers(self):
        """Test multiple documents with different PO numbers"""
        # Create multiple documents
        doc1 = Document.objects.create(
            name="Invoice-PO-11111.pdf",
            document=SimpleUploadedFile("test1.pdf", b"test content 1"),
            filesize_bytes=14,
            team=self.team,
            user=self.user,
        )
        doc2 = Document.objects.create(
            name="Shipping-Notice-PO-22222.pdf",
            document=SimpleUploadedFile("test2.pdf", b"test content 2"),
            filesize_bytes=14,
            team=self.team,
            user=self.user,
        )

        # Link both documents to communication
        DocumentLink.objects.create(
            document=doc1,
            parent_object=self.communication,
            parent_object_type=ContentType.objects.get_for_model(Communication),
        )
        DocumentLink.objects.create(
            document=doc2,
            parent_object=self.communication,
            parent_object_type=ContentType.objects.get_for_model(Communication),
        )

        # Should find both PO numbers
        self.assertTrue(order_number_in_document_names("PO-11111", self.communication))
        self.assertTrue(order_number_in_document_names("PO-22222", self.communication))

        # Should not find a PO number that doesn't exist
        self.assertFalse(order_number_in_document_names("PO-33333", self.communication))

    def test_no_documents_attached(self):
        """Test when no documents are attached to communication"""
        # Communication has no linked documents
        self.assertFalse(order_number_in_document_names("PO-12345", self.communication))

    def test_document_name_without_po_number(self):
        """Test document attached but without PO number in name"""
        doc = Document.objects.create(
            name="General_Invoice.pdf",
            document=SimpleUploadedFile("test.pdf", b"test content"),
            filesize_bytes=12,
            team=self.team,
            user=self.user,
        )

        DocumentLink.objects.create(
            document=doc,
            parent_object=self.communication,
            parent_object_type=ContentType.objects.get_for_model(Communication),
        )

        self.assertFalse(order_number_in_document_names("PO-12345", self.communication))

    def test_complex_document_names(self):
        """Test various complex document naming patterns"""
        test_cases = [
            ("PO-12345-Invoice-2024.pdf", "PO-12345", True),
            ("Invoice for PO-12345.xlsx", "PO-12345", True),
            ("Shipping-Notice-PO-12345-Final.doc", "PO-12345", True),
            ("PO-12345", "PO-12345", True),  # Just the PO number as filename
            ("myPO-12345.pdf", "PO-12345", False),  # PO number is part of larger word
            ("PO-12345x.pdf", "PO-12345", False),  # PO number is part of larger word
            ("Purchase Order 12345.pdf", "12345", True),  # Numeric PO without prefix
            (
                "INV-00012345-signed.pdf",
                "INV-00012345",
                True,
            ),  # PO with prefix and leading zeros
            ("PO 12345 Invoice.pdf", "PO 12345", True),  # PO number with spaces
        ]

        for filename, po_number, expected in test_cases:
            with self.subTest(filename=filename, po_number=po_number):
                # Create a new communication for each test
                comm = Communication.objects.create(
                    team=self.team,
                    supplier=self.supplier,
                    email_thread=self.thread,
                    email_message_id=f"test-{filename}",
                    email_subject="Test",
                    comm_type=Communication.TYPE_EMAIL,
                    direction=Communication.DIRECTION_INCOMING,
                )

                doc = Document.objects.create(
                    name=filename,
                    document=SimpleUploadedFile("test.pdf", b"test"),
                    filesize_bytes=4,
                    team=self.team,
                    user=self.user,
                )

                DocumentLink.objects.create(
                    document=doc,
                    parent_object=comm,
                    parent_object_type=ContentType.objects.get_for_model(Communication),
                )

                self.assertEqual(
                    order_number_in_document_names(po_number, comm),
                    expected,
                    f"Failed for filename: {filename}, po_number: {po_number}",
                )

    def test_order_number_in_email_with_document_fallback(self):
        """Test the complete order_number_in_email function with document fallback"""
        # Create a communication without PO in subject or content
        comm = Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_message_id="fallback-test",
            email_subject="Invoice attached",
            email_content="Please see the attached invoice",
            comm_type=Communication.TYPE_EMAIL,
            direction=Communication.DIRECTION_INCOMING,
        )

        # Should not find PO number yet
        self.assertFalse(order_number_in_email("PO-99999", comm))

        # Add document with PO number in name
        doc = Document.objects.create(
            name="Invoice-PO-99999-Final.pdf",
            document=SimpleUploadedFile("test.pdf", b"test"),
            filesize_bytes=4,
            team=self.team,
            user=self.user,
        )

        DocumentLink.objects.create(
            document=doc,
            parent_object=comm,
            parent_object_type=ContentType.objects.get_for_model(Communication),
        )

        # Now should find PO number via document name
        self.assertTrue(order_number_in_email("PO-99999", comm))

    def test_map_communication_to_orders_with_document_attachment(self):
        """Test that map_communication_to_orders works with document attachments"""
        # Create a PO
        order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
            po_number="PO-ATTACH-123",
        )

        # Create communication without PO in text
        comm = Communication.objects.create(
            team=self.team,
            supplier=self.supplier,
            email_thread=self.thread,
            email_message_id="map-test",
            email_subject="Documents for your order",
            email_content="Attached are the requested documents",
            comm_type=Communication.TYPE_EMAIL,
            direction=Communication.DIRECTION_INCOMING,
        )

        # Initially no link should exist
        self.assertIsNone(
            EmailThreadToPurchaseOrderLink.objects.filter(
                email_thread=self.thread, purchase_order=order
            ).first()
        )

        # Add document with PO number
        doc = Document.objects.create(
            name="OrderConfirmation-PO-ATTACH-123.pdf",
            document=SimpleUploadedFile("test.pdf", b"test"),
            filesize_bytes=4,
            team=self.team,
            user=self.user,
        )

        DocumentLink.objects.create(
            document=doc,
            parent_object=comm,
            parent_object_type=ContentType.objects.get_for_model(Communication),
        )

        # Map should now work via document name
        mapped_order = map_communication_to_orders(comm)

        # Verify the order was mapped
        self.assertEqual(mapped_order, order)

        # Verify the link was created
        link = EmailThreadToPurchaseOrderLink.objects.filter(
            email_thread=self.thread, purchase_order=order
        ).first()
        self.assertIsNotNone(link)
        self.assertEqual(link.email_thread, self.thread)
        self.assertEqual(link.purchase_order, order)
