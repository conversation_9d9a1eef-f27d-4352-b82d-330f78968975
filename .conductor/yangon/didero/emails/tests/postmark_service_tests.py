from unittest import skip

from django.contrib.contenttypes.models import ContentType

from didero.emails.postmark_service import PostmarkEmailService
from didero.notifications.helper import NotificationHelper
from didero.orders.models import OrderApproval, PurchaseOrder
from didero.orders.schemas import PurchaseOrderStatus
from didero.tasks.models import TaskConfig, TaskStatus, TaskType
from didero.testing.cases import TestCase
from didero.users.models.user_models import User
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums


##
# Simple viewset tests for the Postmart email services.
##
class TestPostmarkService(TestCase):
    def setUp(self):
        super().setUp()
        # create an item and its dependencies (users, po)
        self.requestor = self.create_user(
            email="<EMAIL>", first_name="Bam", last_name="Adebayo"
        )

        self.approver = self.create_user(
            email="<EMAIL>", first_name="<PERSON>", last_name="<PERSON>mba<PERSON><PERSON>"
        )

        team = self.approver.teams.first()
        team_id = team.id
        supplier = self.create_supplier(team=team)

        self.po = self.create_purchase_order(
            supplier=supplier, team=team, placed_by=self.requestor
        )

        item1 = self.create_item(supplier=supplier, team_id=team_id)
        self.create_order_item(purchase_order=self.po, item=item1)

        item2 = self.create_item(
            description="Fax Machine (1999)",
            item_number="TECH-01",
            price=1000.5987,
            price_currency="TRY",
            supplier=supplier,
            team_id=team_id,
        )
        self.create_order_item(purchase_order=self.po, item=item2, quantity=4.750)

    @skip
    def test_sign_in_email(self):
        success = PostmarkEmailService.send_sign_in_email(
            self.approver.email, "https://google.com"
        )
        self.assertTrue(success)

    @skip
    def test_confirm_account_email(self):
        success = PostmarkEmailService.send_confirm_account_email(
            self.approver.email, "https://google.com"
        )
        self.assertTrue(success)
        pass

    @skip
    def test_po_approved_email(self):
        success = PostmarkEmailService.send_po_status_update_email(
            self.approver.email,
            self.po,
            PurchaseOrderStatus.APPROVAL_PENDING,
            PurchaseOrderStatus.APPROVED,
        )
        self.assertTrue(success)

    @skip
    def test_po_approval_request_email(self):
        success = PostmarkEmailService.send_po_approval_request_email(
            self.requestor, self.approver, self.po
        )
        self.assertTrue(success)

    @skip
    def test_unread_notifications_email(self):
        # create 4 notifications for a PO
        NotificationHelper.create_notification_for_po_status_update(
            self.po,
            PurchaseOrderStatus.APPROVAL_PENDING.human_readable(),
            PurchaseOrderStatus.APPROVAL_DENIED.human_readable(),
            self.approver,
        )
        NotificationHelper.create_notification_for_po_status_update(
            self.po,
            PurchaseOrderStatus.DRAFT.human_readable(),
            PurchaseOrderStatus.APPROVAL_PENDING.human_readable(),
            self.approver,
        )
        NotificationHelper.create_notification_for_po_status_update(
            self.po,
            PurchaseOrderStatus.APPROVAL_PENDING.human_readable(),
            PurchaseOrderStatus.APPROVED.human_readable(),
            self.approver,
        )

        # send the unread notifications email to the expecting user (self.requestor in this case)
        success = PostmarkEmailService.send_unread_notifications_email(self.requestor)
        self.assertTrue(success)

    @skip
    def test_pending_approvals_email(self):
        pending_approvals = [
            OrderApproval(purchase_order=PurchaseOrder(po_number="PO-111", pk=111)),
            OrderApproval(purchase_order=PurchaseOrder(po_number="PO-222", pk=222)),
            OrderApproval(purchase_order=PurchaseOrder(po_number="PO-333", pk=333)),
            OrderApproval(purchase_order=PurchaseOrder(po_number="PO-444", pk=444)),
            OrderApproval(purchase_order=PurchaseOrder(po_number="PO-555", pk=555)),
        ]
        success = PostmarkEmailService.send_pending_approvals_reminder_email(
            self.approver, pending_approvals
        )
        self.assertTrue(success)

    @skip
    def test_send_unread_tasks_email(self):
        task_type = TaskType.objects.create(name="TEST", base_config={})

        # Create some pending tasks for the user
        self.create_task(
            user=self.requestor,
            task_config=TaskConfig([], "Task 1"),
            task_type=task_type,
            status=TaskStatus.PENDING,
            model_type=ContentType.objects.get_for_model(self.po),
            model_id=self.po.id,
        )
        self.create_task(
            user=self.requestor,
            task_config=TaskConfig([], "Task 2"),
            task_type=task_type,
            status=TaskStatus.PENDING,
            model_type=ContentType.objects.get_for_model(self.po),
            model_id=self.po.id,
        )
        self.create_task(
            user=self.requestor,
            task_config=TaskConfig([], "Task 3"),
            task_type=task_type,
            status=TaskStatus.PENDING,
            model_type=ContentType.objects.get_for_model(self.po),
            model_id=self.po.id,
        )

        # Send the unread tasks email
        self.assertTrue(PostmarkEmailService.send_unread_tasks_email(self.requestor))

    def test_email_permission_for_po_approval_request_email(self):
        # update the email setting to disable it
        setting = TeamSetting.get_user_settings(
            self.approver, TeamSettingEnums.USER_EMAIL_PO_APPROVAL_REQUEST_ENABLED.value
        ).get()
        setting.value = False
        setting.save()

        success = PostmarkEmailService.send_po_approval_request_email(
            self.requestor, self.approver, self.po
        )
        self.assertIsNone(success)

    def test_should_send_email(self):
        valid_email = "<EMAIL>"
        valid_admin_like_email = "<EMAIL>"
        demo_email = "<EMAIL>"
        admin_email = "<EMAIL>"

        # Test with a valid email
        self.assertTrue(PostmarkEmailService.should_send_email(valid_email))

        # Test with an admin like email
        self.assertTrue(PostmarkEmailService.should_send_email(valid_admin_like_email))

        # Test with a demo email (should be False)
        self.assertFalse(PostmarkEmailService.should_send_email(demo_email))

        # Test with an admin email (should be False)
        self.assertFalse(PostmarkEmailService.should_send_email(admin_email))

        # Test with a custom demo account user
        demo_user = User.objects.create(
            email="<EMAIL>", is_demo_user=True
        )
        self.assertFalse(PostmarkEmailService.should_send_email(demo_user.email))
