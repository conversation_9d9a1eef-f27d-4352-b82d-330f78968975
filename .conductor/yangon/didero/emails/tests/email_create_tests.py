from unittest.mock import patch

from django.urls import reverse_lazy as reverse

from didero.email_consumer.main import handle_message_dict
from didero.emails.models import EmailCredentialNylas, EmailThreadToPurchaseOrderLink
from didero.emails.schemas import NylasGrantStatus
from didero.emails.tasks.nylas_tasks import create_communication_and_documents
from didero.suppliers.models import Communication, Supplier, SupplierContact
from didero.testing.cases import TestCase

EXAMPLE_GRANT_ID = "grant-id-123"

EXAMPLE_EMAIL = {
    "id": "1234567890",
    "grant_id": EXAMPLE_GRANT_ID,
    "thread_id": "abcdefghijklmnopqrstuvwxyz",
    "date": 1671234087,
    "folders": [],
    "subject": "Sending Emails with <PERSON>ylas",
    "body": "Nylas API v3 Test!",
    "from": [{"name": "<PERSON> Doe", "email": "<EMAIL>"}],
    "to": [{"name": "<PERSON>", "email": "<EMAIL>"}],
    "cc": [{"name": "Nyla<PERSON>", "email": "<EMAIL>"}],
    "bcc": [{"name": "Nylas", "email": "<EMAIL>"}],
    "attachments": [
        {
            "id": "1234567890",
            "content": "HASKDJhiuahsdjlkhKJAsd=",
            "content_type": "text/plain",
            "filename": "myfile.txt",
        }
    ],
}

EXAMPLE_PUBSUB_EMAIL_MESSAGE = {
    "id": "1234567890",
    "type": "message.created",
    "data": {"object": EXAMPLE_EMAIL},
}

EXAMPLE_PUBSUB_GRANT_EXPIRED_MESSAGE = {
    "id": "1234567890",
    "type": "grant.expired",
    "data": {"object": {"grant_id": EXAMPLE_GRANT_ID}},
}


class TestEmailCreate(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(email="<EMAIL>")
        self.supplier = self.create_supplier(
            team=self.user.teams.first(), domains=["example.com"]
        )
        self.credential = self.create_nylas_credential(
            user=self.user,
            team=self.user.teams.first(),
            email_address="<EMAIL>",
        )
        self.order = self.create_purchase_order(
            team=self.user.teams.first(),
            supplier=self.supplier,
            placed_by=self.user,
        )

    @patch("didero.emails.utils.nylas.download_attachment")
    def test_create_communication(self, mock_download_attachment):
        mock_download_attachment.return_value = b"HASKDJhiuahsdjlkhKJAsd="

        comm = create_communication_and_documents(
            self.credential,
            self.supplier,
            EXAMPLE_EMAIL,
            [self.order],
        )
        self.assertEqual(comm.email_credential, self.credential)
        self.assertEqual(comm.team, self.user.teams.first())
        self.assertEqual(comm.supplier, self.supplier)
        assert EmailThreadToPurchaseOrderLink.objects.filter(
            purchase_order=self.order,
            email_thread=comm.email_thread,
        ).exists()

        contacts = SupplierContact.objects.filter(supplier=self.supplier)
        self.assertEqual(len(contacts), 1)
        self.assertEqual(contacts[0].email, "<EMAIL>")


class TestSendEmailView(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(email="<EMAIL>")
        self.auth_as_user(self.user)
        self.supplier = self.create_supplier(
            team=self.user.teams.first(), domains=["example.com"]
        )
        self.credential = self.create_nylas_credential(
            user=self.user,
            team=self.user.teams.first(),
            email_address="<EMAIL>",
        )
        self.order = self.create_purchase_order(
            team=self.user.teams.first(),
            supplier=self.supplier,
            placed_by=self.user,
        )

    @patch("didero.emails.utils.nylas.download_attachment")
    @patch("didero.emails.utils.nylas.send_message")
    def test_send_reply_email(self, mock_send_message, mock_download_attachment):
        mock_download_attachment.return_value = b"HASKDJhiuahsdjlkhKJAsd="
        mock_send_message.return_value = {"data": EXAMPLE_EMAIL}

        og_email = create_communication_and_documents(
            self.credential,
            self.supplier,
            EXAMPLE_EMAIL,
            [self.order],
        )

        body = {
            "credentialId": self.credential.id,
            "subject": og_email.email_subject,
            "supplierId": self.supplier.id,
            "to": [og_email.email_from],
            "body": "<p>hello,</p><p></p><p>this is a test email.</p><p></p><p>best,</p><p>Testman</p>",
            "replyToId": og_email.id,
            "cc": [],
            "bcc": [],
            "attachments": [],
            "docAttachments": [],
        }

        url = reverse("email-send")

        resp = self.client.post(url, body)
        self.assertEqual(resp.status_code, 200)
        msg = resp.json()["message"]
        self.assertEqual(msg["replyToMessageId"], EXAMPLE_EMAIL["id"])


class TestNylasConsumer(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user(email="<EMAIL>")
        self.supplier = self.create_supplier(
            team=self.user.teams.first(),
            domains=["example.com"],
        )
        self.credential = self.create_nylas_credential(
            user=self.user,
            team=self.user.teams.first(),
            email_address="<EMAIL>",
            grant_id=EXAMPLE_GRANT_ID,
        )

    @patch("didero.emails.utils.nylas.download_attachment")
    def test_consume_nylas_email_message(self, mock_download_attachment):
        mock_download_attachment.return_value = b"HASKDJhiuahsdjlkhKJAsd="

        handle_message_dict(EXAMPLE_PUBSUB_EMAIL_MESSAGE)

        comm = Communication.objects.filter(email_credential=self.credential).first()
        assert comm
        self.assertEqual(comm.supplier, self.supplier)
        self.assertEqual(comm.email_from, EXAMPLE_EMAIL["from"][0]["email"])
        self.assertEqual(comm.email_subject, EXAMPLE_EMAIL["subject"])

    def test_consume_grant_expired_message(self):
        handle_message_dict(EXAMPLE_PUBSUB_GRANT_EXPIRED_MESSAGE)

        cred = EmailCredentialNylas.objects.get(grant_id=EXAMPLE_GRANT_ID)
        self.assertEqual(cred.status, NylasGrantStatus.INVALID)
