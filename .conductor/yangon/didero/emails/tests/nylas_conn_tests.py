import email
from didero.emails.tasks.nylas_tasks import backfill_supplier_emails_from_inbox
from didero.suppliers.models import Communication
from didero.testing.cases import TestCase
from unittest.mock import patch
from didero.emails.utils.nylas import get_supplier_query_by_provider
import time
from didero.users.utils.user_group_permissions import create_default_permission_groups

EXAMPLE_GRANT_ID = "grant-id-123"

EXAMPLE_EMAIL = {
    "id": "**********",
    "grant_id": EXAMPLE_GRANT_ID,
    "thread_id": "abcdefghijklmnopqrstuvwxyz",
    "date": **********,
    "folders": [],
    "subject": "Sending Emails with <PERSON>yla<PERSON>",
    "body": "Nylas API v3 Test!",
    "from": [{"name": "<PERSON>", "email": "<EMAIL>"}],
    "to": [{"name": "<PERSON>", "email": "<EMAIL>"}],
    "cc": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}],
    "bcc": [{"name": "<PERSON>yla<PERSON>", "email": "<EMAIL>"}],
    "attachments": [
        {
            "id": "**********",
            "content": "HASKDJhiuahsdjlkhKJAsd=",
            "content_type": "text/plain",
            "filename": "myfile.txt",
        }
    ],
}


class TestSupplierQuery(TestCase):
    def test_gmail_query(self):
        query = get_supplier_query_by_provider("google", ["example.com"])
        self.assertEqual(query, '"to:example.com OR from:example.com"')

    def test_microsoft_query(self):
        query = get_supplier_query_by_provider("microsoft", ["example.com"])
        self.assertEqual(query, '"to:example.com OR from:example.com"')

    def test_unknown_provider(self):
        with self.assertRaises(ValueError):
            get_supplier_query_by_provider("unknown", ["example.com"])


class TestEmailBackfills(TestCase):
    def setUp(self):
        super().setUp()
        create_default_permission_groups()
        self.user = self.create_user(email="<EMAIL>")
        self.supplier = self.create_supplier(
            team=self.user.teams.first(), domains=["example.com"]
        )
        self.credential = self.create_nylas_credential(
            user=self.user,
            team=self.user.teams.first(),
            email_address="<EMAIL>",
        )
        self.order = self.create_purchase_order(
            team=self.user.teams.first(),
            supplier=self.supplier,
            placed_by=self.user,
        )

    @patch("didero.emails.utils.nylas.download_attachment")
    @patch("didero.emails.utils.nylas.get_messages_from_domains")
    def test_backfill_supplier_emails_from_inbox(
        self, mock_nylas_response, mock_download_attachment
    ):
        now = int(time.time())
        email_1 = EXAMPLE_EMAIL.copy()
        email_1["date"] = now
        email_1["subject"] = "Email from now"

        email_2 = EXAMPLE_EMAIL.copy()
        email_2["date"] = now - (2 * 365 * 24 * 60 * 60)  # current time minus 2 years
        email_2["subject"] = "Email from 2 years ago"

        mock_nylas_response.return_value = [email_1, email_2], None
        mock_download_attachment.return_value = b"HASKDJhiuahsdjlkhKJAsd="

        backfill_supplier_emails_from_inbox(self.credential.id, self.supplier.pk, [])
        self.assertEqual(
            Communication.objects.count(), 1
        )  # only one email is within the time range
