import re


def parse_display_name_and_address(thing: str):
    """
    inputs:
    <PERSON> <<EMAIL>>
    "Joe Alcorn" <<EMAIL>>
    <<EMAIL>>
    <EMAIL>

    outputs:
    ("Joe Alcorn", "<EMAIL>")
    ("", "<EMAIL>")
    """
    striplist = ' "<>'
    if "<" in thing:
        name, from_addr = thing.split("<", 1)
        name = name.strip(striplist)
        from_addr = from_addr.strip(striplist)
        return name, from_addr

    # assume this is just the address then
    return "", thing.strip(striplist)


def split_refs_string(refstr):
    """
    Inputs:
    <<EMAIL>>
    <<EMAIL>> <<EMAIL>>
    <EMAIL> <EMAIL>
    """
    refs = []
    for _ref in refstr.split(" "):
        ref = _ref.strip(" <>")
        if ref and ref not in refs:
            refs.append(ref)
    return refs


def parse_forwarded_email_body(body_text: str):
    """
    Forwarding is handled within email clients rather than the protocol.
    This function attempts to figure out the details of the *message being
    forwarded*, which is not the message we've received

    A --1---> B
    B -2(1)-> Didero

    In this case we're looking to find the metadata of message 1 which
    is hopefully included to message 2.
    """

    # dummy = {
    # <AUTHOR> <EMAIL>\nDate: Mon, Nov 27, 2023 at 10:30\u202fPM\nSubject: Contract review\nTo: Yavor Punchev <<EMAIL>>\n\n\nPlease review and let me know what you think\n\n\nJoe Alcorn\nCofounder\nhttps://decluttergcp.com\n",
    # }
    # gmail_fwd_marker = "---------- Forwarded message ---------"

    # the recipient here should be somebody within the Didero org.
    # It seems likely that the from is either a supplier or somebody else
    # within the org
    to_match = re.search("To: (.+)\n", body_text)
    from_match = re.search("From: (.+)\n", body_text)
    subject_match = re.search("Subject: (.+)\n", body_text)
    # date_match = re.search("Date: (.+)\n", body_text)

    from_name, from_addr, from_domain = "", "", ""
    if from_match:
        from_name, from_addr = parse_display_name_and_address(from_match.group(1))
        from_domain = from_addr.split("@", 1)[-1]

    to_name, to_addr, to_domain = "", "", ""
    if to_match:
        to_name, to_addr = parse_display_name_and_address(to_match.group(1))
        to_domain = to_addr.split("@", 1)[-1]
    return {
        "from": {
            "name": from_name,
            "addr": from_addr,
            "domain": from_domain,
        },
        "to": {
            "name": to_name,
            "addr": to_addr,
            "domain": to_domain,
        },
        "subject": subject_match.group(1) if subject_match else "",
    }
