import base64
import uuid
from datetime import timedelta

import structlog
from django.contrib.admin.options import get_content_type_for_model
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.models import OuterRef, Q, Subquery
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

import didero.emails.utils.nylas as nylas
from didero.documents.models import Document, DocumentLink
from didero.emails.models import (
    EmailCredentialNylas,
    EmailTemplate,
    EmailThread,
    EmailThreadToPurchaseOrderLink,
)
from didero.emails.schemas import NylasGrantStatus
from didero.emails.serializers import (
    AttachmentSummarySerializer,
    EmailCredentialNylasSerializer,
    EmailTemplateSerializer,
)
from didero.emails.tasks.nylas_tasks import (
    backfill_inbox,
    create_communication_and_documents,
)
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Communication, Supplier, SupplierContact
from didero.suppliers.serializers import (
    CommunicationSerializer,
    EmailThreadDetailSerializer,
    EmailThreadSerializer,
)
from didero.views import APIView, PagesPagination, TeamOwnerFilteredModelView

logger = structlog.get_logger()


class EmailCredentialViewset(APIView, TeamOwnerFilteredModelView):
    serializer_class = EmailCredentialNylasSerializer
    permission_classes = [IsAuthenticated]
    model = EmailCredentialNylas

    def get_queryset(self):
        return super().get_queryset().order_by("-created_at")


class EmailDraftViewSet(APIView, TeamOwnerFilteredModelView):
    serializer_class = CommunicationSerializer
    permission_classes = [IsAuthenticated]
    model = Communication

    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                user=self.request.user,
                supplier=self.request.query_params["supplier_id"],
                is_draft=True,
            )
        )

    # TODO: clean up communication model/view/serializer
    def create(self, request):
        cred = EmailCredentialNylas.objects.get(
            id=self.request.data["credential"], team=self.request.team
        )

        # Check if creating from template
        template_id = request.data.get("template_id")
        if template_id:
            try:
                template = EmailTemplate.objects.get(id=template_id, team=request.team)
                # Use template data as base
                data = {
                    **request.data,
                    "email_message_id": uuid.uuid4().hex,
                    "is_draft": True,
                    "user": request.user.id,
                    "team": request.team.id,
                    "email_credential": cred.id,
                    "comm_type": Communication.TYPE_EMAIL,
                    "direction": Communication.DIRECTION_OUTGOING,
                    "email_from": cred.email_address,
                    # Apply template values if not overridden
                    # Note: template.body can contain HTML content which gets mapped to email_body
                    "email_subject": request.data.get(
                        "email_subject", template.subject
                    ),
                    "email_content": request.data.get("email_content", template.body),
                    "email_to": request.data.get("email_to", template.email_to),
                    "email_cc": request.data.get("email_cc", template.email_cc),
                    "email_bcc": request.data.get("email_bcc", template.email_bcc),
                }
            except EmailTemplate.DoesNotExist:
                return Response(
                    {"error": "Template not found"}, status=status.HTTP_404_NOT_FOUND
                )
        else:
            data = {
                **request.data,
                "email_message_id": uuid.uuid4().hex,
                "is_draft": True,
                "user": request.user.id,
                "team": request.team.id,
                "email_credential": cred.id,
                "comm_type": Communication.TYPE_EMAIL,
                "direction": Communication.DIRECTION_OUTGOING,
                "email_from": cred.email_address,
            }

        serializer = self.serializer_class(data=data)
        serializer.is_valid(raise_exception=True)
        draft = serializer.save()

        # If created from template, copy template attachments
        if template_id and template:
            self._copy_template_attachments(template, draft)

        return Response(serializer.data)

    def _copy_template_attachments(self, template, draft):
        """Copy attachments from email template to the draft communication."""
        template_content_type = ContentType.objects.get_for_model(EmailTemplate)
        comm_content_type = ContentType.objects.get_for_model(Communication)

        # Get all document links for the template
        template_links = DocumentLink.objects.filter(
            parent_object_type=template_content_type, parent_object_id=template.id
        )

        # Create corresponding links for the draft
        for link in template_links:
            try:
                DocumentLink.objects.create(
                    document=link.document,
                    parent_object_type=comm_content_type,
                    parent_object_id=draft.id,
                )
            except IntegrityError:
                # Document already linked, skip
                pass

    def update(self, request, *args, **kwargs):
        instance = super().get_queryset().get(pk=kwargs["pk"])
        cred = EmailCredentialNylas.objects.get(
            id=self.request.data["credential"], team=self.request.team
        )
        data = {
            **request.data,
            "email_message_id": uuid.uuid4().hex,
            "is_draft": True,
            "user": request.user.id,
            "team": request.team.id,
            "email_credential": cred.id,
            "comm_type": Communication.TYPE_EMAIL,
            "direction": Communication.DIRECTION_OUTGOING,
            "email_from": cred.email_address,
        }
        serializer = self.serializer_class(instance, data=data)
        serializer.is_valid(raise_exception=True)
        serializer.update(instance, serializer.validated_data)
        return Response(serializer.data)

    @action(detail=False, methods=["post"], url_path="from-template")
    def create_from_template(self, request):
        """Create an email draft from a template, including all attachments."""
        template_id = request.data.get("template_id")
        if not template_id:
            return Response(
                {"error": "template_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            template = EmailTemplate.objects.get(id=template_id, team=request.team)
        except EmailTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found"}, status=status.HTTP_404_NOT_FOUND
            )

        cred = EmailCredentialNylas.objects.get(
            id=request.data["credential"], team=request.team
        )

        # Create draft with template data
        data = {
            "supplier": request.data["supplier"],
            "email_message_id": uuid.uuid4().hex,
            "is_draft": True,
            "user": request.user.id,
            "team": request.team.id,
            "email_credential": cred.id,
            "comm_type": Communication.TYPE_EMAIL,
            "direction": Communication.DIRECTION_OUTGOING,
            "email_from": cred.email_address,
            "email_subject": template.subject,
            "email_content": template.body,
            "email_to": [
                {"email_address": email} for email in (template.email_to or [])
            ],
            "email_cc": [
                {"email_address": email} for email in (template.email_cc or [])
            ],
            "email_bcc": [
                {"email_address": email} for email in (template.email_bcc or [])
            ],
        }

        # Allow overrides from request
        for field in ["email_subject", "email_content"]:
            if field in request.data:
                data[field] = request.data[field]

        # Handle email recipient overrides separately
        for field in ["email_to", "email_cc", "email_bcc"]:
            if field in request.data:
                # Convert to proper format if needed
                if (
                    isinstance(request.data[field], list)
                    and len(request.data[field]) > 0
                ):
                    if isinstance(request.data[field][0], str):
                        data[field] = [
                            {"email_address": email} for email in request.data[field]
                        ]
                    else:
                        data[field] = request.data[field]

        serializer = self.serializer_class(data=data)
        serializer.is_valid(raise_exception=True)
        draft = serializer.save()

        # Copy template attachments
        self._copy_template_attachments(template, draft)

        # Return the draft with attachments included
        response_data = serializer.data
        # Add attachment info to response using AttachmentSummarySerializer
        comm_content_type = ContentType.objects.get_for_model(Communication)
        attachment_links = DocumentLink.objects.filter(
            parent_object_type=comm_content_type, parent_object_id=draft.id
        ).select_related("document")

        # Use AttachmentSummarySerializer for consistency
        documents = [link.document for link in attachment_links]
        response_data["attachments"] = AttachmentSummarySerializer(
            documents, many=True
        ).data

        return Response(response_data)


class SendEmailNylasView(APIView):
    permission_classes = [IsAuthenticated]

    def decode_data_url(self, data_url):
        mimetype = None
        try:
            # encoded for html
            # data:image/jpeg;base64,/9j/4AAQSkZJ ... ICgICAgIA==
            meta, file_data = data_url.split(",", 1)
            mimetype = meta.split(";", 1)[0].strip("data:")
        except ValueError:
            file_data = data_url
        return file_data, mimetype

    def post(self, request):
        team = request.team
        supplier = team.suppliers.filter(id=request.data["supplier_id"]).first()
        purchase_order_id = request.data.get("purchase_order_id")

        if not supplier:
            return self.error_response(
                [
                    {
                        "code": "notFound",
                        "attr": "supplierId",
                        "detail": "No such Supplier known to your team",
                    }
                ]
            )

        cred = EmailCredentialNylas.objects.get(id=request.data["credential_id"])
        if cred.team != request.team:
            return self.error_response(
                [
                    {
                        "code": "unauthorised",
                        "attr": "credentialId",
                        "detail": "This credential is not attached to your team",
                    }
                ]
            )

        # The body can contain HTML content - Nylas automatically handles MIME formatting
        # for both HTML and plain text versions
        message = {
            "subject": request.data["subject"],
            "body": request.data["body"],  # Can be HTML or plain text
            "from": [
                {
                    "email": cred.email_address,
                }
            ],
            "to": [{"email": to_address} for to_address in request.data["to"]],
            "cc": [{"email": cc_address} for cc_address in request.data.get("cc", [])],
            "bcc": [
                {"email": bcc_address} for bcc_address in request.data.get("bcc", [])
            ],
        }

        if "reply_to_id" in request.data:
            """
            Workaround Nylas behavior for replies. Nylas cannot send replies in these circumstances:
                1. The reply subject is changed
                2. The reply is not sent from an address that is in the original email
                3. The reply is from an email that was included only in cc/bcc
            We check if the subject is changed or if the reply sender
            """
            reply_to_comm = team.comms.filter(id=request.data["reply_to_id"]).first()
            from_email = reply_to_comm.email_from
            to_emails = reply_to_comm.email_to.all().values_list(
                "email_address", flat=True
            )

            is_replying_to_sender = from_email in request.data["to"]
            reply_subject_is_same = message["subject"] == reply_to_comm.email_subject
            sender_is_in_original_to_list = cred.email_address in to_emails

            if (
                reply_to_comm
                and is_replying_to_sender
                and reply_subject_is_same
                and sender_is_in_original_to_list
            ):
                message["reply_to_message_id"] = reply_to_comm.email_message_id
        attachments = []
        if "attachments" in request.data:
            attachments += [
                {
                    "filename": att["name"],
                    "content_type": att["type"],
                    "content": self.decode_data_url(att["data"])[0],
                }
                for att in request.data["attachments"]
            ]

        if "doc_attachments" in request.data:
            try:
                docs = [
                    Document.objects.get(uuid=doc_uuid)
                    for doc_uuid in request.data["doc_attachments"]
                ]
                docs = [doc for doc in docs if doc.team == request.team]
            except Document.DoesNotExist:
                return Response(
                    {
                        "error": "Could not find one or more of the documents you attached"
                    },
                    status=400,
                )

            for doc in docs:
                with doc.document.open() as f:
                    content = f.read()
                attachments += [
                    {
                        "filename": doc.name,
                        "content_type": doc.content_type,
                        "content": base64.b64encode(content).decode("ascii"),
                    }
                ]

        if attachments:
            message["attachments"] = attachments

        # If there is a draft, the id is in the request data
        # Delete the draft and replace it with the nylas message
        if request.data.get("id"):
            draft = Communication.objects.filter(id=request.data.get("id"))
            if draft.exists():
                draft.delete()

        try:
            nylas_res = nylas.send_message(cred.grant_id, message)
        except Exception as exc:
            logger.exception("Error sending email", exc=exc)
            return self.error_response(
                [
                    {
                        "code": "email_send_failure",
                        "detail": str(exc),
                    }
                ]
            )

        purchase_order = (
            PurchaseOrder.objects.filter(id=purchase_order_id).first()
            if purchase_order_id
            else None
        )

        # The communication will be sent to pubsub, but we create it here so user can see it in the UI ASAP
        # If the pubsub somehow creates it first though, it will raise an IntegrityError
        try:
            create_communication_and_documents(
                cred,
                supplier,
                nylas_res["data"],
                [purchase_order] if purchase_order else [],
            )
        except IntegrityError as e:
            logger.error(
                "Error creating communication and documents",
                error=e,
                message_id=nylas_res["data"]["id"],
                grant_id=nylas_res["data"]["grant_id"],
            )

        return Response({"sent": True, "message": message})


class EmailThreadViewSet(APIView, TeamOwnerFilteredModelView):
    serializer_class = EmailThreadSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    model = EmailThread

    def get_serializer_class(self):
        if self.action == "retrieve":
            return EmailThreadDetailSerializer
        return EmailThreadSerializer

    def retrieve(self, request, pk=None):
        try:
            email_thread = get_object_or_404(
                EmailThread, thread_id=pk, team=self.request.team
            )
            serializer = self.get_serializer_class()(email_thread)
            return Response(serializer.data)
        except ValidationError as _:
            return Response(
                {"error": "Resource not found or access denied."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def get_queryset(self):
        # This function is used both for emails for POs and for suppliers.
        # We therefore check both possible args
        purchase_order_id = self.request.query_params.get("order_id")
        supplier_id = self.request.query_params.get("supplier_id")
        contact_id = self.request.query_params.get("contact_id")
        # We have a direct way of getting threads, based on purchase order ID
        if purchase_order_id:
            thread_ids = EmailThreadToPurchaseOrderLink.objects.filter(
                purchase_order=purchase_order_id
            ).values_list("email_thread", flat=True)
            threads = super().get_queryset().filter(pk__in=thread_ids)
        # We do not have a direct way of getting threads from supplier - we have
        # to go through the emails themselves
        elif supplier_id:
            thread_ids = (
                Communication.objects.filter(
                    team=self.request.team,
                    supplier=supplier_id,
                )
                .values_list("email_thread", flat=True)
                .distinct()
            )
            threads = super().get_queryset().filter(pk__in=thread_ids)
        # Same as for supplier threads; we need to go through comms
        elif contact_id:
            thread_ids = (
                Communication.objects.filter(
                    contacts__pk=contact_id, team=self.request.team
                )
                .values_list("email_thread", flat=True)
                .distinct()
            )
            threads = super().get_queryset().filter(pk__in=thread_ids)
        # We intentionally do not want to replicate a standard inbox - a view showing all your emails.
        # Therefore, if the FE doesn't filter by PO or supplier, instead of returning
        # all emails, we return none.
        else:
            threads = super().get_queryset()

        latest_comm_time = (
            Communication.objects.filter(email_thread=OuterRef("pk"))
            .order_by("-comm_time")
            .values("comm_time")[:1]
        )

        threads = threads.annotate(
            most_recent_email_time=Subquery(latest_comm_time)
        ).order_by("-most_recent_email_time")
        return threads

    @action(detail=False, methods=["post"])
    def link_po(self, request, thread_id):
        purchase_order_id = request.data.get("purchase_order_id")
        if not purchase_order_id:
            return Response({"detail": "purchase_order_id is required."}, status=400)

        try:
            purchase_order = PurchaseOrder.objects.get(
                id=purchase_order_id, team=self.request.team
            )
        except PurchaseOrder.DoesNotExist:
            return Response(
                {
                    "detail": f"Purchase order with id = {purchase_order_id} was not found."
                },
                status=404,
            )

        try:
            email_thread = super().get_queryset().get(thread_id=thread_id)
        except EmailThread.DoesNotExist:
            return Response(
                {"detail": f"EmailThread with thread_id = {thread_id} was not found."},
                status=404,
            )

        thread_to_po_link, created = (
            EmailThreadToPurchaseOrderLink.objects.get_or_create(
                email_thread=email_thread, purchase_order=purchase_order
            )
        )

        # Get all documents from the email thread's communications
        for communication in email_thread.emails.all():
            document_links = DocumentLink.objects.filter(
                parent_object_type=get_content_type_for_model(Communication),
                parent_object_id=communication.id,
            )

            # Link each document to the purchase order
            for doc_link in document_links:
                DocumentLink.objects.get_or_create(
                    parent_object_type=get_content_type_for_model(PurchaseOrder),
                    parent_object_id=purchase_order.id,
                    document=doc_link.document,
                )

        return Response(
            {
                "detail": f"Purchase order ({purchase_order_id}) linked to email thread ({thread_id}) successfully ({thread_to_po_link.pk})."
            },
            status=200,
        )


class EmailSendSuggestionView(APIView):
    """
    Returns hints for email addresses the user may
    want to send to or include in cc/bcc headers.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        team = request.team
        extra_filters = {}
        if "supplier_id" in request.GET:
            extra_filters["supplier_id"] = request.GET["supplier_id"]

        contacts = SupplierContact.objects.filter(
            supplier__team=team,
            **extra_filters,
        ).values_list("email", flat=True)
        connected = EmailCredentialNylas.objects.filter(
            team=team,
            status=NylasGrantStatus.VALID,
        ).values_list("email_address", flat=True)

        return Response({"suggestions": set(contacts).union(connected)})


class CompleteNylasOAuthView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        code = request.data["code"]
        response = nylas.connect_mailbox_with_code(code, request.user)

        if not response:
            return self.single_error_response(
                status=status.HTTP_400_BAD_REQUEST,
                detail="Bad connection attempt",
                attr="error",
            )
        if response.status_code != 200:
            return Response(response.data, status=response.status_code)

        data = response.json()

        try:
            cred = EmailCredentialNylas.objects.create(
                user=request.user,
                team=request.team,
                email_address=data["email"],
                provider=data["provider"],
                scope=data["scope"],
                access_token=data["access_token"],
                refresh_token=data["refresh_token"],
                expires_at=timezone.now() + timedelta(seconds=data["expires_in"]),
                grant_id=data["grant_id"],
            )
        except Exception as exc:
            logger.exception("Error creating Nylas credential", exc=exc)
            return Response({"error": "Could not create Nylas credential"}, status=500)

        backfill_inbox.delay(cred.id)
        return Response(EmailCredentialNylasSerializer(cred).data)


class GetEmailTemplatesView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        team_id = request.GET.get("team_id")
        supplier_id = request.GET.get("supplier_id", None)

        logger.info(
            "GetEmailTemplatesView.get called",
            team_id=team_id,
            supplier_id=supplier_id,
            user_id=request.user.id if request.user else None,
            user_team_id=request.team.id if hasattr(request, "team") else None,
        )

        if not team_id:
            logger.warning("team_id parameter missing in request")
            return self.single_error_response(
                status=status.HTTP_400_BAD_REQUEST,
                detail="team_id parameter is required",
                attr="error",
            )

        # Staff or superusers can access templates for any team
        if request.user.is_staff or request.user.is_superuser:
            pass  # Allow access to any team's templates
        # Regular users can only access their own team's templates
        elif not hasattr(request, "team") or team_id != str(request.team.id):
            logger.warning(
                "Team ID mismatch or missing team attribute",
                requested_team_id=team_id,
                user_team_id=request.team.id if hasattr(request, "team") else None,
            )
            return self.single_error_response(
                status=status.HTTP_403_FORBIDDEN,
                detail="You can only access templates for your team",
                attr="error",
            )

        templates = EmailTemplate.objects.filter(team_id=team_id)
        if supplier_id:
            # Show both team-level and supplier-specific templates
            templates = templates.filter(
                Q(supplier_id=supplier_id) | Q(supplier__isnull=True)
            )

        # Note: Can't prefetch generic relations, so we'll let the serializer handle it

        # Use the serializer to include attachment information
        serializer = EmailTemplateSerializer(templates, many=True)
        data = serializer.data

        logger.info(
            "GetEmailTemplatesView returning templates",
            template_count=len(data),
            team_id=team_id,
        )

        return Response(data)


class RenderEmailTemplateView(APIView):
    """
    API view for server-side email template rendering.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Render an email template with entity data.

        Expected request body:
        {
            "template_id": 123,
            "entities": {"purchase_order": 456, "supplier": 789},
            "additional_context": {"custom_variable": "custom_value"}
        }
        """
        from didero.emails.template_renderer import email_template_renderer

        logger.info(
            "Template render request",
            template_id=request.data.get("template_id"),
            entities=request.data.get("entities", {}),
            team_id=request.team.id,
            user_id=request.user.id,
        )

        # Validate required fields
        if not request.data:
            return Response(
                {"error": "Request body is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        template_id = request.data.get("template_id")
        if not template_id:
            return Response(
                {"error": "template_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            template_id = int(template_id)
            if template_id <= 0:
                raise ValueError("Template ID must be positive")
        except (ValueError, TypeError):
            return Response(
                {"error": "template_id must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate optional fields
        entities = request.data.get("entities", {})
        if entities is not None and not isinstance(entities, dict):
            return Response(
                {"error": "entities must be a dictionary"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        additional_context = request.data.get("additional_context", {})
        if additional_context is not None and not isinstance(additional_context, dict):
            return Response(
                {"error": "additional_context must be a dictionary"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Render template (this never raises exceptions)
        rendered_template = email_template_renderer.render_template(
            template_id=template_id,
            team_id=request.team.id,
            user=request.user,
            entities=entities,
            additional_context=additional_context,
        )

        logger.info(
            "Template render response",
            template_id=template_id,
            team_id=request.team.id,
            error_count=len(rendered_template.get("errors", [])),
            has_subject=bool(rendered_template.get("subject")),
            has_body=bool(rendered_template.get("body")),
        )

        return Response(rendered_template, status=status.HTTP_200_OK)


class GetTemplateVariablesView(APIView):
    """
    API view to get available template variables for documentation.

    This helps frontend developers know what variables are available
    when creating or editing templates.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get all available template variables for drag-and-drop template builder.
        """
        logger.info(
            "Template variables request",
            team_id=request.team.id,
            user_id=request.user.id,
        )

        try:
            variables = self._get_template_variables()

            logger.info(
                "Template variables response",
                team_id=request.team.id,
                variable_count=len(variables),
            )

            return Response({"variables": variables})

        except Exception as e:
            logger.error(
                "Failed to load template variables", error=str(e), exc_info=True
            )
            return Response(
                {"error": "Failed to load template variables"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _get_template_variables(self):
        """
        Get explicit list of useful template variables.

        This replaces the complex recursive approach with a simple, maintainable list
        of variables that are actually useful in email templates.
        """
        return [
            # Purchase Order variables
            {
                "label": "PO Number",
                "template": "{{ purchase_order.po_number }}",
                "category": "Purchase Order",
            },
            {
                "label": "Order Status",
                "template": "{{ purchase_order.order_status }}",
                "category": "Purchase Order",
            },
            {
                "label": "Total Cost",
                "template": "{{ purchase_order.total_cost }}",
                "category": "Purchase Order",
            },
            {
                "label": "PO Created Date",
                "template": "{{ purchase_order.created_at }}",
                "category": "Purchase Order",
            },
            {
                "label": "PO Modified Date",
                "template": "{{ purchase_order.modified_at }}",
                "category": "Purchase Order",
            },
            {
                "label": "Requested Date",
                "template": "{{ purchase_order.requested_date }}",
                "category": "Purchase Order",
            },
            {
                "label": "Placed By",
                "template": "{{ purchase_order.placed_by.display_name }}",
                "category": "Purchase Order",
            },
            {
                "label": "PO Line Items",
                "template": "{{ purchase_order.po_line_items }}",
                "category": "Purchase Order",
            },
            {
                "label": "Shipping Address Line 1",
                "template": "{{ purchase_order.shipping_address.line_1 }}",
                "category": "Purchase Order",
            },
            {
                "label": "Shipping Address Line 2",
                "template": "{{ purchase_order.shipping_address.line_2 }}",
                "category": "Purchase Order",
            },
            {
                "label": "Shipping City",
                "template": "{{ purchase_order.shipping_address.city }}",
                "category": "Purchase Order",
            },
            {
                "label": "Shipping State",
                "template": "{{ purchase_order.shipping_address.state_or_province }}",
                "category": "Purchase Order",
            },
            {
                "label": "Shipping Postal Code",
                "template": "{{ purchase_order.shipping_address.postal_code }}",
                "category": "Purchase Order",
            },
            {
                "label": "Shipping Country",
                "template": "{{ purchase_order.shipping_address.country }}",
                "category": "Purchase Order",
            },
            # Supplier variables
            {
                "label": "Supplier Name",
                "template": "{{ supplier.name }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Description",
                "template": "{{ supplier.description }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Default Email",
                "template": "{{ supplier.default_email }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Website",
                "template": "{{ supplier.website_url }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Payment Terms",
                "template": "{{ supplier.payment_terms }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Shipping Method",
                "template": "{{ supplier.shipping_method }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Notes",
                "template": "{{ supplier.notes }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Address Line 1",
                "template": "{{ supplier.supplier_address.line_1 }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Address Line 2",
                "template": "{{ supplier.supplier_address.line_2 }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier City",
                "template": "{{ supplier.supplier_address.city }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier State",
                "template": "{{ supplier.supplier_address.state_or_province }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Postal Code",
                "template": "{{ supplier.supplier_address.postal_code }}",
                "category": "Supplier",
            },
            {
                "label": "Supplier Country",
                "template": "{{ supplier.supplier_address.country }}",
                "category": "Supplier",
            },
            {
                "label": "Contact Name",
                "template": "{{ supplier.default_contact_detail.name }}",
                "category": "Supplier",
            },
            {
                "label": "Contact Email",
                "template": "{{ supplier.default_contact_detail.email }}",
                "category": "Supplier",
            },
            {
                "label": "Contact Phone",
                "template": "{{ supplier.default_contact_detail.phone }}",
                "category": "Supplier",
            },
            # User variables (always available)
            {
                "label": "User Name",
                "template": "{{ user.display_name }}",
                "category": "User",
            },
            {
                "label": "User First Name",
                "template": "{{ user.first_name }}",
                "category": "User",
            },
            {
                "label": "User Last Name",
                "template": "{{ user.last_name }}",
                "category": "User",
            },
            {"label": "User Email", "template": "{{ user.email }}", "category": "User"},
            # Team variables (always available)
            {"label": "Team Name", "template": "{{ team.name }}", "category": "Team"},
            {
                "label": "Team Email",
                "template": "{{ team.email_from }}",
                "category": "Team",
            },
            {"label": "Team Phone", "template": "{{ team.phone }}", "category": "Team"},
            {
                "label": "Team Address Line 1",
                "template": "{{ team.default_address.line_1 }}",
                "category": "Team",
            },
            {
                "label": "Team Address Line 2",
                "template": "{{ team.default_address.line_2 }}",
                "category": "Team",
            },
            {
                "label": "Team City",
                "template": "{{ team.default_address.city }}",
                "category": "Team",
            },
            {
                "label": "Team State",
                "template": "{{ team.default_address.state_or_province }}",
                "category": "Team",
            },
            {
                "label": "Team Postal Code",
                "template": "{{ team.default_address.postal_code }}",
                "category": "Team",
            },
            {
                "label": "Team Country",
                "template": "{{ team.default_address.country }}",
                "category": "Team",
            },
            {
                "label": "Default Requestor",
                "template": "{{ team.default_requestor.display_name }}",
                "category": "Team",
            },
            # Global variables (always available)
            {
                "label": "Current Date",
                "template": "{{ current_date }}",
                "category": "Global",
            },
        ]


class GetEmailTemplateByIdView(APIView):
    """
    API view to get a specific email template by ID for editing.

    Returns the raw unrendered template data including subject, body,
    email recipients, etc. This is used by the frontend for template editing.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, template_id):
        """
        Get a specific email template by ID for editing.

        Returns:
        {
            "id": 123,
            "template_name": "PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "PO {{ purchase_order.po_number }} Confirmation",
            "body": "Dear {{ supplier.name }}, your PO is confirmed...",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "team_id": 1,
            "supplier_id": null,
            "created_at": "2024-01-01T00:00:00Z",
            "modified_at": "2024-01-01T00:00:00Z"
        }
        """
        logger.info(
            "Get template by ID request",
            template_id=template_id,
            team_id=request.team.id,
            user_id=request.user.id,
        )

        try:
            template_id = int(template_id)
            if template_id <= 0:
                raise ValueError("Template ID must be positive")
        except (ValueError, TypeError):
            return Response(
                {"error": "template_id must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            template = EmailTemplate.objects.get(
                id=template_id, team_id=request.team.id
            )

            template_data = {
                "id": template.id,
                "template_name": template.template_name,
                "email_type": template.email_type,
                "subject": template.subject,
                "body": template.body,
                "email_to": template.email_to or [],
                "email_cc": template.email_cc or [],
                "email_bcc": template.email_bcc or [],
                "team_id": template.team_id,
                "supplier_id": template.supplier_id,
                "created_at": template.created_at,
                "modified_at": template.modified_at,
            }

            logger.info(
                "Get template by ID response",
                template_id=template_id,
                template_name=template.template_name,
                team_id=request.team.id,
            )

            return Response(template_data, status=status.HTTP_200_OK)

        except EmailTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found or access denied"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            logger.error(
                "Failed to retrieve email template",
                template_id=template_id,
                team_id=request.team.id,
                error=str(e),
            )
            return Response(
                {"error": "Failed to retrieve template"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CreateEmailTemplateView(APIView):
    """
    API view to create a new email template.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Create a new email template.

        Expected request body:
        {
            "template_name": "PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "PO {{ purchase_order.po_number }} Confirmation",
            "body": "<p>Dear {{ supplier.name }}, your PO is confirmed...</p>",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "supplier_id": null  // optional
        }

        Returns:
        {
            "id": 123,
            "template_name": "PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "PO {{ purchase_order.po_number }} Confirmation",
            "body": "<p>Dear {{ supplier.name }}, your PO is confirmed...</p>",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "team_id": 1,
            "supplier_id": null,
            "created_at": "2024-01-01T00:00:00Z",
            "modified_at": "2024-01-01T00:00:00Z"
        }
        """
        logger.info(
            "Create template request",
            template_name=request.data.get("template_name"),
            team_id=request.team.id,
            user_id=request.user.id,
        )

        try:
            # Validate required fields
            required_fields = ["template_name", "subject", "body"]
            for field in required_fields:
                if not request.data.get(field):
                    return Response(
                        {"error": f"{field} is required"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Validate supplier_id if provided
            supplier_id = request.data.get("supplier_id")
            if supplier_id:
                try:
                    supplier_id = int(supplier_id)
                    if not Supplier.objects.filter(
                        id=supplier_id, team=request.team
                    ).exists():
                        return Response(
                            {"error": "Supplier not found or access denied"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                except (ValueError, TypeError):
                    return Response(
                        {"error": "supplier_id must be a valid integer"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Create the template
            template_data = {
                "team": request.team,
                "template_name": request.data["template_name"],
                "subject": request.data["subject"],
                "body": request.data["body"],
                "email_type": request.data.get("email_type"),
                "email_to": request.data.get("email_to", []),
                "email_cc": request.data.get("email_cc", []),
                "email_bcc": request.data.get("email_bcc", []),
                "supplier_id": supplier_id,
            }

            template = EmailTemplate.objects.create(**template_data)

            response_data = {
                "id": template.id,
                "template_name": template.template_name,
                "email_type": template.email_type,
                "subject": template.subject,
                "body": template.body,
                "email_to": template.email_to or [],
                "email_cc": template.email_cc or [],
                "email_bcc": template.email_bcc or [],
                "team_id": template.team_id,
                "supplier_id": template.supplier_id,
                "created_at": template.created_at,
                "modified_at": template.modified_at,
            }

            logger.info(
                "Create template response",
                template_id=template.id,
                template_name=template.template_name,
                team_id=request.team.id,
            )

            return Response(response_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(
                "Failed to create email template", team_id=request.team.id, error=str(e)
            )
            return Response(
                {"error": "Failed to create template"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UpdateEmailTemplateView(APIView):
    """
    API view to update an existing email template.
    """

    permission_classes = [IsAuthenticated]

    def put(self, request, template_id):
        """
        Update an existing email template.

        Expected request body:
        {
            "template_name": "Updated PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "Updated PO {{ purchase_order.po_number }} Confirmation",
            "body": "<p>Dear {{ supplier.name }}, your updated PO...</p>",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "supplier_id": null
        }

        Returns: Same format as CreateEmailTemplateView
        """
        logger.info(
            "Update template request",
            template_id=template_id,
            template_name=request.data.get("template_name"),
            team_id=request.team.id,
            user_id=request.user.id,
        )

        try:
            template_id = int(template_id)
            if template_id <= 0:
                raise ValueError("Template ID must be positive")
        except (ValueError, TypeError):
            return Response(
                {"error": "template_id must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            template = EmailTemplate.objects.get(
                id=template_id, team_id=request.team.id
            )
        except EmailTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found or access denied"},
                status=status.HTTP_404_NOT_FOUND,
            )

        try:
            # Validate required fields
            required_fields = ["template_name", "subject", "body"]
            for field in required_fields:
                if not request.data.get(field):
                    return Response(
                        {"error": f"{field} is required"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Validate supplier_id if provided
            supplier_id = request.data.get("supplier_id")
            if supplier_id:
                try:
                    supplier_id = int(supplier_id)
                    if not Supplier.objects.filter(
                        id=supplier_id, team=request.team
                    ).exists():
                        return Response(
                            {"error": "Supplier not found or access denied"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                except (ValueError, TypeError):
                    return Response(
                        {"error": "supplier_id must be a valid integer"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Update the template
            template.template_name = request.data["template_name"]
            template.subject = request.data["subject"]
            template.body = request.data["body"]
            template.email_type = request.data.get("email_type")
            template.email_to = request.data.get("email_to", [])
            template.email_cc = request.data.get("email_cc", [])
            template.email_bcc = request.data.get("email_bcc", [])
            template.supplier_id = supplier_id
            template.save()

            response_data = {
                "id": template.id,
                "template_name": template.template_name,
                "email_type": template.email_type,
                "subject": template.subject,
                "body": template.body,
                "email_to": template.email_to or [],
                "email_cc": template.email_cc or [],
                "email_bcc": template.email_bcc or [],
                "team_id": template.team_id,
                "supplier_id": template.supplier_id,
                "created_at": template.created_at,
                "modified_at": template.modified_at,
            }

            logger.info(
                "Update template response",
                template_id=template_id,
                template_name=template.template_name,
                team_id=request.team.id,
            )

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(
                "Failed to update email template",
                template_id=template_id,
                team_id=request.team.id,
                error=str(e),
            )
            return Response(
                {"error": "Failed to update template"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DeleteEmailTemplateView(APIView):
    """
    API view to delete an email template.
    """

    permission_classes = [IsAuthenticated]

    def delete(self, request, template_id):
        """
        Delete an email template.

        Returns:
        {
            "message": "Template deleted successfully"
        }
        """
        logger.info(
            "Delete template request",
            template_id=template_id,
            team_id=request.team.id,
            user_id=request.user.id,
        )

        try:
            template_id = int(template_id)
            if template_id <= 0:
                raise ValueError("Template ID must be positive")
        except (ValueError, TypeError):
            return Response(
                {"error": "template_id must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            template = EmailTemplate.objects.get(
                id=template_id, team_id=request.team.id
            )
            template_name = template.template_name
            template.delete()

            logger.info(
                "Delete template response",
                template_id=template_id,
                template_name=template_name,
                team_id=request.team.id,
            )

            return Response(
                {"message": "Template deleted successfully"}, status=status.HTTP_200_OK
            )

        except EmailTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found or access denied"},
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            logger.error(
                "Failed to delete email template",
                template_id=template_id,
                team_id=request.team.id,
                error=str(e),
            )
            return Response(
                {"error": "Failed to delete template"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
