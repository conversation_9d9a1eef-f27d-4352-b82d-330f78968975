from random import randint
from typing import TYPE_CHECKING

from admin_extra_buttons.api import ExtraButtonsMixin, button
from django.contrib import admin, messages
from django.http import HttpRequest
from django.shortcuts import redirect
from django.utils.timezone import now

from didero.addresses.models import Address
from didero.insights.models import ItemGeoInsight

if TYPE_CHECKING:
    TypedItemGeoInsightAdmin = admin.ModelAdmin[ItemGeoInsight]
else:
    TypedItemGeoInsightAdmin = admin.ModelAdmin


class ItemGeoInsightAdmin(ExtraButtonsMixin, TypedItemGeoInsightAdmin):  # type: ignore
    autocomplete_fields = [
        "team",
        "supplier",
        "item",
        "sender_address",
        "shipping_address",
    ]
    list_display = (
        "team",
        "supplier",
        "item",
        "sender_address",
        "shipping_address",
        "item_number",
        "item_hs_code",
        "supplier_name",
        "sender_address_country",
        "sender_address_latitude",
        "sender_address_longitude",
        "shipping_address_country",
        "shipping_address_latitude",
        "shipping_address_longitude",
        "quantity",
        "total_cost",
        "insight_date",
    )
    # readonly_fields = list_display
    search_fields = ["supplier_name", "item_number", "item_hs_code"]

    # TODO: remove comments from this before releasing to the masses
    # don't allow anyone to create, edit or delete
    # def has_add_permission(self, request):
    #     return False

    # def has_change_permission(self, request, obj=None):
    #     return False

    # def has_delete_permission(self, request, obj=None):
    #     return False

    @button(
        label="Create Test Insights (for your team)",
    )
    def create_test_insights(self, request: HttpRequest):
        team = request.user.teams.first()  # type: ignore
        insights = []
        if not team:
            self.message_user(
                request,
                "User is not associated with any team.",
                level=messages.ERROR,
            )
            return redirect("..")

        # Create a ItemGeoInsight per supplier
        for supplier in team.suppliers.all():
            item = supplier.items.first()
            sender_address = Address.objects.filter(
                team=team, supplier=supplier
            ).first()
            shipping_address = Address.objects.filter(team=team, supplier=None).first()

            if not all([item, supplier, sender_address, shipping_address]):
                continue

            # to fix typing below
            assert sender_address is not None
            assert shipping_address is not None

            # Create test ItemGeoInsight instances
            insights.append(
                ItemGeoInsight.objects.update_or_create(
                    team=team,
                    item=item,
                    supplier=supplier,
                    sender_address=sender_address,
                    shipping_address=shipping_address,
                    insight_date=now().date(),
                    defaults={
                        "item_description": item.description,
                        "item_number": item.item_number,
                        "item_hs_code": item.hs_code,
                        "supplier_name": supplier.name,
                        "sender_address_country": sender_address.country,
                        "sender_address_latitude": sender_address.latitude,
                        "sender_address_longitude": sender_address.longitude,
                        "shipping_address_country": shipping_address.country,
                        "shipping_address_latitude": shipping_address.latitude,
                        "shipping_address_longitude": shipping_address.longitude,
                        "quantity": randint(1, 100),
                        "total_cost": item.price,
                    },
                )
            )

        self.message_user(
            request,
            f"{len(insights)} test ItemGeoInsight(s) created successfully.",
            level=messages.SUCCESS,
        )
        return redirect("..")


admin.site.register(ItemGeoInsight, ItemGeoInsightAdmin)
