from datetime import date as Date

import structlog

from didero.activity_log.models import PurchaseOrderStatusUpdate
from didero.insights.models import ItemGeoInsight
from didero.orders.models import PurchaseOrder
from didero.orders.schemas import ORDER_STATUSES_TERMINAL

logger = structlog.get_logger(__name__)


def generate_item_geo_insights(team_id: int, date: Date = Date.today()) -> int:
    # Get all PO terminal status updates that happened today
    po_pks = (
        PurchaseOrderStatusUpdate.objects.filter(
            purchase_order__team_id=team_id,
            new_status__in=ORDER_STATUSES_TERMINAL,
            created_at__date=date,
        )
        .values_list("purchase_order__pk", flat=True)
        .distinct()
    )

    # Get all Purchase Orders for the team and date
    orders = PurchaseOrder.objects.filter(pk__in=po_pks)

    if date == Date.today():
        # if we're looking for the current day's data, do this extra check for po status:
        # the po_pks might contain POs that were in terminal state at some point today, but moved later in the day to a different state
        orders.filter(order_status__in=ORDER_STATUSES_TERMINAL)

    # Dictionary to hold item insights
    item_insights = {}

    # Go through the orders and collect data
    for order in orders:
        supplier = order.supplier
        sender_address = order.sender_address
        shipping_address = order.shipping_address
        order_items = order.items.all()

        for order_item in order_items:
            item = order_item.item
            quantity = order_item.quantity
            total_price = order_item.price * order_item.quantity

            # Create a unique key for the item-supplier-address combination
            key = (item.id, supplier.id, sender_address.id, shipping_address.id)

            if key not in item_insights:
                item_insights[key] = {
                    "item": item,
                    "supplier": supplier,
                    "sender_address": sender_address,
                    "shipping_address": shipping_address,
                    "quantity": 0,
                    "total_cost": 0,
                }

            item_insights[key]["quantity"] += quantity
            item_insights[key]["total_cost"] += total_price

    # Create insights using the data from above
    generated_insights = []
    for data in item_insights.values():
        try:
            insight = ItemGeoInsight.objects.update_or_create(
                team_id=team_id,
                item=data["item"],
                supplier=data["supplier"],
                sender_address=data["sender_address"],
                shipping_address=data["shipping_address"],
                insight_date=date,
                defaults={
                    "item_description": data["item"].description,
                    "item_number": data["item"].item_number,
                    "item_hs_code": data["item"].hs_code,
                    "supplier_name": data["supplier"].name,
                    "sender_address_country": data["sender_address"].country,
                    "sender_address_latitude": data["sender_address"].latitude,
                    "sender_address_longitude": data["sender_address"].longitude,
                    "shipping_address_country": data["shipping_address"].country,
                    "shipping_address_latitude": data["shipping_address"].latitude,
                    "shipping_address_longitude": data["shipping_address"].longitude,
                    "quantity": data["quantity"],
                    "total_cost": data["total_cost"],
                },
            )
            generated_insights.append(insight)
            logger.info(f"Generated insight for {key, date}")
        except Exception as e:
            logger.info(
                f"Error while generating_item_geo_insights for item {item}: {e}"
            )
    logger.info(
        f"Completed generating insights for team {team_id} ({len(generated_insights)} insights generated)"
    )
    return len(generated_insights)
