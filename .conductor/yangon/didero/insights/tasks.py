import structlog
from celery import shared_task
from celery_singleton import Singleton

from didero.insights.utils import generate_item_geo_insights
from didero.users.models.team_models import Team

logger = structlog.get_logger(__name__)

LOCK_EXPIRE = 60 * 60  # 1 hour


@shared_task(base=Singleton, lock_expiry=LOCK_EXPIRE, queue="periodic_tasks")
def create_daily_item_geo_insights():
    logger.info("Calculating daily ItemGeoInsight(s)")
    for team in Team.objects.all():
        logger.info(f"Generating daily ItemGeoInsight(s) for team {team.id}")
        generate_item_geo_insights(team.id)
    return True, None
