from django.urls import path
from rest_framework.routers import DefaultRouter

from didero.insights.views import ItemGeoInsightViewset, NearbyItemGeoInsightViewset

router = DefaultRouter()

urlpatterns = [
    path(
        "api/insights/item_geo",
        ItemGeoInsightViewset.as_view({"get": "list"}),
        name="insights-item-geo-list",
    ),
    path(
        "api/insights/item_geo/<int:pk>",
        ItemGeoInsightViewset.as_view(
            {
                "get": "retrieve",
            }
        ),
        name="insights-item-geo-detail",
    ),
    path(
        "api/insights/item_geo/nearby",
        NearbyItemGeoInsightViewset.as_view(
            {
                "get": "get_nearby_insights",
            }
        ),
        name="insights-item-geo-nearby",
    ),
]
