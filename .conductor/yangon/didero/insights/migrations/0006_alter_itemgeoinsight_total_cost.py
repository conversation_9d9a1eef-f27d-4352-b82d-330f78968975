# Generated by Django 4.2.7 on 2024-12-02 22:23

from decimal import Decimal

import djmoney.models.fields
import djmoney.models.validators
from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        (
            "insights",
            "0005_remove_itemgeoinsight_unique_team_item_supplier_address_date_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="itemgeoinsight",
            name="total_cost",
            field=djmoney.models.fields.MoneyField(
                decimal_places=2,
                default=Decimal("0.00"),
                default_currency="USD",
                max_digits=14,
                validators=[djmoney.models.validators.MinMoneyValidator(0)],
            ),
        ),
    ]
