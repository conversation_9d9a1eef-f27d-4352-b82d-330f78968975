# Generated by Django 4.2.7 on 2024-11-26 17:32

import django.db.models.deletion
import django_countries.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("addresses", "0010_address_latitude_address_longitude"),
        ("insights", "0004_alter_itemgeoinsight_insight_date"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="itemgeoinsight",
            name="unique_team_item_supplier_address_date",
        ),
        migrations.RemoveIndex(
            model_name="itemgeoinsight",
            name="insights_it_address_1e70f0_idx",
        ),
        migrations.RemoveIndex(
            model_name="itemgeoinsight",
            name="insights_it_address_2385b7_idx",
        ),
        migrations.RemoveIndex(
            model_name="itemgeoinsight",
            name="insights_it_address_469977_idx",
        ),
        migrations.RemoveIndex(
            model_name="itemgeoinsight",
            name="insights_it_address_92cf8b_idx",
        ),
        migrations.RenameField(
            model_name="itemgeoinsight",
            old_name="address_country",
            new_name="shipping_address_country",
        ),
        migrations.RenameField(
            model_name="itemgeoinsight",
            old_name="address_latitude",
            new_name="shipping_address_latitude",
        ),
        migrations.RenameField(
            model_name="itemgeoinsight",
            old_name="address_longitude",
            new_name="shipping_address_longitude",
        ),
        migrations.RemoveField(
            model_name="itemgeoinsight",
            name="address",
        ),
        migrations.AddField(
            model_name="itemgeoinsight",
            name="sender_address",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="item_sent_from",
                to="addresses.address",
            ),
        ),
        migrations.AddField(
            model_name="itemgeoinsight",
            name="sender_address_country",
            field=django_countries.fields.CountryField(default="US", max_length=2),
        ),
        migrations.AddField(
            model_name="itemgeoinsight",
            name="sender_address_latitude",
            field=models.DecimalField(
                blank=True, decimal_places=6, max_digits=9, null=True
            ),
        ),
        migrations.AddField(
            model_name="itemgeoinsight",
            name="sender_address_longitude",
            field=models.DecimalField(
                blank=True, decimal_places=6, max_digits=9, null=True
            ),
        ),
        migrations.AddField(
            model_name="itemgeoinsight",
            name="shipping_address",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="item_shipped_to",
                to="addresses.address",
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["sender_address"], name="insights_it_sender__f34763_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["shipping_address"], name="insights_it_shippin_e7230d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["sender_address_country"], name="insights_it_sender__66b8ae_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["sender_address_latitude"],
                name="insights_it_sender__c0eaf7_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["sender_address_longitude"],
                name="insights_it_sender__bcaad7_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["shipping_address_country"],
                name="insights_it_shippin_ab3fd7_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["shipping_address_latitude"],
                name="insights_it_shippin_ecea28_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["shipping_address_longitude"],
                name="insights_it_shippin_b81ace_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="itemgeoinsight",
            constraint=models.UniqueConstraint(
                fields=(
                    "team",
                    "item",
                    "supplier",
                    "sender_address",
                    "shipping_address",
                    "insight_date",
                ),
                name="unique_team_item_supplier_address_date",
            ),
        ),
    ]
