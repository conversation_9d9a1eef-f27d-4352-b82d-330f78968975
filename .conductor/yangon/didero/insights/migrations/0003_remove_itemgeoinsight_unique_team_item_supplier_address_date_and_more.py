# Generated by Django 4.2.7 on 2024-11-25 22:32

import datetime

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("insights", "0002_itemgeoinsight_address_country_and_more"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="itemgeoinsight",
            name="unique_team_item_supplier_address_date",
        ),
        migrations.AddField(
            model_name="itemgeoinsight",
            name="insight_date",
            field=models.DateField(
                blank=True,
                default=datetime.date(2024, 11, 25),
                help_text="Date for which the insight was calculated for",
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["insight_date"], name="insights_it_insight_613c83_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="itemgeoinsight",
            constraint=models.UniqueConstraint(
                fields=("team", "item", "supplier", "address", "insight_date"),
                name="unique_team_item_supplier_address_date",
            ),
        ),
    ]
