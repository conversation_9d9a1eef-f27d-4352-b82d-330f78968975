# Generated by Django 4.2.7 on 2024-11-26 16:31

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "insights",
            "0003_remove_itemgeoinsight_unique_team_item_supplier_address_date_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="itemgeoinsight",
            name="insight_date",
            field=models.DateField(
                blank=True,
                default=django.utils.timezone.now,
                help_text="Date for which the insight was calculated for",
            ),
        ),
    ]
