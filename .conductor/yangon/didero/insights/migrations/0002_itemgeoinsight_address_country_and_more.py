# Generated by Django 4.2.7 on 2024-11-21 22:40
import django_countries.fields
import structlog
from django.db import migrations, models

logger = structlog.get_logger(__name__)


def add_country_item_description_to_item_geo_insights(apps, schema_editor):
    ItemGeoInsight = apps.get_model("insights", "ItemGeoInsight")

    for insight in ItemGeoInsight.objects.all():
        insight.address_country = insight.address.country.code
        insight.item_description = insight.item.description
        insight.save()
        logger.info(
            f"Upated country for insight {insight.pk} from 'US' to '{insight.address.country.code}'"
        )


def revert_country_to_us_item_description_empty_for_item_geo_insights(
    apps, schema_editor
):
    ItemGeoInsight = apps.get_model("insights", "ItemGeoInsight")

    for insight in ItemGeoInsight.objects.exclude(address_country="US").all():
        original_country = insight.address.country.code
        insight.address_country = "US"
        insight.item_description = ""
        insight.save()
        logger.info(
            f"Upated country for insight {insight.pk} from '{original_country}' to 'US'"
        )


class Migration(migrations.Migration):
    dependencies = [
        ("insights", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="itemgeoinsight",
            name="address_country",
            field=django_countries.fields.CountryField(default="US", max_length=2),
        ),
        migrations.AddField(
            model_name="itemgeoinsight",
            name="item_description",
            field=models.TextField(default=""),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["address_country"], name="insights_it_address_92cf8b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="itemgeoinsight",
            index=models.Index(
                fields=["item_description"], name="insights_it_item_de_c198b6_idx"
            ),
        ),
        migrations.RunPython(
            add_country_item_description_to_item_geo_insights,
            reverse_code=revert_country_to_us_item_description_empty_for_item_geo_insights,
        ),
    ]
