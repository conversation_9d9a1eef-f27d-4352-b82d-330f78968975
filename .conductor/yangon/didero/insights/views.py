from django.db.models import Q
from django_countries.fields import CountryField
from django_filters import rest_framework as filters
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.filters import (
    <PERSON><PERSON>ilter,
    ChoiceInFilter,
    NullsLastOrderingFilter,
    NumberInFilter,
)
from didero.insights.models import ItemGeoInsight
from didero.insights.serializers import ItemGeoInsightSerializer
from didero.views import (
    APIView,
    PagesPagination,
    TeamOwnerFilteredModelView,
)


class ItemGeoInsightFilter(BaseFilter):
    ids = NumberInFilter(field_name="id")
    supplier_ids = NumberInFilter(field_name="supplier_id")
    supplier_name = filters.CharFilter(
        field_name="supplier_name", lookup_expr="icontains"
    )
    item_ids = filters.BaseInFilter(field_name="item_id")
    item_description = filters.CharFilter(
        field_name="item_description", lookup_expr="icontains"
    )
    item_number = filters.CharFilter(field_name="item_number", lookup_expr="icontains")
    item_hs_code = filters.CharFilter(
        field_name="item_hs_code", lookup_expr="icontains"
    )
    item_hs_codes = filters.CharFilter(method="filter_item_hs_codes")
    sender_address_country_codes = ChoiceInFilter(
        field_name="sender_address_country",
        choices=CountryField().choices,
    )
    shipping_address_country_codes = ChoiceInFilter(
        field_name="shipping_address_country",
        choices=CountryField().choices,
    )
    quantity_min = filters.NumberFilter(field_name="quantity", lookup_expr="gte")
    quantity_max = filters.NumberFilter(field_name="quantity", lookup_expr="lte")
    total_cost_min = filters.NumberFilter(field_name="total_cost", lookup_expr="gte")
    total_cost_max = filters.NumberFilter(field_name="total_cost", lookup_expr="lte")
    insight_date_after = filters.DateFilter(field_name="created_at", lookup_expr="gte")
    insight_date_before = filters.DateFilter(field_name="created_at", lookup_expr="lte")

    class Meta:
        model = ItemGeoInsight
        fields = {
            "id": ["exact"],
            "supplier": ["exact"],
            "item": ["exact"],
            "team": ["exact"],
            "sender_address": ["exact"],
            "sender_address_latitude": ["exact"],
            "sender_address_longitude": ["exact"],
            "shipping_address": ["exact"],
            "shipping_address_latitude": ["exact"],
            "shipping_address_longitude": ["exact"],
            "quantity": ["exact"],
            "total_cost": ["exact"],
        }

    def multi_field_search(self, queryset, name, value):
        return queryset.filter(
            Q(supplier__name__icontains=value)
            | Q(item__description__icontains=value)
            | Q(item_hs_code__icontains=value)
            | Q(item_number__icontains=value)
            | Q(sender_address__country__icontains=value)
            | Q(shipping_address__country__icontains=value)
            | Q(quantity=value)
            | Q(total_cost__icontains=value)
        ).distinct()

    @property
    def qs(self):
        """
        This property ensures that the queryset returned by the filter is distinct,
        solving the problem of duplicate entries when filtering across many-to-many joins,
        e.g. fields within Items or Approvals.
        """
        queryset = super().qs
        return queryset.distinct()

    def filter_item_hs_codes(self, queryset, name, value):
        """
        Custom filter to handle filtering by a list of HS codes, including nulls.
        """
        hs_codes = [code.strip() for code in value.split(",")]
        include_null = False
        hs_codes_cleaned = []

        for hs_code in hs_codes:
            if hs_code.lower() in ["null", "none", ""]:
                include_null = True
            else:
                hs_codes_cleaned.append(hs_code)

        q = Q()
        if hs_codes_cleaned:
            for hs_code in hs_codes_cleaned:
                q |= Q(item_hs_code__icontains=hs_code)
        if include_null:
            q |= Q(item_hs_code__isnull=True)

        return queryset.filter(q)


class ItemGeoInsightViewset(APIView, TeamOwnerFilteredModelView):
    serializer_class = ItemGeoInsightSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    model = ItemGeoInsight
    filter_backends = [filters.DjangoFilterBackend, NullsLastOrderingFilter]
    filterset_class = ItemGeoInsightFilter
    ordering_fields = [
        "supplier",
        "item",
        "sender_address",
        "sender_address_country",
        "shipping_address",
        "shipping_address_country",
        "item_hs_code",
    ]


class NearbyItemGeoInsightViewset(APIView, TeamOwnerFilteredModelView):
    model = ItemGeoInsight
    serializer_class = ItemGeoInsightSerializer

    @action(detail=False, methods=["get"])
    def get_nearby_insights(self, request):
        team = self.request.team
        latitude = request.GET.get("latitude")
        longitude = request.GET.get("longitude")
        radius = request.GET.get("radius")

        if team and latitude and longitude and radius:
            try:
                insights = self.model.team_sender_addresses_within_radius(
                    team, float(latitude), float(longitude), float(radius)
                )
                return Response(
                    {
                        "results": self.serializer_class(insights, many=True).data,
                    }
                )
            except Exception as e:
                return self.single_error_response(400, f"Error: {e}", "error")
        else:
            return self.single_error_response(
                400,
                "Validation error: you need all field url params [latitude, longitude, radius]",
                "validation_error",
            )
