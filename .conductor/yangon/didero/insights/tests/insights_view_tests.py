from django.urls import reverse
from rest_framework import status

from didero.addresses.models import Address
from didero.insights.models import ItemGeoInsight
from didero.testing.cases import TestCase


class TestItemGeoInsightViewset(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user()
        self.auth_as_user(self.user)
        self.team = self.user.teams.first()
        self.team_id = self.team.pk

        # Create related models: Supplier, Item, Address
        self.supplier = self.create_supplier(team=self.team)
        self.item = self.create_item(team_id=self.team_id, supplier=self.supplier)
        self.sender_address = Address.objects.create(
            line_1="123 Main St",
            city="Anytown",
            state_or_province="CA",
            postal_code="12345",
            country="US",
            team=self.team,
        )
        self.shipping_address = Address.objects.create(
            line_1="123 Main St",
            city="NY",
            state_or_province="NY",
            postal_code="10001",
            country="US",
            team=self.team,
            supplier=self.supplier,
        )

        # Create an ItemGeoInsight instance
        self.item_geo_insight = ItemGeoInsight.objects.create(
            team=self.team,
            item=self.item,
            supplier=self.supplier,
            sender_address=self.sender_address,
            shipping_address=self.shipping_address,
            item_number=self.item.item_number,
            item_hs_code="1234.56",
            supplier_name=self.supplier.name,
            sender_address_latitude=37.7749,
            sender_address_longitude=-122.4194,
            quantity=100,
            total_cost=1000,
        )

    def test_item_geo_insight_list(self):
        # Test the list URL: "api/insights/item_geo"
        url = reverse("insights-item-geo-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.json().get("results", [])
        # Ensure only insights for the user's team are returned
        self.assertEqual(
            len(results), ItemGeoInsight.objects.filter(team=self.team).count()
        )

    def test_item_geo_insight_detail(self):
        # Test the detail URL: "api/insights/item_geo/<int:id>"
        url = reverse("insights-item-geo-detail", args=[self.item_geo_insight.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["itemNumber"], self.item.item_number)

    def test_item_geo_insight_access_with_different_team(self):
        # Create a new user belonging to a different team
        user2 = self.create_user(email="<EMAIL>")
        self.auth_as_user(user2)

        # Attempt to access the ItemGeoInsight detail
        url = reverse("insights-item-geo-detail", args=[self.item_geo_insight.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Ensure the user cannot see the ItemGeoInsight in the list view
        url = reverse("insights-item-geo-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.json().get("results", [])
        self.assertEqual(len(results), 0)

    def test_item_geo_insight_filter_by_item_number(self):
        # Test filtering by item_number
        url = reverse("insights-item-geo-list")
        response = self.client.get(url, {"item_number": self.item.item_number})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.json().get("results", [])
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["itemNumber"], self.item.item_number)

    def test_item_geo_insight_filter_by_supplier_name(self):
        # Test filtering by supplier_name
        url = reverse("insights-item-geo-list")
        response = self.client.get(url, {"supplier_name": self.supplier.name})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.json().get("results", [])
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["supplierName"], self.supplier.name)

    def test_item_geo_insight_create_not_allowed(self):
        # Test that creation is not allowed via API (read-only)
        url = reverse("insights-item-geo-list")
        data = {
            "item": self.item.id,
            "supplier": self.supplier.id,
            "sender_address": self.sender_address.id,
            "shipping_address": self.shipping_address.id,
            "item_number": "NEWITEM",
            "item_hs_code": "9876.54",
            "supplier_name": "New Supplier",
            "sender_address_latitude": 40.0,
            "sender_address_longitude": -75.0,
            "quantity": 50,
            "total_cost": 500,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_item_geo_insight_update_not_allowed(self):
        # Test that updating is not allowed via API (read-only)
        url = reverse("insights-item-geo-detail", args=[self.item_geo_insight.id])
        data = {
            "quantity": 200,
        }
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_item_geo_insight_delete_not_allowed(self):
        # Test that deletion is not allowed via API (read-only)
        url = reverse("insights-item-geo-detail", args=[self.item_geo_insight.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_item_geo_insight_invalid_detail(self):
        # Test accessing a non-existent ItemGeoInsight detail
        url = reverse("insights-item-geo-detail", args=[999999])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
