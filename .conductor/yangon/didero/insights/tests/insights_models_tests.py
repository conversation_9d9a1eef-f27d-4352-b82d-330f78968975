from decimal import Decimal

from didero.addresses.models import Address
from didero.insights.models import ItemGeoInsight
from didero.testing.cases import TestCase


class TestItemGeoInsightModels(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user()
        self.auth_as_user(self.user)
        self.team = self.user.teams.first()
        self.team_id = self.team.pk

        # Create related models: Supplier, Item, Address
        self.supplier = self.create_supplier(team=self.team)
        self.item = self.create_item(team_id=self.team_id, supplier=self.supplier)
        self.sender_address = Address.objects.create(
            line_1="123 Main St",
            city="Anytown",
            state_or_province="CA",
            postal_code="12345",
            country="US",
            team=self.team,
        )
        self.shipping_address = Address.objects.create(
            line_1="123 Main St",
            city="NY",
            state_or_province="NY",
            postal_code="10001",
            country="US",
            team=self.team,
            supplier=self.supplier,
        )

    def test_create_and_get(self):
        # Create an ItemGeoInsight instance
        item_geo_insight = ItemGeoInsight.objects.create(
            team=self.team,
            item=self.item,
            supplier=self.supplier,
            sender_address=self.sender_address,
            shipping_address=self.shipping_address,
            item_number=self.item.item_number,
            item_hs_code="1234.56",
            supplier_name=self.supplier.name,
            sender_address_latitude=37.7749,
            sender_address_longitude=-122.4194,
            quantity=100,
            total_cost=1000,
        )

        # Retrieve the ItemGeoInsight instance from the database
        retrieved_insight = ItemGeoInsight.objects.get(id=item_geo_insight.id)

        # Verify the values of the retrieved instance
        self.assertEqual(retrieved_insight.team, self.team)
        self.assertEqual(retrieved_insight.item, self.item)
        self.assertEqual(retrieved_insight.supplier, self.supplier)
        self.assertEqual(retrieved_insight.sender_address, self.sender_address)
        self.assertEqual(retrieved_insight.item_number, self.item.item_number)
        self.assertEqual(retrieved_insight.item_hs_code, "1234.56")
        self.assertEqual(retrieved_insight.supplier_name, self.supplier.name)
        self.assertEqual(retrieved_insight.sender_address_latitude, Decimal("37.7749"))
        self.assertEqual(
            retrieved_insight.sender_address_longitude, Decimal("-122.4194")
        )
        self.assertEqual(retrieved_insight.quantity, 100)
        self.assertEqual(retrieved_insight.total_cost.amount, 1000)
        self.assertEqual(retrieved_insight.total_cost.currency.name, "US Dollar")


class TestTeamItemGeoInsightsWithinRadius(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user()
        self.auth_as_user(self.user)
        self.team = self.user.teams.first()

        # metadata we need to create the insights
        self.supplier = self.create_supplier(team=self.team)
        self.item = self.create_item(team_id=self.team.id, supplier=self.supplier)
        self.shipping_address = Address.objects.create(
            line_1="123 Main St",
            city="NY",
            state_or_province="NY",
            postal_code="10001",
            country="US",
            team=self.team,
            supplier=self.supplier,
        )

        # Create sender addresses with specific latitudes and longitudes
        self.sender_address_sf = Address.objects.create(
            line_1="1 Market St",
            city="San Francisco",
            state_or_province="CA",
            postal_code="94105",
            country="US",
            team=self.team,
            latitude=Decimal("37.7749"),
            longitude=Decimal("-122.4194"),
        )

        self.sender_address_la = Address.objects.create(
            line_1="100 N Main St",
            city="Los Angeles",
            state_or_province="CA",
            postal_code="90012",
            country="US",
            team=self.team,
            latitude=Decimal("34.0522"),
            longitude=Decimal("-118.2437"),
        )

        self.sender_address_ny = Address.objects.create(
            line_1="350 5th Ave",
            city="New York",
            state_or_province="NY",
            postal_code="10118",
            country="US",
            team=self.team,
            latitude=Decimal("40.7128"),
            longitude=Decimal("-74.0060"),
        )

        self.sender_address_aus = Address.objects.create(
            line_1="1 George St",
            city="Sydney",
            state_or_province="NSW",
            postal_code="2000",
            country="AU",
            team=self.team,
            latitude=Decimal("-33.8688"),
            longitude=Decimal("151.2093"),
        )

        # Create ItemGeoInsight instances with the corresponding sender addresses
        self.insight_sf = ItemGeoInsight.objects.create(
            team=self.team,
            supplier=self.supplier,
            item=self.item,
            sender_address=self.sender_address_sf,
            shipping_address=self.shipping_address,
            sender_address_latitude=self.sender_address_sf.latitude,
            sender_address_longitude=self.sender_address_sf.longitude,
        )

        self.insight_la = ItemGeoInsight.objects.create(
            team=self.team,
            supplier=self.supplier,
            item=self.item,
            sender_address=self.sender_address_la,
            shipping_address=self.shipping_address,
            sender_address_latitude=self.sender_address_la.latitude,
            sender_address_longitude=self.sender_address_la.longitude,
        )

        self.insight_ny = ItemGeoInsight.objects.create(
            team=self.team,
            supplier=self.supplier,
            item=self.item,
            sender_address=self.sender_address_ny,
            shipping_address=self.shipping_address,
            sender_address_latitude=self.sender_address_ny.latitude,
            sender_address_longitude=self.sender_address_ny.longitude,
        )

        # insight in australia and no lat/long
        self.insight_no_location = ItemGeoInsight.objects.create(
            team=self.team,
            supplier=self.supplier,
            item=self.item,
            sender_address=self.sender_address_aus,
            shipping_address=self.shipping_address,
            sender_address_latitude=None,
            sender_address_longitude=None,
        )

    def test_addresses_within_radius(self):
        # Coordinates of San Francisco
        lat = 37.7749
        lng = -122.4194
        # 600 km radius (Distance between SF and LA: 559.1205770615533 km)
        radius_km = 600

        results = ItemGeoInsight.team_sender_addresses_within_radius(
            self.team, lat, lng, radius_km
        )
        self.assertEqual(len(results), 2)

        # Verify that the correct insights are returned
        insights_ids = [insight.id for insight in results]
        self.assertIn(self.insight_sf.id, insights_ids)
        self.assertIn(self.insight_la.id, insights_ids)
        self.assertNotIn(self.insight_ny.id, insights_ids)
        self.assertNotIn(self.insight_no_location.id, insights_ids)

    def test_no_addresses_within_radius(self):
        # Coordinates somewhere in the ocean
        lat = 0.0
        lng = 0.0
        radius_km = 100  # 100 km radius

        results = ItemGeoInsight.team_sender_addresses_within_radius(
            self.team, lat, lng, radius_km
        )
        self.assertEqual(len(results), 0)

    def test_all_addresses_within_large_radius(self):
        # Large radius to include all insights
        lat = 37.7749
        lng = -122.4194
        radius_km = 5000  # 5000 km radius

        results = ItemGeoInsight.team_sender_addresses_within_radius(
            self.team, lat, lng, radius_km
        )
        self.assertEqual(len(results), 3)

        insights_ids = [insight.id for insight in results]
        self.assertIn(self.insight_sf.id, insights_ids)
        self.assertIn(self.insight_la.id, insights_ids)
        self.assertIn(self.insight_ny.id, insights_ids)
        self.assertNotIn(self.insight_no_location.id, insights_ids)
