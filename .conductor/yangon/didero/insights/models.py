from math import asin, cos, radians, sin, sqrt

from django.db import models
from django.utils.timezone import now
from django_countries.fields import CountryField
from djmoney.models.fields import Decimal, MoneyField
from djmoney.models.validators import MinMoneyValidator

from didero.models import BaseModelWithTeamField
from didero.users.models.team_models import Team


# Item insigths (eg: total_cost and quantity) by geo
# We will be adding 1 row per item per day via an offline script
class ItemGeoInsight(BaseModelWithTeamField):
    team = models.ForeignKey(
        "users.Team",
        on_delete=models.CASCADE,  # only delete insights when a team is deleted
        related_name="item_supplier_geo_insights",
    )
    item = models.ForeignKey(
        "items.Item",
        on_delete=models.DO_NOTHING,  # we want to keep historical data
        related_name="item_supplier_geo_insights",
    )
    supplier = models.ForeignKey(
        "suppliers.Supplier",
        on_delete=models.DO_NOTHING,  # we want to keep historical data
        related_name="item_supplier_geo_insights",
    )
    shipping_address = models.ForeignKey(
        "addresses.Address",
        on_delete=models.DO_NOTHING,
        related_name="item_shipped_to",
        null=True,
        blank=True,
    )
    sender_address = models.ForeignKey(
        "addresses.Address",
        on_delete=models.DO_NOTHING,
        related_name="item_sent_from",
        null=True,
        blank=True,
    )
    # We want frozen in time snapshots of a few item/supplier/address identifiers (eg: hs_code, lat, long, names)
    # Note: we allow null on hs_code, latitude and longitude since they are optional on item/supplier
    # and we want the ability to fill those out later on without missing data
    item_description = models.TextField(default="")
    item_number = models.CharField(max_length=30)
    item_hs_code = models.CharField(max_length=20, null=True, blank=True)
    supplier_name = models.CharField(max_length=256)
    sender_address_country = CountryField(default="US")
    sender_address_latitude = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )
    sender_address_longitude = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )
    shipping_address_country = CountryField(default="US")
    shipping_address_latitude = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )
    shipping_address_longitude = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )
    # These are daily aggregates (amount of items requested and the total cost per day)
    quantity = models.FloatField(default=0.00)
    total_cost = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="USD",
        default=Decimal("0.00"),
        validators=[MinMoneyValidator(0)],
    )
    insight_date = models.DateField(
        default=now,
        blank=True,
        help_text="Date for which the insight was calculated for",
    )

    class Meta:
        indexes = [
            models.Index(fields=["team"]),
            models.Index(fields=["item"]),
            models.Index(fields=["supplier"]),
            models.Index(fields=["sender_address"]),
            models.Index(fields=["shipping_address"]),
            models.Index(fields=["item_number"]),
            models.Index(fields=["item_hs_code"]),
            models.Index(fields=["item_description"]),
            models.Index(fields=["sender_address_country"]),
            models.Index(fields=["sender_address_latitude"]),
            models.Index(fields=["sender_address_longitude"]),
            models.Index(fields=["shipping_address_country"]),
            models.Index(fields=["shipping_address_latitude"]),
            models.Index(fields=["shipping_address_longitude"]),
            models.Index(fields=["quantity"]),
            models.Index(fields=["total_cost"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["insight_date"]),
        ]

        ordering = ["created_at"]

        constraints = [
            models.UniqueConstraint(
                fields=[
                    "team",
                    "item",
                    "supplier",
                    "sender_address",
                    "shipping_address",
                    "insight_date",
                ],
                name="unique_team_item_supplier_address_date",
            )
        ]

    @staticmethod
    def team_sender_addresses_within_radius(
        team: Team, lat: float, lng: float, radius_km: float
    ):
        insights = ItemGeoInsight.objects.filter(team=team).exclude(
            sender_address_latitude__isnull=True, sender_address_longitude__isnull=True
        )
        result = []
        for insight in insights:
            distance = ItemGeoInsight.haversine_distance(
                lat,
                lng,
                float(insight.sender_address_latitude),
                float(insight.sender_address_longitude),
            )
            if distance <= radius_km:
                result.append(insight)
        return result

    @staticmethod
    def haversine_distance(lat1, lon1, lat2, lon2):
        """
        Calculate the great-circle distance between two points
        on the Earth specified in decimal degrees.
        """
        # Convert decimal degrees to radians
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * asin(sqrt(a))
        r = 6371  # Radius of Earth in kilometers. Use 3956 for miles
        return c * r
