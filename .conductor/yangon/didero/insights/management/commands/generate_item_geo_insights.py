from datetime import datetime

import structlog
from django.core.management.base import BaseCommand

from didero.insights.utils import generate_item_geo_insights

logger = structlog.get_logger(__name__)

"""
This script generates item-geo insights for the given team and date (today if no date is given)
"""


class Command(BaseCommand):
    help = (
        "Generate item-geo insights for the given team and date ('yyyy-mm-dd' format)"
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "--team",
            type=str,
            help="Specify the team to be processed",
        )
        parser.add_argument(
            "--date",
            type=str,
            help="Specify the date to be processed",
        )

    def handle(self, *args, **kwargs):
        date_str = kwargs.get("date")
        team = kwargs.get("team")

        if date_str:
            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                self.stderr.write("Invalid date format. Please use 'yyyy-mm-dd'.")
                return
        else:
            date = datetime.today().date()

        if not team:
            self.stderr.write("Please provide a team using the --team argument.")
            return

        # Use the utils.generate_item_geo_insights to generate data for the date/team combination
        try:
            generate_item_geo_insights(date=date, team_id=team)
            logger.info(
                "Item-geo insights generated for team %s on date %s", team, date
            )
        except Exception as e:
            logger.error("An error occurred: %s", str(e))
            self.stderr.write("An error occurred while generating insights.")
