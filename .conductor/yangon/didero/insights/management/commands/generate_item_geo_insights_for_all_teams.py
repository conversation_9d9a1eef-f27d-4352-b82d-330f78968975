from datetime import datetime, timedelta

import structlog
from django.core.management.base import BaseCommand

from didero.insights.utils import generate_item_geo_insights
from didero.users.models.team_models import Team

logger = structlog.get_logger(__name__)

"""
This script generates item-geo insights for all teams from the beginning of times
"""


class Command(BaseCommand):
    help = "Generate item-geo insights for all teams"

    def add_arguments(self, parser):
        parser.add_argument(
            "--date",
            type=str,
            help="(optional) Specify the date to be processed",
        )

    def handle(self, *args, **kwargs):
        date_str = kwargs.get("date")
        team = kwargs.get("team")
        date = None

        if date_str:
            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
                logger.info(
                    "Generating item-geo insights for all teams on date %s",
                    date,
                )
            except ValueError:
                self.stderr.write("Invalid date format. Please use 'yyyy-mm-dd'")
                return
        else:
            logger.info("Generating item-geo insights for all teams & dates")

        for team in Team.objects.all():
            if date:
                # if date, then only calculate the team item-geo insights for the given date
                self.generate_insights_for_date(date, team.pk)
            else:
                # get all dates starting the day the team was created
                start_date = team.created_at.date()
                current_date = datetime.today().date()
                date_range = (
                    start_date + timedelta(days=i)
                    for i in range((current_date - start_date).days + 1)
                )
                logger.info(
                    "Generating item-geo insights for team %s starting on date %s",
                    team,
                    start_date,
                )
                for date in date_range:
                    self.generate_insights_for_date(date, team.pk)

    def generate_insights_for_date(self, date, team_id: int):
        # Use the utils.generate_item_geo_insights to generate data for the date/team combination
        try:
            generate_item_geo_insights(date=date, team_id=team_id)
            logger.info(
                "Item-geo insights generated for team %s on date %s", team_id, date
            )
        except Exception as e:
            logger.error("An error occurred: %s", str(e))
            self.stderr.write("An error occurred while generating insights")
