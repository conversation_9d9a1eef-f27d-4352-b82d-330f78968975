from decimal import Decimal

from rest_framework import serializers

from didero.insights.models import ItemGeoInsight


class ItemGeoInsightSerializer(serializers.ModelSerializer):
    class Meta:
        model = ItemGeoInsight
        read_only_fields = (
            "id",
            "team",
            "item",
            "supplier",
            "sender_address",
            "shipping_address",
            "item_description",
            "item_number",
            "item_hs_code",
            "supplier_name",
            "sender_address_country",
            "sender_address_latitude",
            "sender_address_longitude",
            "shipping_address_country",
            "shipping_address_latitude",
            "shipping_address_longitude",
            "quantity",
            "total_cost",
            "insight_date",
            "created_at",
        )
        fields = read_only_fields

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        quantity = representation.get("quantity")
        total_cost = representation.get("total_cost")
        if quantity is not None:
            representation["quantity"] = float(Decimal(quantity).normalize())
        if total_cost is not None:
            representation["total_cost"] = float(Decimal(total_cost).normalize())
        return representation
