# Supervisord configuration for Celery workers
# This file is used by the didero-worker container to run Celery workers and beat
# Temporal workers are now in separate containers - see temporal-worker.Dockerfile

[supervisord]
nodaemon=true
user=root

[inet_http_server]
port=0.0.0.0:9001
username=%(ENV_SUPERVISOR_USERNAME)s
password=%(ENV_SUPERVISOR_PASSWORD)s

# TODO: We should really use a file output and pipe to Cloudwatch. But log to supervisor's STDOUT for now
# Main worker - handles default and other queues
[program:celery_worker]
command=uv run celery -A didero worker --loglevel=info -Q default,bulk_supplier_imports,task_email_notifications --concurrency=8
autostart=true
autorestart=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
user=root

# Dedicated worker for periodic_tasks queue
[program:celery_worker_periodic_tasks]
command=uv run celery -A didero worker --loglevel=info -Q periodic_tasks --concurrency=3 --max-tasks-per-child=10
autostart=true
autorestart=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
user=root

# Dedicated worker for ai_workflows queue - AGGRESSIVE SCALING for backlog
[program:celery_worker_ai_workflows]
command=uv run celery -A didero worker --loglevel=info -Q ai_workflows --concurrency=16 --max-tasks-per-child=50
autostart=true
autorestart=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
user=root

# Dedicated worker for email_backfills queue - AGGRESSIVE SCALING for chronic backlog
[program:celery_worker_email_backfills]
command=uv run celery -A didero worker --loglevel=info -Q email_backfills --concurrency=12 --max-tasks-per-child=100
autostart=true
autorestart=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
user=root

# TODO: We should really use a file output and pipe to Cloudwatch. But log to supervisor's STDOUT for now
[program:celery_beat]
command=uv run celery -A didero beat -l info
autostart=true
autorestart=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
user=root

# Temporal workers removed - now run in separate containers
# See temporal-worker-*.Dockerfile for Temporal worker containers
