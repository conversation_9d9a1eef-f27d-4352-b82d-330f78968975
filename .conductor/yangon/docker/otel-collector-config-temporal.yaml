receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    timeout: 1s
    send_batch_size: 100

  # Filter processor to separate Temporal metrics
  # Original configuration (kept for rollback):
  # filter/temporal:
  #   metrics:
  #     include:
  #       match_type: regexp
  #       metric_names:
  #         - "^temporal_.*"
  #         - "^workflow_.*"
  
  # Optimized configuration - only essential metrics for monitoring
  filter/temporal:
    metrics:
      include:
        match_type: strict
        metric_names:
          # Workflow Health (5 metrics)
          - "temporal_workflow_completed"
          - "temporal_workflow_failed"
          - "temporal_workflow_task_execution_latency"
          - "temporal_workflow_endtoend_latency"
          - "temporal_workflow_task_schedule_to_start_latency"
          # Activity Health (4 metrics)
          - "temporal_activity_execution_failed"
          - "temporal_activity_execution_latency"
          - "temporal_activity_schedule_to_start_latency"
          - "temporal_activity_task_error"
          # Queue Health (4 metrics)
          - "temporal_activity_poll_no_task"
          - "temporal_workflow_task_queue_poll_empty"
          - "temporal_worker_task_slots_available"
          - "temporal_worker_task_slots_used"
          # Worker Health (2 metrics)
          - "temporal_sticky_cache_hit"
          - "temporal_sticky_cache_size"
          # Client Health (2 metrics)
          - "temporal_request_failure"
          - "temporal_request_latency"
          # Include workflow custom metrics
          - "workflow_started"
          - "workflow_completed"
          - "workflow_failed"

  filter/application:
    metrics:
      exclude:
        match_type: strict
        metric_names:
          # Exclude all Temporal SDK metrics
          - "temporal_workflow_completed"
          - "temporal_workflow_failed"
          - "temporal_workflow_task_execution_latency"
          - "temporal_workflow_endtoend_latency"
          - "temporal_workflow_task_schedule_to_start_latency"
          - "temporal_activity_execution_failed"
          - "temporal_activity_execution_latency"
          - "temporal_activity_schedule_to_start_latency"
          - "temporal_activity_task_error"
          - "temporal_activity_poll_no_task"
          - "temporal_workflow_task_queue_poll_empty"
          - "temporal_worker_task_slots_available"
          - "temporal_worker_task_slots_used"
          - "temporal_sticky_cache_hit"
          - "temporal_sticky_cache_size"
          - "temporal_request_failure"
          - "temporal_request_latency"
          # Exclude workflow custom metrics
          - "workflow_started"
          - "workflow_completed"
          - "workflow_failed"

  # Add attributes to identify metrics source
  attributes/temporal:
    actions:
      - key: service.name
        value: temporal-worker
        action: upsert
      - key: metric.source
        value: temporal-sdk
        action: upsert

  attributes/application:
    actions:
      - key: metric.source
        value: application
        action: upsert

exporters:
  debug:
    verbosity: detailed

  # AWS EMF exporter for Temporal metrics
  awsemf/temporal:
    region: "us-east-2"
    namespace: "Didero/Temporal" # Separate namespace for Temporal
    log_group_name: "/dev/didero-temporal-metrics"
    dimension_rollup_option: "NoDimensionRollup"
    metric_declarations:
      - dimensions:
          - [namespace, workflow_type]
          - [namespace]
        metric_name_selectors:
          - "temporal_workflow_completed"
          - "temporal_workflow_failed"
          - "temporal_workflow_task_execution_latency"
          - "temporal_workflow_endtoend_latency"
          - "temporal_workflow_task_schedule_to_start_latency"
          - "temporal_sticky_cache_hit"
          - "temporal_sticky_cache_size"
          - "temporal_workflow_task_queue_poll_empty"
      - dimensions:
          - [namespace, activity_type]
          - [namespace, activity_type, workflow_type]
          - [namespace]
        metric_name_selectors:
          - "temporal_activity_execution_failed"
          - "temporal_activity_execution_latency"
          - "temporal_activity_schedule_to_start_latency"
          - "temporal_activity_task_error"
          - "temporal_activity_poll_no_task"
      - dimensions:
          - [namespace]
        metric_name_selectors:
          - "temporal_worker_task_slots_available"
          - "temporal_worker_task_slots_used"
          - "temporal_request_failure"
          - "temporal_request_latency"
      - dimensions:
          - [namespace, success]
          - [namespace, success, reason]
          - [namespace, success, team_id]
        metric_name_selectors:
          - "workflow_started"
          - "workflow_completed"
          - "workflow_failed"

  # AWS EMF exporter for application metrics
  awsemf/application:
    region: "us-east-2"
    namespace: "Didero/Dev" # Keep existing namespace for app metrics
    log_group_name: "/dev/didero-metrics"
    dimension_rollup_option: "NoDimensionRollup"

  # AWS X-Ray for traces (unchanged)
  awsxray:
    region: "us-east-2"

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [debug, awsxray]

    # Pipeline for Temporal metrics
    metrics/temporal:
      receivers: [otlp]
      processors: [batch, filter/temporal, attributes/temporal]
      exporters: [debug, awsemf/temporal]

    # Pipeline for application metrics
    metrics/application:
      receivers: [otlp]
      processors: [batch, filter/application, attributes/application]
      exporters: [debug, awsemf/application]

  telemetry:
    logs:
      level: "debug"
