ARG PYTHON_VERSION=3.11-slim-bullseye

FROM public.ecr.aws/docker/library/python:${PYTHON_VERSION}

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PATH="/root/.local/bin:${PATH}"
ENV C_FORCE_ROOT=1

RUN mkdir -p /code

WORKDIR /code
COPY . /code/

# We were getting errors of `error: failed to remove directory `/code/.venv``.
# To be safe, we just hard delete it here
RUN rm -rf /code/.venv

COPY pyproject.toml uv.lock /code/

# Update and install necessary packages with retries and extended timeout
RUN set -ex && \
    apt-get update -o Acquire::Retries=5 -o Acquire::http::Timeout="160" && \
    apt-get install -y curl gcc make libpq-dev libcurl4-openssl-dev libssl-dev poppler-utils procps tesseract-ocr wkhtmltopdf && \
    pip install --upgrade pip wheel && \
    rm -rf /var/lib/apt/lists/*  # Clean up the apt cache

# Install uv using their install script and add it to PATH
RUN curl -LsSf https://github.com/astral-sh/uv/releases/download/0.6.16/uv-installer.sh | sh
ENV PATH="$PATH:/root/.cargo/bin"
RUN which uv

# Create a new virtual environment
RUN uv venv

# Now we can sync / create the virtual environment correctly
RUN uv sync && \
    rm -rf /root/.cache/

COPY . /code/
