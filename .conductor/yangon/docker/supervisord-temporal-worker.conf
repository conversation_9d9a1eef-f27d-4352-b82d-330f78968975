[supervisord]
nodaemon=true
user=root

[program:temporal_worker]
command=uv run python manage.py temporal_worker --queue %(ENV_TEMPORAL_QUEUE)s
numprocs=%(ENV_TEMPORAL_WORKER_NUMPROCS)s
process_name=%(program_name)s_%(process_num)02d
autostart=true
autorestart=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
stderr_logfile=/dev/fd/2
stderr_logfile_maxbytes=0
user=root
environment=TEMPORAL_MAX_WORKERS="%(ENV_TEMPORAL_MAX_WORKERS)s"