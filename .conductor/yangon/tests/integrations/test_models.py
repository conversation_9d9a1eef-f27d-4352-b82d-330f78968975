from django.test import TestCase

from didero.integrations.models import ERPIntegrationConfig
from didero.users.models import Team


class ERPIntegrationConfigModelTest(TestCase):
    def setUp(self):
        self.team = Team.objects.create(name="Test Team")

    def test_create_erp_config(self):
        """Test creating an ERP configuration"""
        config = ERPIntegrationConfig.objects.create(
            team=self.team,
            erp_type="netsuite",
            enabled=True,
            field_mappings={
                "tracking_number": "custbody_ionq_tracking_number",
                "estimated_delivery_date": "expectedreceiptdate",
            },
            config={"endpoint": "https://test.netsuite.com", "api_version": "2023_2"},
        )

        self.assertEqual(config.team, self.team)
        self.assertEqual(config.erp_type, "netsuite")
        self.assertTrue(config.enabled)

    def test_one_to_one_constraint(self):
        """Test that each team can only have one ERP config"""
        # Create first config
        ERPIntegrationConfig.objects.create(
            team=self.team, erp_type="netsuite", enabled=True
        )

        # Attempt to create second config should fail
        with self.assertRaises(Exception):
            ERPIntegrationConfig.objects.create(
                team=self.team, erp_type="sap", enabled=True
            )
