import os
from unittest import TestCase
from unittest.mock import patch

from didero.integrations.erp.credentials import SimpleEnvCredentialProvider


class TestSimpleEnvCredentialProvider(TestCase):
    @patch.dict(
        os.environ,
        {
            "IONQ_NETSUITE_ACCOUNT_ID": "test_account",
            "IONQ_NETSUITE_CONSUMER_KEY": "test_key",
            "IONQ_NETSUITE_CONSUMER_SECRET": "test_secret",
            "IONQ_NETSUITE_TOKEN_ID": "test_token",
            "IONQ_NETSUITE_TOKEN_SECRET": "test_token_secret",
        },
    )
    def test_get_credentials(self):
        """Test retrieving credentials from environment"""
        provider = SimpleEnvCredentialProvider()
        creds = provider.get_credentials("ionq", "netsuite")

        self.assertEqual(creds["account_id"], "test_account")
        self.assertEqual(creds["consumer_key"], "test_key")
        self.assertEqual(creds["consumer_secret"], "test_secret")
        self.assertEqual(creds["token_id"], "test_token")
        self.assertEqual(creds["token_secret"], "test_token_secret")

    def test_missing_credentials(self):
        """Test handling of missing credentials"""
        provider = SimpleEnvCredentialProvider()
        creds = provider.get_credentials("nonexistent", "netsuite")

        # Should return empty dict for missing credentials
        self.assertEqual(len(creds), 0)
