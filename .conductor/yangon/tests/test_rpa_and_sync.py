"""
End-to-End Test for RPA Service Integration with Didero

This test verifies:
1. RPA Service Connection - Tests actual connectivity to the RPA service
2. NetSuite PO Sync Functionality - Tests the sync of PO data from NetSuite to Didero
3. Error Handling - Verifies proper error handling for various failure scenarios
4. Data Processing - Validates that PO data is correctly processed and stored

Usage:
    python -m tests.test_rpa_service_e2e

Environment Variables:
    DIDERO_RPA_URL - URL of the RPA service (required)
    DIDERO_RPA_API_KEY - API key for the RPA service (required)
    TEST_PO_NUMBER - PO number to use for testing (defaults to "A307054")
"""

import asyncio
import logging
import os
import sys
import uuid
from decimal import Decimal
from typing import Any, Dict, Optional, Tuple
from unittest import mock

import requests
import structlog
from asgiref.sync import sync_to_async
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.db import transaction

# Initialize Django before importing models
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")
import django

django.setup()

from didero.orders.models import PurchaseOrder
from didero.workflows.core.nodes.purchase_orders.netsuite_sync import (
    NetSuiteSyncErrorType,
    RPAFetchResult,
    SyncNetSuitePOParams,
    netsuite_po_sync_worker,
    normalize_po_number,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = structlog.get_logger(__name__)

# Test constants - Using strict configuration approach
try:
    DEFAULT_RPA_URL = settings.DIDERO_RPA_URL
except AttributeError:
    raise ImproperlyConfigured(
        "DIDERO_RPA_URL setting is not configured for RPA tests."
    )

try:
    DEFAULT_RPA_KEY = os.environ["DIDERO_RPA_API_KEY"]
except KeyError:
    raise ImproperlyConfigured(
        "DIDERO_RPA_API_KEY environment variable is not set for RPA tests."
    )

DEFAULT_TEST_PO = os.environ.get("TEST_PO_NUMBER", "A307054")
DEFAULT_CUSTOMER = "total_home_supply"


class RPAServiceE2ETest:
    """End-to-End test suite for RPA service integration."""

    def __init__(
        self,
        rpa_url: str = DEFAULT_RPA_URL,
        rpa_api_key: str = DEFAULT_RPA_KEY,
        test_po_number: str = DEFAULT_TEST_PO,
        customer: str = DEFAULT_CUSTOMER,
    ):
        """Initialize the test suite."""
        self.rpa_url = rpa_url
        self.rpa_api_key = rpa_api_key
        self.test_po_number = test_po_number
        self.customer = customer
        self.transaction_id = str(uuid.uuid4())

        # Make API key available in environment for rpa.py functions
        os.environ["DIDERO_RPA_API_KEY"] = self.rpa_api_key

        # Override settings for tests
        settings.DIDERO_RPA_URL = self.rpa_url

        logger.info(
            "Initializing RPA Service E2E Test",
            rpa_url=self.rpa_url,
            test_po=self.test_po_number,
            transaction_id=self.transaction_id,
        )

    async def test_rpa_connection(self) -> bool:
        """
        Test direct connection to the RPA service.

        This test only verifies the health endpoint is accessible.
        No actual jobs are submitted to minimize interactions with the RPA service.

        Returns:
            bool: True if connection is successful, False otherwise
        """
        logger.info("Testing RPA connection", url=self.rpa_url)

        try:
            # Test health endpoint only
            health_url = f"{self.rpa_url.rstrip('/')}/health"
            response = await sync_to_async(requests.get)(
                health_url, headers={"x-api-key": self.rpa_api_key}, timeout=10
            )

            if response.status_code != 200:
                logger.error(
                    "Health endpoint failed",
                    status_code=response.status_code,
                    response=response.text[:100],
                )
                return False

            # Successfully connected to health endpoint
            logger.info(
                "Health endpoint successful",
                status_code=response.status_code,
                response=response.text[:100],
            )
            return True

        except Exception as e:
            logger.error("Error connecting to RPA service", error=str(e), exc_info=True)
            return False

    async def test_create_or_find_po(self) -> Optional[PurchaseOrder]:
        """
        Create or find a PO for testing.

        Either creates a new PO with the test PO number or finds an existing one.

        Returns:
            Optional[PurchaseOrder]: The PO object if successful, None otherwise
        """
        try:
            # Try to find existing PO with test number
            normalized_po_number = normalize_po_number(self.test_po_number)

            async def find_po():
                return await sync_to_async(
                    lambda: PurchaseOrder.objects.filter(
                        po_number=normalized_po_number
                    ).first()
                )()

            po = await find_po()

            if po:
                logger.info(
                    "Found existing PO for testing",
                    po_id=po.pk,
                    po_number=po.po_number,
                )
                return po

            # Create new test PO if not found
            logger.info("Creating new test PO", po_number=normalized_po_number)

            async def create_po():
                def sync_create():
                    with transaction.atomic():
                        po = PurchaseOrder.objects.create(
                            po_number=normalized_po_number,
                            order_status="draft",
                            payment_status="unpaid",
                            internal_notes=f"Test PO created by RPA E2E Test {self.transaction_id}",
                            total_cost=Decimal("0.00"),
                            total_cost_currency="USD",
                        )
                        return po

                return await sync_to_async(sync_create)()

            po = await create_po()

            logger.info(
                "Created new test PO",
                po_id=po.pk,
                po_number=po.po_number,
            )
            return po

        except Exception as e:
            logger.error("Error creating/finding test PO", error=str(e), exc_info=True)
            return None

    # Sample RPA response data structure to use for mocking
    SAMPLE_RPA_RESPONSE = {
        "raw_data": {
            "summary": {
                "po_number": "A300807",
                "vendor": "Botany Incorporated\n15 Commerce Rd\nUnit 4\nFairfield NJ 07004\nUnited States\u00a0Map",
                "date": "7/18/2023",
                "location": "THS Warehouse",
                "subsidiary": "Total Home Supply LLC",
                "memo": "P226974",
                "total": "301.63",
                "approval_status": None,
                "last_modified_date": None,
                "created_date": None,
                "source": "CSV",
                "currency": "USA",
                "exchange_rate": "",
                "ship_to": "Shipping\nTotal Home Supply\n26 Chapin Road\nUnit 1109\nPine Brook NJ 07058\nUnited States\n************\u00a0Map",
                "terms": "Due on receipt",
                "contact": "List\u00a0Search",
                "freight_terms": None,
                "shipping_method": "Ground",
                "status": "CONFIRMED",
                "last_modified": "",
                "created": "",
                "fob": "",
            },
            "items": [
                {
                    "item_number": "GE-AKCQ08ACJ",
                    "description": "GE AKCQ08ACJ 8000 BTU Through-the-Wall Room Air Conditioner - 115V",
                    "quantity": 1.0,
                    "unit_price": 301.63,
                    "amount": 301.63,
                }
            ],
        },
        "formatted_data": {
            "purchase_order": {
                "po_number": "A300807",
                "team_id": 1,
                "supplier_id": None,
                "internal_notes": "P226974",
                "vendor_notes": "",
                "payment_terms": "Due on receipt",
                "shipping_method": "Ground",
                "order_status": "CONFIRMED",
                "payment_status": "UNPAID",
                "source": "EXTERNAL",
                "placement_time": "2023-07-18T00:00:00",
                "total_cost_0": 301.63,
                "total_cost_currency": "USD",
                "vendor": "Botany Incorporated\n15 Commerce Rd\nUnit 4\nFairfield NJ 07004\nUnited States\u00a0Map",
                "vendor_address": {
                    "company": "Botany Incorporated",
                    "street": "15 Commerce Rd",
                    "unit": "Unit 4",
                    "city": "Fairfield",
                    "state": "NJ",
                    "zip": "07004",
                    "country": "United States",
                    "phone": None,
                },
                "shipping_address": {
                    "company": "Total Home Supply",
                    "street": "26 Chapin Road",
                    "unit": "Unit 1109",
                    "city": "Pine Brook",
                    "state": "NJ",
                    "zip": "07058",
                    "country": "United States",
                    "phone": "************",
                },
            },
            "line_items": [
                {
                    "item_number": "GE-AKCQ08ACJ",
                    "description": "GE AKCQ08ACJ 8000 BTU Through-the-Wall Room Air Conditioner - 115V",
                    "quantity": 1.0,
                    "unit_price_0": 301.63,
                    "unit_price_currency": "USD",
                    "category": "PRODUCT",
                }
            ],
        },
    }

    async def _mock_fetch_netsuite_po_data(
        self, params: SyncNetSuitePOParams
    ) -> RPAFetchResult:
        """Mock the fetch_netsuite_po_data function to avoid actual RPA calls during testing."""
        logger.info(
            "Mock RPA fetch called",
            po_id=params.purchase_order_id,
            netsuite_po_number=params.netsuite_po_number,
            transaction_id=params.transaction_id,
        )

        # Use our sample data but update it with the real PO number
        sample_data = self.SAMPLE_RPA_RESPONSE.copy()

        # Update the sample data with the actual PO number
        sample_data["raw_data"]["summary"]["po_number"] = params.netsuite_po_number
        sample_data["formatted_data"]["purchase_order"]["po_number"] = (
            params.netsuite_po_number
        )

        # Add an additional line item to make the test more interesting
        additional_item = {
            "item_number": f"TEST-ITEM-{uuid.uuid4().hex[:8]}",
            "description": "Test Item Added for RPA Test",
            "quantity": 2.0,
            "unit_price": 150.00,
            "amount": 300.00,
        }
        sample_data["raw_data"]["items"].append(additional_item)

        # Add the corresponding formatted line item
        formatted_additional_item = {
            "item_number": additional_item["item_number"],
            "description": additional_item["description"],
            "quantity": additional_item["quantity"],
            "unit_price_0": additional_item["unit_price"],
            "unit_price_currency": "USD",
            "category": "PRODUCT",
        }
        sample_data["formatted_data"]["line_items"].append(formatted_additional_item)

        # Update the total cost to reflect the additional item
        new_total = 301.63 + 300.00  # Original + new item
        sample_data["raw_data"]["summary"]["total"] = str(new_total)
        sample_data["formatted_data"]["purchase_order"]["total_cost_0"] = new_total

        return RPAFetchResult(
            success=True,
            po_data=sample_data["formatted_data"]["purchase_order"],
            line_items_data=sample_data["formatted_data"]["line_items"],
            rpa_data=sample_data,
            transaction_id=params.transaction_id,
        )

    async def _mock_openai_address_parsing(
        self, raw_address: str, address_type: str, transaction_id: str = ""
    ):
        """Mock OpenAI address parsing to avoid actual API calls during testing."""
        # Import the AddressSchema class for proper return typing
        from didero.addresses.schemas import AddressSchema

        # Create a properly structured AddressSchema object based on address type
        if "vendor" in address_type.lower():
            return AddressSchema(
                line_1="15 Commerce Rd",
                line_2="Unit 4",
                city="Fairfield",
                state_or_province="NJ",
                postal_code="07004",
                country="US",
            )
        else:  # shipping address
            return AddressSchema(
                line_1="26 Chapin Road",
                line_2="Unit 1109",
                city="Pine Brook",
                state_or_province="NJ",
                postal_code="07058",
                country="US",
            )

    async def test_sync_po(
        self, po: PurchaseOrder
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Test syncing a PO with NetSuite.

        Uses the netsuite_po_sync_worker function but properly mocks all external calls
        to avoid actual API dependencies during testing.

        Args:
            po: The PO to sync

        Returns:
            Tuple[bool, Optional[Dict[str, Any]]]: (success, changes made)
        """
        logger.info(
            "Testing PO sync functionality", po_id=po.id, po_number=po.po_number
        )

        # Setup all the necessary mocks
        mock_patches = [
            # Mock RPA service
            mock.patch(
                "didero.workflows.core.nodes.purchase_orders.netsuite_sync.fetch_netsuite_po_data"
            ),
            # Mock OpenAI calls for address parsing
            mock.patch(
                "didero.ai.order_acknowledgment.netsuite_shipping_address.parse_address_with_ai"
            ),
            mock.patch(
                "didero.ai.order_acknowledgment.netsuite_supplier_address.parse_address_with_ai"
            ),
        ]

        # Apply all patches
        for patch in mock_patches:
            patch.start()

        try:
            # Configure the mocks

            # Mock RPA fetch
            from didero.workflows.core.nodes.purchase_orders.netsuite_sync import (
                fetch_netsuite_po_data,
            )

            fetch_netsuite_po_data.side_effect = self._mock_fetch_netsuite_po_data

            # Mock OpenAI calls for address parsing
            from didero.ai.order_acknowledgment.netsuite_shipping_address import (
                parse_address_with_ai as shipping_parse,
            )
            from didero.ai.order_acknowledgment.netsuite_supplier_address import (
                parse_address_with_ai as vendor_parse,
            )

            # Define proper async side effect functions that return mock data
            async def shipping_side_effect(
                address, address_type, transaction_id="", **kwargs
            ):
                return await self._mock_openai_address_parsing(
                    address, "shipping", transaction_id
                )

            async def vendor_side_effect(
                address, address_type, transaction_id="", **kwargs
            ):
                return await self._mock_openai_address_parsing(
                    address, "vendor", transaction_id
                )

            shipping_parse.side_effect = shipping_side_effect
            vendor_parse.side_effect = vendor_side_effect

            # Create sync parameters
            sync_params = SyncNetSuitePOParams(
                purchase_order_id=po.pk,
                netsuite_po_number=po.po_number,
                customer_name=self.customer,
                transaction_id=self.transaction_id,
            )

            # Record PO state before sync
            po_before = await sync_to_async(PurchaseOrder.objects.get)(id=po.pk)
            po_before_dict = await self._po_to_dict(po_before)

            logger.info("PO state before sync", po_id=po.pk, po_data=po_before_dict)

            # Execute the sync with the mocked functions
            error, result = await netsuite_po_sync_worker(sync_params)

            if error:
                logger.error(
                    "Sync failed",
                    error_type=error.error_type,
                    message=error.message,
                    details=error.error_details,
                )
                return False, None

            if not result:
                logger.error("Sync returned no result")
                return False, None

            # Record PO state after sync
            po_after = await sync_to_async(PurchaseOrder.objects.get)(id=po.pk)
            po_after_dict = await self._po_to_dict(po_after)

            logger.info("PO state after sync", po_id=po.pk, po_data=po_after_dict)

            # Verify changes
            changes = self._compare_po_states(po_before_dict, po_after_dict)

            logger.info(
                "Sync completed successfully",
                po_id=po.pk,
                updated_fields=result.updated_fields,
                changes=changes,
            )

            # Verify RPA mock was called
            assert fetch_netsuite_po_data.called, "Mock fetch function was not called"

            # Verify address mocks were called
            # assert shipping_parse.called or vendor_parse.called, "Address parsing mocks were not called"

            return True, changes

        except Exception as e:
            logger.error("Error during PO sync test", error=str(e), exc_info=True)
            return False, None
        finally:
            # Stop all patches
            for patch in mock_patches:
                patch.stop()

    async def _mock_fetch_error(self, params: SyncNetSuitePOParams):
        """Mock RPA fetch that returns an error."""
        # Create an error result with the right structure
        from didero.workflows.core.nodes.purchase_orders.netsuite_sync import (
            NetSuiteSyncError,
            NetSuiteSyncErrorType,
            RPAFetchResult,
        )

        error = NetSuiteSyncError.create(
            NetSuiteSyncErrorType.RPA_POLLING_FAILED,
            {"job_id": "test-job-id", "status": "FAILED"},
            "Simulated RPA job failure",
        )

        return RPAFetchResult(
            success=False,
            po_data={},
            line_items_data=[],
            error=error,
            transaction_id=params.transaction_id,
        )

    async def test_error_handling(self) -> bool:
        """
        Test error handling in RPA integration.

        Simulates various error scenarios to verify proper error handling.

        Returns:
            bool: True if error handling works as expected, False otherwise
        """
        logger.info("Testing error handling")

        # Setup mocks for OpenAI to prevent real API calls
        openai_patches = [
            mock.patch(
                "didero.ai.order_acknowledgment.netsuite_shipping_address.parse_address_with_ai"
            ),
            mock.patch(
                "didero.ai.order_acknowledgment.netsuite_supplier_address.parse_address_with_ai"
            ),
        ]

        # Apply OpenAI patches for all tests
        for patch in openai_patches:
            patch.start()

        try:
            # Configure the OpenAI mocks
            from didero.ai.order_acknowledgment.netsuite_shipping_address import (
                parse_address_with_ai as shipping_parse,
            )
            from didero.ai.order_acknowledgment.netsuite_supplier_address import (
                parse_address_with_ai as vendor_parse,
            )

            # Define proper async side effect functions that return mock data
            async def shipping_side_effect(
                address, address_type, transaction_id="", **kwargs
            ):
                return await self._mock_openai_address_parsing(
                    address, "shipping", transaction_id
                )

            async def vendor_side_effect(
                address, address_type, transaction_id="", **kwargs
            ):
                return await self._mock_openai_address_parsing(
                    address, "vendor", transaction_id
                )

            shipping_parse.side_effect = shipping_side_effect
            vendor_parse.side_effect = vendor_side_effect

            # 1. Test invalid PO ID
            with mock.patch(
                "didero.workflows.core.nodes.purchase_orders.netsuite_sync.fetch_netsuite_po_data",
                side_effect=self._mock_fetch_netsuite_po_data,
            ):
                invalid_po_params = SyncNetSuitePOParams(
                    purchase_order_id=999999999,  # Non-existent PO ID
                    netsuite_po_number="INVALID-PO",
                    customer_name=self.customer,
                    transaction_id=f"{self.transaction_id}-invalid-po",
                )

                error, result = await netsuite_po_sync_worker(invalid_po_params)

                if not error or error.error_type != NetSuiteSyncErrorType.PO_NOT_FOUND:
                    logger.error(
                        "Invalid PO test failed",
                        expected=NetSuiteSyncErrorType.PO_NOT_FOUND,
                        actual=error.error_type if error else "No error",
                    )
                    return False

                logger.info(
                    "Invalid PO test passed",
                    error_type=error.error_type,
                    message=error.message,
                )

            # 2. Test RPA service error
            with mock.patch(
                "didero.workflows.core.nodes.purchase_orders.netsuite_sync.fetch_netsuite_po_data",
                side_effect=self._mock_fetch_error,
            ):
                # Create a valid PO for this test
                test_po = await self.test_create_or_find_po()
                if not test_po:
                    logger.error("Failed to create test PO for error handling test")
                    return False

                rpa_error_params = SyncNetSuitePOParams(
                    purchase_order_id=test_po.pk,
                    netsuite_po_number=test_po.po_number,
                    customer_name=self.customer,
                    transaction_id=f"{self.transaction_id}-rpa-error",
                )

                error, result = await netsuite_po_sync_worker(rpa_error_params)

                if (
                    not error
                    or error.error_type != NetSuiteSyncErrorType.RPA_POLLING_FAILED
                ):
                    logger.error(
                        "RPA error test failed",
                        expected=NetSuiteSyncErrorType.RPA_POLLING_FAILED,
                        actual=error.error_type if error else "No error",
                    )
                    return False

                logger.info(
                    "RPA error test passed",
                    error_type=error.error_type,
                    message=error.message,
                )

            # Additional error tests could be added here

            return True

        except Exception as e:
            logger.error(
                "Unexpected error in error handling test", error=str(e), exc_info=True
            )
            return False
        finally:
            # Stop all OpenAI patches
            for patch in openai_patches:
                patch.stop()

    async def test_data_processing(self) -> bool:
        """
        Test data processing functions.

        Tests the flattening and normalization functions with mock RPA data.

        Returns:
            bool: True if data processing works correctly, False otherwise
        """
        logger.info("Testing data processing functions")

        try:
            # Use our sample data
            rpa_data = self.SAMPLE_RPA_RESPONSE

            # Test Stagehand API-optimized data processing
            # New Stagehand system returns API-optimized data directly
            flattened_po = rpa_data.get("purchase_order", {})
            flattened_items = rpa_data.get("line_items", [])

            # Check required fields in flattened PO data
            required_po_fields = ["po_number", "order_status", "vendor"]
            missing_fields = [
                field for field in required_po_fields if field not in flattened_po
            ]

            if missing_fields:
                logger.error(
                    "Missing required fields in flattened PO data",
                    missing_fields=missing_fields,
                )
                return False

            # Verify flattened line items
            if not flattened_items:
                logger.warning("No line items in flattened data")
            else:
                # Check first item for required fields
                first_item = flattened_items[0]
                required_item_fields = ["item_number", "quantity", "description"]
                missing_item_fields = [
                    field for field in required_item_fields if field not in first_item
                ]

                if missing_item_fields:
                    logger.error(
                        "Missing required fields in flattened line items",
                        missing_item_fields=missing_item_fields,
                    )
                    return False

                logger.info(
                    "Line items processed correctly",
                    item_count=len(flattened_items),
                    first_item_fields=list(first_item.keys()),
                )

            # Test PO number normalization
            test_po_number = flattened_po["po_number"]
            normalized = normalize_po_number(test_po_number)
            normalized_for_netsuite = normalize_po_number(
                test_po_number, for_netsuite=True
            )

            logger.info(
                "PO number normalization",
                original=test_po_number,
                normalized=normalized,
                normalized_for_netsuite=normalized_for_netsuite,
            )

            # Basic verification of normalization logic
            if test_po_number.startswith("#") and normalized_for_netsuite.startswith(
                "#"
            ):
                logger.error(
                    "Normalization for NetSuite failed - # prefix not removed",
                    original=test_po_number,
                    normalized=normalized_for_netsuite,
                )
                return False

            logger.info("Data processing tests passed")
            return True

        except Exception as e:
            logger.error("Error in data processing test", error=str(e), exc_info=True)
            return False

    async def _po_to_dict(self, po: PurchaseOrder) -> Dict[str, Any]:
        """Convert a PurchaseOrder object to a dictionary for easier comparison."""
        result = {
            "id": po.id,
            "po_number": po.po_number,
            "order_status": po.order_status,
            "payment_status": po.payment_status,
            "internal_notes": po.internal_notes,
            "vendor_notes": await get_model_attribute(po, "vendor_notes"),
            "payment_terms": po.payment_terms,
            "shipping_method": po.shipping_method,
            "total_cost": str(po.total_cost),
            "total_cost_currency": po.total_cost_currency,
            "supplier_id": po.supplier_id,
        }

        # Get supplier name if supplier exists
        if po.supplier_id:
            supplier_name = await sync_to_async(
                lambda: po.supplier.name if po.supplier else None
            )()
            result["supplier_name"] = supplier_name

        # Get item count and details
        item_count = await sync_to_async(lambda: po.items.count())()
        result["item_count"] = item_count

        if item_count > 0:
            items = await sync_to_async(lambda: list(po.items.all()))()
            item_details = []

            for item in items:
                item_obj = await get_model_attribute(item, "item")
                item_number = (
                    await get_model_attribute(item_obj, "item_number")
                    if item_obj
                    else "Unknown"
                )
                quantity = await get_model_attribute(item, "quantity")
                price = await get_model_attribute(item, "price")

                item_details.append(
                    {
                        "item_number": item_number,
                        "quantity": str(quantity),
                        "price": str(price),
                    }
                )

            result["items"] = item_details

        return result

    def _compare_po_states(
        self, before: Dict[str, Any], after: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compare before and after states of a PO to determine changes."""
        changes = {}

        # Compare basic fields
        for field in [
            "order_status",
            "payment_status",
            "internal_notes",
            "vendor_notes",
            "payment_terms",
            "shipping_method",
            "total_cost",
            "supplier_id",
            "supplier_name",
        ]:
            if field in before and field in after and before[field] != after[field]:
                changes[field] = {
                    "before": before[field],
                    "after": after[field],
                }

        # Compare item counts
        if before.get("item_count") != after.get("item_count"):
            changes["item_count"] = {
                "before": before.get("item_count"),
                "after": after.get("item_count"),
            }

        # Compare items (simplified - just checking counts and overall structure)
        # For a real implementation, we'd need to match items by ID or item_number
        before_items = before.get("items", [])
        after_items = after.get("items", [])

        if len(before_items) != len(after_items):
            changes["items"] = {
                "before_count": len(before_items),
                "after_count": len(after_items),
                "description": "Item count changed",
            }
        elif before_items and after_items:
            # Check if any item details changed
            # This is a simplified check - in reality, we'd need to match items properly
            item_changes = False
            for i in range(min(len(before_items), len(after_items))):
                if before_items[i] != after_items[i]:
                    item_changes = True
                    break

            if item_changes:
                changes["items"] = {
                    "description": "Item details changed",
                }

        return changes


async def get_model_attribute(model_instance: Any, attribute_name: str) -> Any:
    """Helper function to safely get model attribute in async context."""
    return await sync_to_async(lambda: getattr(model_instance, attribute_name, None))()


async def run_tests():
    """Run all tests in the E2E test suite."""
    # Get settings from environment
    rpa_url = os.environ.get("DIDERO_RPA_URL", DEFAULT_RPA_URL)
    rpa_api_key = os.environ.get("DIDERO_RPA_API_KEY", DEFAULT_RPA_KEY)
    test_po_number = os.environ.get("TEST_PO_NUMBER", DEFAULT_TEST_PO)

    logger.info(
        "Starting RPA Service E2E Tests",
        rpa_url=rpa_url,
        test_po=test_po_number,
    )

    # Track overall results
    results = {}
    test_count = 0
    success_count = 0

    # Wrap all database operations in a transaction that will be rolled back
    async def run_tests_in_transaction():
        nonlocal results, test_count, success_count

        # Use Django's atomic transaction that will be rolled back
        def run_in_atomic_transaction():
            with transaction.atomic():
                # Create a savepoint that we'll roll back to
                sid = transaction.savepoint()

                try:
                    # Initialize test suite
                    test_suite = RPAServiceE2ETest(
                        rpa_url=rpa_url,
                        rpa_api_key=rpa_api_key,
                        test_po_number=test_po_number,
                    )

                    # Run all the tests synchronously within transaction
                    return test_suite, sid
                except Exception as e:
                    logger.error(
                        "Error setting up test suite", error=str(e), exc_info=True
                    )
                    transaction.savepoint_rollback(sid)
                    raise

        # Run initial setup in transaction
        test_suite, sid = await sync_to_async(run_in_atomic_transaction)()

        try:
            # Test 1: Test RPA Connection
            logger.info("TEST 1: RPA Connection")
            connection_result = await test_suite.test_rpa_connection()
            results["rpa_connection"] = connection_result
            test_count += 1
            if connection_result:
                success_count += 1

            # Only continue with other tests if connection is successful
            if connection_result:
                # Test 2: Create or Find Test PO
                logger.info("TEST 2: Create/Find PO")
                test_po = await test_suite.test_create_or_find_po()
                results["create_find_po"] = test_po is not None
                test_count += 1
                if test_po:
                    success_count += 1

                    # Test 3: Sync PO
                    logger.info("TEST 3: PO Sync")
                    sync_success, changes = await test_suite.test_sync_po(test_po)
                    results["po_sync"] = sync_success
                    test_count += 1
                    if sync_success:
                        success_count += 1

                # Test 4: Error Handling
                logger.info("TEST 4: Error Handling")
                error_handling_result = await test_suite.test_error_handling()
                results["error_handling"] = error_handling_result
                test_count += 1
                if error_handling_result:
                    success_count += 1

                # Test 5: Data Processing
                logger.info("TEST 5: Data Processing")
                data_processing_result = await test_suite.test_data_processing()
                results["data_processing"] = data_processing_result
                test_count += 1
                if data_processing_result:
                    success_count += 1

            # Return success status
            return success_count == test_count

        finally:
            # Always roll back the transaction to discard all database changes
            await sync_to_async(transaction.savepoint_rollback)(sid)
            logger.info("All database changes have been rolled back")

    # Run all tests within transaction
    success = await run_tests_in_transaction()

    # Print summary
    logger.info(
        "Test Summary",
        total_tests=test_count,
        successful_tests=success_count,
        results=results,
    )

    # Print detailed report
    print("\n" + "=" * 80)
    print("RPA SERVICE INTEGRATION E2E TEST REPORT")
    print("=" * 80)

    print(f"\nTotal Tests: {test_count}")
    print(f"Successful Tests: {success_count}")
    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        print(f"  {test_name}: {status}")

    print("\n" + "=" * 80)
    print("NOTE: All database changes have been rolled back - no data was modified")
    print("=" * 80)

    # Return overall success
    return success


if __name__ == "__main__":
    # Run the async test suite
    success = asyncio.run(run_tests())

    # Exit with appropriate code
    sys.exit(0 if success else 1)
