terraform {
  source = "git::**************:dideroai/didero-terraform-modules.git//sqs?ref=main"
}

include {
  path = find_in_parent_folders()
}

locals {
  deadletter_policy_data = jsondecode(file("${get_terragrunt_dir()}/deadletter-policy.json"))
}

inputs = {
  name                        = "didero-staging-periodic_tasks"
  create_deadletter           = true
  sqs_deadletter_queue_policy = local.deadletter_policy_data
}
