remote_state {
  backend = "s3"
  config = {
    bucket         = "didero-terraform-state-prod"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    encrypt        = true
    region         = "us-east-2"
    dynamodb_table = "didero-terraform-state-prod"
  }
}

terraform {
  extra_arguments "common_vars" {
    commands = ["plan", "apply", "import", "destroy"]

    arguments = [
      "-var-file=${get_parent_terragrunt_dir()}/common.tfvars",
      "-parallelism=2"
    ]
  }
}

iam_role = "arn:aws:iam::855701137767:role/Admin"
