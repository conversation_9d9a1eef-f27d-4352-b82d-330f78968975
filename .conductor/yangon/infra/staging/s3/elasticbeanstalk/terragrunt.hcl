terraform {
  source = "git::**************:dideroai/didero-terraform-modules.git//s3?ref=main"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  bucket_name = "elasticbeanstalk-us-east-2-855701137767"
  public_access_block = {
    block_public_acls       = false
    block_public_policy     = false
    ignore_public_acls      = false
    restrict_public_buckets = false
  }
  s3_policy = <<POLICY
{
    "Version": "2008-10-17",
    "Statement": [
        {
            "Sid": "eb-af163bf3-d27b-4712-b795-d1e33e331ca4",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::855701137767:role/django-ec2"
            },
            "Action": [
                "s3:ListBucket",
                "s3:ListBucketVersions",
                "s3:GetObject",
                "s3:GetObjectVersion"
            ],
            "Resource": [
                "arn:aws:s3:::elasticbeanstalk-us-east-2-855701137767",
                "arn:aws:s3:::elasticbeanstalk-us-east-2-855701137767/resources/environments/*"
            ]
        },
        {
            "<PERSON>": "eb-58950a8c-feb6-11e2-89e0-0800277d041b",
            "Effect": "Deny",
            "Principal": {
                "AWS": "*"
            },
            "Action": "s3:DeleteBucket",
            "Resource": "arn:aws:s3:::elasticbeanstalk-us-east-2-855701137767"
        }
    ]
}
POLICY
}
