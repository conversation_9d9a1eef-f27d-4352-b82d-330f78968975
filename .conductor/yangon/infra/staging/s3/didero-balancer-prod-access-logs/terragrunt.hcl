terraform {
  source = "git::**************:dideroai/didero-terraform-modules.git//s3?ref=main"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  bucket_name        = "didero-balancer-prod-access-logs"
  bucket_key_enabled = true
  tags = {
    prodBalancerAccessLogs = ""
  }
  s3_policy = <<POLICY
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::033677994240:root"
            },
            "Action": "s3:PutObject",
            "Resource": "arn:aws:s3:::didero-balancer-prod-access-logs/AWSLogs/855701137767/*"
        }
    ]
}
POLICY
}
