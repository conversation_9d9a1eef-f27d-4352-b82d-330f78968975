terraform {
  source = "git::**************:dideroai/didero-terraform-modules.git//s3?ref=main"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  bucket_name        = "didero-docs-staging-east-2"
  bucket_key_enabled = true
  tags = {
    customerData = "true"
  }
  cors_rules = [
    {
      allowed_headers = ["*"]
      allowed_methods = ["GET", "POST", "PUT", "DELETE", "HEAD"]
      allowed_origins = ["https://staging-app.didero.ai"]
      expose_headers  = []
    }
  ]
}
