# Order Acknowledgement Workflow Human Validation Setting

This document describes the team setting that controls whether human validation is required for order acknowledgements.

## Feature Overview

The Order Acknowledgement workflow has been enhanced to support optional human validation. When human validation is disabled, the system will automatically accept order acknowledgements that pass all validation checks and move the PO to the "Awaiting Shipment" status.

This is similar to the PO Creation workflow, which has a corresponding setting to control whether human validation is required for POs.

## Implementation

### Team Setting

A new team setting has been added: `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED`. This setting defaults to `True` to maintain backward compatibility with the existing behavior.

When this setting is `True`:
- The workflow will create a human validation task for order acknowledgements
- Users must manually approve or reject the order acknowledgement

When this setting is `False`:
- The workflow will automatically approve order acknowledgements that pass all validation checks
- The PO will be moved to "Awaiting Shipment" status automatically
- A success notification will be created instead of a human validation task
- A comment will be added to the PO indicating that the order acknowledgement was automatically accepted

### Changes

1. Added a new enum value in `TeamSettingEnums`: `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED`
2. Added a default value in `team_setting_utils.py`: `DEFAULT_TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED = True`
3. Updated the `create_default_team_settings` function to include the new setting
4. Created a migration to add the setting to all existing teams
5. Modified the `order_acknowledgement_post_validation_actions` function to check the setting and act accordingly
6. Added a new task type for success notifications: `ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION`

## How to Use

The setting can be toggled at the team level. To enable automatic acceptance of order acknowledgements:

1. Set the `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED` setting to `False` for the team
2. Order acknowledgements that pass all validation checks will be automatically accepted
3. The PO will be moved to "Awaiting Shipment" status
4. Users will receive a success notification instead of a validation task

## API Example

To update the setting for a team:

```python
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums

# Disable human validation for order acknowledgements
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED.value,
    team=team,
    defaults={"value": False},
)
```

## Database Migration

The migration file `0048_add_order_ack_human_validation_setting.py` adds the new setting to all existing teams with a default value of `True` to maintain backward compatibility with the existing behavior.