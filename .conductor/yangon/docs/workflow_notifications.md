# Workflow Notifications in Didero API

This document describes how workflow notifications are handled in the Didero API, particularly focusing on the mechanism for determining notification recipients.

## Overview

Workflow notifications are generated at various points in the Purchase Order Creation, Order Acknowledgement, and Shipment workflows. These notifications inform users about important events such as:

- Successful creation of a Purchase Order
- Successful processing of an Order Acknowledgement
- Successful processing of a Shipment
- Validation requirements for workflow steps

## Recipient Determination

A central utility function `get_notification_recipient()` is used to determine who should receive workflow notifications. This function follows a priority-based approach:

1. **Purchase Order's placed_by User**: If the purchase order has a `placed_by` field set, that user will receive the notification.
2. **Team's Default Requestor**: If no `placed_by` user is set, the team's default requestor (if configured) will receive the notification.
3. **AI User**: As a fallback, the team's AI user will receive the notification.

This approach ensures that notifications are routed to the most appropriate user, with a clear fallback hierarchy.

## Implementation

The notification recipient logic is implemented in `/didero/workflows/utils.py`:

```python
def get_notification_recipient(purchase_order: "PurchaseOrder", team: Optional["Team"] = None) -> Optional["User"]:
    """
    Determine the appropriate recipient for workflow notifications based on priority.
    
    Priority order:
    1. Purchase order's placed_by user (if available)
    2. Team's default_requestor (if available)
    3. Team's AI user as a fallback
    
    Args:
        purchase_order: The PurchaseOrder instance
        team: Optional Team instance (will use purchase_order.team if not provided)
    
    Returns:
        User instance to receive the notification, or None if no appropriate user found
    """
    # Use purchase_order's team if team not provided
    team = team or purchase_order.team
    
    # Priority 1: Purchase order's placed_by
    if purchase_order.placed_by:
        return purchase_order.placed_by
        
    # Priority 2: Team's default_requestor
    if team and hasattr(team, 'default_requestor') and team.default_requestor:
        return team.default_requestor
        
    # Priority 3: AI user as fallback
    return get_didero_ai_user(team=team)
```

## Usage in Workflows

This centralized function is used in the Order Acknowledgement, Purchase Order Creation, and Shipment workflows:

### Order Acknowledgement Workflow

In the Order Acknowledgement workflow (`order_ack.py`), the function is used to determine who should receive success notifications when an order acknowledgement is automatically accepted:

```python
def create_order_acknowledgement_success_notification(
    purchase_order: PurchaseOrder,
    order_acknowledgement: OrderAcknowledgement,
    email_id: str,
):
    # Use the shared helper function to determine notification recipient
    user = get_notification_recipient(purchase_order)
    if not user:
        logger.warning(
            "No notification recipient found for team",
            team_id=purchase_order.team.id if purchase_order.team else None,
            po_id=purchase_order.id,
            po_number=purchase_order.po_number,
        )
        return
    
    # Create the notification using the determined user
    # ... notification creation code ...
```

### Purchase Order Creation Workflow

Similarly, in the Purchase Order Creation workflow (`activities.py`), the function determines who should receive success notifications when a PO is automatically created and issued:

```python
# Use the helper function to get the appropriate notification recipient
notification_user = get_notification_recipient(purchase_order)

# Only create notification if we have a recipient
if notification_user:
    task = create_task_v2(
        task_type=TaskTypeName.PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION,
        user=notification_user,
        # ... task creation parameters ...
    )
```

### Shipment Workflow

In the Shipment workflow (`shipments.py`), the function is used to determine who should receive success notifications when a shipment is automatically processed:

```python
def create_shipment_success_notification(
    order: "PurchaseOrder", shipment: "Shipment", email_id: Optional[str] = None
):
    # Use the shared helper function to determine notification recipient
    user = get_notification_recipient(order)
    if not user:
        logger.warning(
            "No notification recipient found for team",
            team_id=order.team.id if order.team else None,
            order_id=order.id,
            shipment_id=shipment.id,
        )
        return
        
    # Create the notification using the determined user
    # ... notification creation code ...
```

## Team Configuration

### Default Recipient

For teams that want notifications to go to a specific user by default, administrators should:

1. Set the team's `default_requestor` field to the desired user
2. Ensure the user has appropriate permissions to view and manage purchase orders

This configuration will ensure that workflow notifications are routed to the designated team member whenever the `placed_by` user is not available.

### Human Validation Settings

Each workflow has a team setting that controls whether human validation is required:

- **PO Creation**: `TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED` 
- **Order Acknowledgement**: `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED`
- **Shipment**: `TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED`

When these settings are enabled (set to `True`, which is the default), human validation tasks are created for administrators to review before approving. When disabled, the system will automatically process the operation and send a success notification to the appropriate recipient.

## Benefits

This approach provides several benefits:

1. **Consistent notification routing**: A single, centralized function determines notification recipients across all workflows
2. **Clear priority hierarchy**: Well-defined fallback mechanism ensures notifications always reach an appropriate user
3. **Configurability**: Teams can control who receives notifications by setting their default requestor
4. **Reduced notification noise**: By targeting specific users rather than all team members, we reduce notification fatigue