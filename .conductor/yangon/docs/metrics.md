# Didero API Metrics

This document provides a comprehensive overview of metrics collected throughout the Didero API using OpenTelemetry. These metrics are designed to give visibility into application performance, business processes, and system health.

## Metric Types

OpenTelemetry supports several types of metrics, each suited to different measurement needs:

- **Counter**: Monotonically increasing values that only go up (e.g., request count)
- **Histogram**: Distribution of values with configurable buckets (e.g., request duration)
- **Gauge**: Values that can go up and down (e.g., active connections)
- **UpDownCounter**: Values that can increment or decrement (e.g., queue size)

## Metric Categories

Our metrics are organized into logical categories based on the system components they monitor:

## HTTP Metrics

These metrics track web request performance and error rates.

| Metric Name | Type | Description | Rationale | Attributes |
|-------------|------|-------------|-----------|------------|
| `http.server.request.count` | Counter | Count of HTTP requests | Tracks overall API usage and load | `http.method`, `http.route`, `http.status_code`, `http.status_class` |
| `http.server.duration` | Histogram | Duration of HTTP requests in milliseconds | Measures API response time performance | `http.method`, `http.route` |
| `http.server.exceptions` | Counter | Count of exceptions during HTTP requests | Tracks API errors | `http.method`, `http.route`, `exception.type` |

**Rationale**: HTTP metrics provide critical visibility into the API's external interface performance. They help identify slow endpoints, error-prone routes, and unexpected usage patterns. The path normalization in the middleware prevents high cardinality by replacing UUIDs and numeric IDs with placeholders.

## Database Metrics

These metrics monitor database interactions and errors.

| Metric Name | Type | Description | Rationale | Attributes |
|-------------|------|-------------|-----------|------------|
| `database.errors` | Counter | Count of database errors | Tracks database connectivity and query issues | `http.route`, `error.type` |

**Rationale**: Database errors are a common source of performance issues and application failures. Tracking these separately allows for quicker diagnosis of database-related problems versus application logic errors.

## Celery Task Metrics

These metrics provide visibility into asynchronous task processing.

| Metric Name | Type | Description | Rationale | Attributes |
|-------------|------|-------------|-----------|------------|
| `celery.task.started` | Counter | Count of Celery tasks started | Tracks task creation rate and volume | `celery.task_name`, `celery.queue` |
| `celery.task.completed` | Counter | Count of Celery tasks completed | Tracks task completion success/failure rates | `celery.task_name`, `celery.queue`, `celery.status` |
| `celery.task.duration` | Histogram | Duration of Celery task execution in milliseconds | Measures task processing efficiency | `celery.task_name`, `celery.queue` |

**Rationale**: Asynchronous tasks are critical for background processing but can be difficult to monitor. These metrics provide visibility into task throughput, success rates, and processing times, helping identify bottlenecks or problematic tasks.

## Business Process Metrics

These metrics track core business operations.

| Metric Name | Type | Description | Rationale | Attributes |
|-------------|------|-------------|-----------|------------|
| `purchase_order.created` | Counter | Count of purchase orders created | Tracks business transaction volume | Varies by implementation |
| `email.processed` | Counter | Count of emails processed | Tracks email processing load | Varies by implementation |
| `document.processed` | Counter | Count of documents processed | Tracks document processing volume | Varies by implementation |

**Rationale**: Business process metrics provide visibility into the application's core functionality, helping to measure throughput and identify patterns in usage. They're essential for understanding how the application is being used in production.

## Workflow Metrics

These metrics track workflow execution and performance.

| Metric Name | Type | Description | Rationale | Attributes |
|-------------|------|-------------|-----------|------------|
| `workflow.trigger.count` | Counter | Count of workflow triggers by type and source | Tracks workflow initiation | `team.id`, `workflow.type`, `workflow.trigger`, `trigger.source`, `email.id` |
| `workflow.skip.count` | Counter | Count of skipped workflow triggers by reason | Tracks non-execution reasons | `team.id`, `workflow.type`, `skip.reason`, `trigger.source`, `email.id` |
| `workflow.execution.count` | Counter | Count of workflow executions | Tracks overall workflow throughput | `team.id`, `workflow.type`, `status` |
| `workflow.node.execution.count` | Counter | Count of workflow node executions | Tracks workflow component usage | `team.id`, `workflow.type`, `node.type`, `status` |
| `workflow.execution.duration` | Histogram | Duration of workflow executions in milliseconds | Measures end-to-end workflow performance | `team.id`, `workflow.type`, `status` |
| `workflow.node.execution.duration` | Histogram | Duration of workflow node executions in milliseconds | Identifies slow workflow components | `team.id`, `workflow.type`, `node.type`, `status` |

**Rationale**: Workflow metrics are essential for monitoring the behavior of complex business processes that span multiple components. They provide visibility into execution paths, performance bottlenecks, and error conditions within multi-step operations.

## Email Processing Metrics

These metrics track email categorization and workflow triggering.

| Metric Name | Type | Description | Rationale | Attributes |
|-------------|------|-------------|-----------|------------|
| `email.categorization.count` | Counter | Count of emails categorized by type | Tracks AI categorization usage | `team.id`, `email.category`, `email.categorization.confidence`, `email.id` |
| `email.workflow.trigger.count` | Counter | Count of workflows triggered by email category | Tracks automation from email | `team.id`, `workflow.type`, `workflow.trigger`, `email.category`, `email.id` |

**Rationale**: Email processing metrics are specific to the email ingestion and categorization capabilities of the platform. They help monitor the effectiveness of email categorization and track how often different types of emails trigger automated workflows.

## Best Practices for Using Metrics

1. **Dashboards**: Create CloudWatch dashboards that group related metrics together (e.g., HTTP performance, Task processing, Business operations)

2. **Alerts**: Set up CloudWatch alarms for critical thresholds:
   - High error rates (e.g., HTTP 5xx errors > 1% of requests)
   - Slow response times (e.g., p95 latency > 1000ms)
   - Failed task processing (e.g., task failure rate > 5%)

3. **Dimensions**: When querying metrics, use the appropriate dimensions to filter and group data:
   - By endpoint for HTTP metrics
   - By task name for Celery metrics
   - By team for multi-tenant operations

4. **Correlation**: Correlate metrics with traces for deeper debugging:
   - Start with a metric anomaly (e.g., spike in duration)
   - Find traces for the affected requests
   - Analyze trace spans to identify the specific component causing the issue

## Future Metric Enhancements

Potential areas for expanding our metrics collection:

1. **Resource Usage**: Memory, CPU, and connection pool utilization
2. **Caching**: Cache hit/miss rates and latency
3. **External Services**: Third-party API call volumes and latencies
4. **User Experience**: Client-side latency and error rates
5. **Business Outcomes**: Success rates for key workflows

## Implementation Notes

- All metrics are exported to AWS CloudWatch via the OpenTelemetry collector
- Metric data is sent every 60 seconds to reduce CloudWatch API calls
- Normalization is applied to HTTP paths to prevent high cardinality
- Options requests are filtered out to reduce noise