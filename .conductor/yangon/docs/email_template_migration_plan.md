# Email Template Migration Plan

**Author**: <PERSON>  
**Date**: May 28, 2025

## Overview

This document outlines the plan to migrate the TEAM_PO_DEFAULT_EMAIL_TEMPLATE from the TeamSetting system to the EmailTemplate system, providing a more robust and flexible email templating solution.

## Current State

- Email templates are stored as TeamSettings with JSON values containing `subject` and `template` (body)
- Frontend performs simple string replacement for variables like `{PO_NUMBER}`, `{FROM_NAME}`, etc.
- Limited flexibility and no support for supplier-specific templates

## Proposed Solution

### Backend Changes

#### 1. EmailTemplate Model Enhancements

Add email type choices to the EmailTemplate model:

```python
# didero/emails/models.py
class EmailTemplateType(models.TextChoices):
    PURCHASE_ORDER_DEFAULT = "purchase_order_default", "Purchase Order Default"
    ORDER_ACKNOWLEDGEMENT = "order_acknowledgement", "Order Acknowledgement"  
    SHIPMENT_NOTIFICATION = "shipment_notification", "Shipment Notification"
    # Add more as needed

# Update EmailTemplate model
class EmailTemplate(BaseModel):
    # ... existing fields ...
    email_type = models.Char<PERSON><PERSON>(
        max_length=50, 
        choices=EmailTemplateType.choices,
        db_index=True
    )
```

#### 2. EmailTemplate API Endpoints

Create serializer and viewset for EmailTemplate:

```python
# didero/emails/serializers.py
class EmailTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailTemplate
        fields = [
            'id', 'team', 'supplier', 'template_name', 
            'email_type', 'email_to', 'email_cc', 'email_bcc',
            'subject', 'body', 'created_at', 'modified_at'
        ]

# didero/emails/views.py
class EmailTemplateViewSet(APIView, TeamOwnerFilteredModelView):
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAuthenticated]
    model = EmailTemplate
    
    @action(detail=False, methods=['post'], url_path='render')
    def render_template(self, request):
        """
        Render an email template with provided variables
        
        Request:
        {
            "email_type": "purchase_order_default",
            "team_id": 123,
            "supplier_id": 456,  # Optional
            "variables": {
                "po_number": "PO-12345",
                "supplier_name": "ACME Corp",
                // ... any other variables
            }
        }
        
        Response:
        {
            "subject": "New Purchase Request - PO-12345",
            "body": "<p>Hello ACME Corp...</p>",
            "to": ["<EMAIL>"],
            "cc": [],
            "bcc": [],
            "template_id": 789
        }
        """
```

#### 3. Data Migration

Create migration to convert existing TeamSettings to EmailTemplates:

```python
# didero/emails/migrations/00XX_migrate_team_po_templates.py
def migrate_po_templates(apps, schema_editor):
    TeamSetting = apps.get_model('users', 'TeamSetting')
    EmailTemplate = apps.get_model('emails', 'EmailTemplate')
    
    for setting in TeamSetting.objects.filter(name='TEAM_PO_DEFAULT_EMAIL_TEMPLATE'):
        if setting.value and isinstance(setting.value, dict):
            subject = setting.value.get('subject', '')
            body = setting.value.get('template', '')
            
            # Convert placeholders from {VAR} to {{ var }}
            replacements = [
                ('{PO_NUMBER}', '{{ po_number }}'),
                ('{FROM_NAME}', '{{ placed_by_first_name }}'),
                ('{REQUESTED_SHIP_DATE}', '{{ po_requested_ship_date }}'),
                ('{ESTIMATED_DELIVERY_DATE}', '{{ estimated_delivery_date }}'),
            ]
            
            for old, new in replacements:
                subject = subject.replace(old, new)
                body = body.replace(old, new)
            
            EmailTemplate.objects.create(
                team=setting.team,
                template_name="Purchase Order Default Email",
                email_type="purchase_order_default",
                subject=subject,
                body=body,
            )
```

#### 4. URL Configuration

Add EmailTemplate endpoints to urls.py:

```python
# didero/emails/urls.py
router.register("email-templates", EmailTemplateViewSet, basename="email-template")
```

### Frontend Changes

#### 1. Email Template Variables Utility

Create utility to extract all available variables:

```typescript
// src/utils/email-template-variables.ts
export const extractPurchaseOrderVariables = (order: PurchaseOrder | null | undefined) => {
  // Extract all PO fields as template variables
  // See implementation in previous messages
};

export const extractSupplierVariables = (supplier: Supplier | null | undefined) => {
  // Extract all Supplier fields as template variables
  // See implementation in previous messages
};

export const buildEmailTemplateVariables = (
  order?: PurchaseOrder | null,
  supplier?: Supplier | null,
) => {
  return {
    ...extractPurchaseOrderVariables(order),
    ...extractSupplierVariables(supplier),
  };
};
```

#### 2. New API Hook

Create hook for rendering email templates:

```typescript
// src/api/email-templates.ts
interface RenderEmailTemplateRequest {
  email_type: string;
  team_id: number;
  supplier_id?: number;
  variables: Record<string, any>;
}

interface RenderEmailTemplateResponse {
  subject: string;
  body: string;
  to: string[];
  cc: string[];
  bcc: string[];
  template_id: number;
}

export const useRenderedEmailTemplate = ({
  token,
  emailType,
  teamId,
  supplierId,
  variables,
}: {
  token: string;
  emailType: string;
  teamId: number;
  supplierId?: number;
  variables: Record<string, any>;
}) => {
  return useQuery({
    queryKey: ['email-template-render', emailType, teamId, supplierId, variables],
    queryFn: () => 
      post<RenderEmailTemplateResponse>('email-templates/render/', token, {
        email_type: emailType,
        team_id: teamId,
        supplier_id: supplierId,
        variables,
      }),
    enabled: !!emailType && !!teamId,
  });
};
```

#### 3. Update Components

Replace usage of `useTeamDefaultEmailTemplate` in OrderDetails.tsx:

```typescript
// Before
const { body, subject } = useTeamDefaultEmailTemplate({
  token,
  poNumber: order?.poNumber ?? '',
  fromName: order?.placedBy?.firstName ?? '',
  requestedShipDate: order?.requestedShipDate,
  estimatedDeliveryDate: order?.shipments[0]?.estimatedDeliveryDate,
});

// After
import { buildEmailTemplateVariables } from '@/utils/email-template-variables';

const { data: emailTemplate } = useRenderedEmailTemplate({
  token,
  emailType: 'purchase_order_default',
  teamId: session.team.id,
  supplierId: order?.supplier?.id,
  variables: buildEmailTemplateVariables(order, supplier),
});

const { body, subject } = emailTemplate || { body: '', subject: '' };
```

## Migration Strategy

### Phase 1: Backend Infrastructure (Week 1)
1. Add email_type field to EmailTemplate model
2. Create EmailTemplate API endpoints
3. Deploy backend changes

### Phase 2: Data Migration (Week 2)
1. Run migration to convert TeamSettings to EmailTemplates
2. Verify all teams have migrated templates
3. Keep TeamSettings as fallback

### Phase 3: Frontend Migration (Week 3)
1. Deploy new email template utilities
2. Update components to use new API
3. Test with select teams

### Phase 4: Cleanup (Week 4)
1. Remove TEAM_PO_DEFAULT_EMAIL_TEMPLATE from TeamSettingEnums
2. Remove old TeamSettings entries
3. Update documentation

## Benefits

1. **Flexibility**: Support for supplier-specific templates
2. **Power**: Full Jinja2 templating capabilities (conditionals, loops, filters)
3. **Extensibility**: Easy to add new template types
4. **Centralization**: All email templates in one system
5. **User-friendly**: Template authors have access to all PO and Supplier fields

## Risks and Mitigation

1. **Data migration errors**: Keep TeamSettings as backup during migration
2. **Template syntax errors**: Add validation endpoint to check templates
3. **Performance**: Consider caching rendered templates if needed
4. **User confusion**: Provide documentation and examples for template authors

## Success Metrics

- All teams successfully migrated to EmailTemplate system
- No increase in email sending errors
- Positive feedback from users on template flexibility
- At least 10% of teams create supplier-specific templates within 30 days