# Team Workflows Onboarding: Technical Guide

## Introduction

This technical guide explains the architecture and detailed process for setting up the three core workflows in the Didero platform:
- Purchase Order (PO) Creation
- Order Acknowledgment
- Shipment Tracking

This document is intended for developers, system administrators, and technical staff who need to understand the underlying mechanisms or automate the onboarding process.

## System Architecture

### Team Model Structure

Teams in Didero are structured around these key models:
- `Team`: The core entity representing a company or organization
- `TeamDomain`: Associates email domains with teams
- `TeamMembership`: Links users to teams with roles
- `TeamSetting`: Stores configuration options for teams and users

### Workflow Components

Each workflow consists of these components:
- `WorkflowType`: Defines the workflow category (e.g., `PURCHASE_ORDER_CREATION`)
- `WorkflowTrigger`: Defines what initiates the workflow (e.g., `ON_PURCHASE_ORDER_EMAIL_RECEIVED`)
- `UserWorkflow`: The database record linking a workflow to a team
- `WorkflowSnapshot`: The specific workflow configuration and graph
- Workflow definition files: Define the DAG (Directed Acyclic Graph) of workflow nodes

## Manual Setup Process

### 1. Team Creation

Teams can be created manually or through user registration:

```python
from didero.users.models.team_models import Team, TeamDomain

# Create a team
team = Team.objects.create(
    name="New Company",
    # other fields as needed
)

# Associate a domain with the team
TeamDomain.objects.create(
    team=team,
    domain="newcompany.com"
)
```

### 2. Team Settings Configuration

Essential settings for workflows:

```python
from didero.users.utils.team_setting_utils import create_default_team_settings
from didero.users.models.user_team_setting_models import TeamSetting, TeamSettingEnums

# Create all default settings
create_default_team_settings(team)

# Or configure individual settings
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED.value,
    team=team,
    defaults={"value": True}
)
```

### 3. Workflow Creation

Each workflow must be explicitly created for the team:

```python
# In Django shell
from didero.workflows.core.nodes.purchase_orders.po_creation.workflow_definition import create_purchase_order_creation_workflow_for_team
from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.workflow_definition import create_purchase_order_acknowledgement_workflow_for_team
from didero.workflows.core.nodes.purchase_orders.shipment.workflow_definition import create_purchase_order_shipment_workflow_for_team

# Create the workflows
po_workflow = create_purchase_order_creation_workflow_for_team(team.id)
ack_workflow = create_purchase_order_acknowledgement_workflow_for_team(team.id)
ship_workflow = create_purchase_order_shipment_workflow_for_team(team.id)
```

## Automation Through Management Command

The platform includes a management command that automates the onboarding process:

```bash
python manage.py onboard_team_workflows --team-id 123 --enable-human-validation --enable-followups --enable-email-po-creation
```

When using the Django admin interface, you also have additional options:

- **Setting the default requestor**: Select a user who will receive task notifications
- **Enabling task email notifications**: Turn on email notifications for both the default requestor (user-level setting) and the team (team-level setting)
- **Managing existing workflows**: The interface will inform you if workflows already exist and allow you to update them

### Command Implementation

The command's main functionality:

```python
# Inside the handle method of onboard_team_workflows command
def handle(self, *args, **options):
    team_id = options["team_id"]
    enable_human_validation = options["enable_human_validation"]
    enable_followups = options["enable_followups"]
    enable_email_po_creation = options["enable_email_po_creation"]

    team = Team.objects.get(pk=team_id)
    
    # Create default settings
    create_default_team_settings(team)
    
    # Update workflow-specific settings
    if enable_human_validation:
        TeamSetting.objects.update_or_create(
            name=TeamSettingEnums.TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED.value,
            team=team,
            defaults={"value": enable_human_validation}
        )
        # Similar updates for other validation settings
    
    # Create workflows
    po_creation_workflow = create_purchase_order_creation_workflow_for_team(team.id)
    order_ack_workflow = create_purchase_order_acknowledgement_workflow_for_team(team.id)
    shipment_workflow = create_purchase_order_shipment_workflow_for_team(team.id)
```

## Workflow Definitions

### PO Creation Workflow

The PO Creation workflow is defined by this schema:

```python
schema = {
    "nodes": [
        {
            "id": "extract_po_details",
            "type": "action",
            "node_class": "extract_po_details",
            "successors": ["create_po"],
        },
        {
            "id": "create_po",
            "type": "action",
            "node_class": "create_po",
            "successors": ["link_email"],
        },
        {
            "id": "link_email",
            "type": "action",
            "node_class": "link_email",
            "successors": [],
        },
    ]
}
```

### Order Acknowledgment Workflow

The Order Acknowledgment workflow includes validation steps:

```python
schema = {
    "nodes": [
        {
            "id": "A",
            "type": "action",
            "node_class": "extract_order_acknowledgement_details",
            "successors": ["B"],
        },
        {
            "id": "B",
            "type": "action",
            "node_class": "validate_order_acknowledgement_info",
            "successors": ["C"],
        },
        # Additional nodes...
    ]
}
```

### Shipment Workflow

The Shipment workflow handles tracking information:

```python
schema = {
    "nodes": [
        {
            "id": "A",
            "type": "action",
            "node_class": "validate_shipment_email",
            "successors": ["B"],
        },
        {
            "id": "B",
            "type": "action",
            "node_class": "read_shipment_details_from_email",
            "successors": ["C"],
        },
        {
            "id": "C",
            "type": "action",
            "node_class": "create_shipment_from_details",
            "successors": [],
        },
    ]
}
```

## Integration with Temporal

Workflows are executed using Temporal:

```python
async def start_temporal_workflow(instance_uuid, graph_schema, params, temporal_id):
    client = await get_temporal_client()
    await client.start_workflow(
        DagBFSWorkflow.run,
        args=(instance_uuid, graph_schema, params),
        id=temporal_id,
        task_queue="user_workflows",
    )
```

## Email Integration Configuration

Email processing requires proper credential setup:

```python
from didero.emails.models import EmailCredentialNylas

# Create credential (simplified - actual process involves OAuth)
credential = EmailCredentialNylas.objects.create(
    email_address="<EMAIL>",
    team=team
)

# Set as default for team
team.default_po_email_credential = credential
team.save()
```

## Advanced Configuration

### Human Validation Settings

Human validation can be configured differently for each workflow type:

```python
# PO Creation validation
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED.value,
    team=team,
    defaults={"value": True}
)

# Order Acknowledgment validation
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED.value,
    team=team,
    defaults={"value": True}
)

# Shipment validation
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED.value,
    team=team,
    defaults={"value": True}
)
```

### Follow-up Configuration

Configure follow-up behavior using the new global configuration system:

```python
# Enable follow-ups with global configuration
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_FOLLOWUP_GLOBAL_CONFIG.value,
    team=team,
    defaults={"value": {
        "workflow_enabled": True,
        "business_hours_only": True,
        "timezone": "America/New_York"
    }}
)

# Set follow-up wait time (in hours) - legacy setting still used
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_FOLLOWUP_WAIT_TIME_HOURS.value,
    team=team,
    defaults={"value": 48}  # Wait 48 hours before following up
)

# Configure OA follow-up behavior
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_FOLLOWUP_OA_CONFIG.value,
    team=team,
    defaults={"value": {
        "enabled": True,
        "initial_wait_hours": 24,
        "repeat_every_hours": 24,
        "max_attempts_before_task": 3
    }}
)

# Configure shipment follow-up behavior
TeamSetting.objects.update_or_create(
    name=TeamSettingEnums.TEAM_FOLLOWUP_SHIPMENT_CONFIG.value,
    team=team,
    defaults={"value": {
        "enabled": True,
        "time_based_wait_hours": 24,
        "time_based_repeat_hours": 24,
        "date_based_followup_strategy": "balanced",
        "max_attempts_before_task": 5
    }}
)
```

## Troubleshooting and Verification

### Verifying Workflow Creation

```python
from didero.workflows.models import UserWorkflow
from didero.workflows.schemas import WorkflowType, WorkflowTrigger

# Check if PO Creation workflow exists
po_workflow = UserWorkflow.objects.filter(
    workflow_type=WorkflowType.PURCHASE_ORDER_CREATION.value,
    trigger=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
    team=team
).first()

if po_workflow and po_workflow.current_snapshot:
    print("PO Creation workflow is correctly configured")
else:
    print("PO Creation workflow is missing or misconfigured")
```

### Common Issues

1. **Missing default team settings**: Run `create_default_team_settings(team)`
2. **Workflows not registered with Temporal**: Check Temporal server configuration
3. **Email credentials not properly set up**: Verify OAuth tokens and permissions
4. **Human validation failing**: Ensure Didero AI user exists for the team

## Conclusion

This technical guide provides detailed information on the team onboarding process, workflow architecture, and configuration options. For most cases, the automated onboarding through the Django admin interface is recommended, but this guide should help understand the underlying mechanisms or automate the process further if needed.