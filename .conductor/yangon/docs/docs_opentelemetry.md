# Integrating OpenTelemetry with Didero API

This document outlines the implementation of observability in the Didero API using OpenTelemetry for distributed tracing and metrics collection. The metrics and traces are exported to AWS CloudWatch and X-Ray via the AWS Distro for OpenTelemetry (ADOT) collector.

## Prerequisites

The following dependencies are required in `pyproject.toml`:

```python
opentelemetry-api
opentelemetry-sdk
opentelemetry-exporter-otlp
opentelemetry-instrumentation-django
opentelemetry-instrumentation-celery
opentelemetry-instrumentation-psycopg2
opentelemetry-instrumentation-redis
```

## Implementation Overview

### 1. Telemetry Module

The main telemetry module (`didero/telemetry/__init__.py`) configures OpenTelemetry, sets up trace providers, metric providers, and defines core metrics:

```python
# Main components:
- Tracer provider setup with AWS X-Ray compatible configuration
- Meter provider setup for CloudWatch metrics
- HTTP, Database, Celery, and Business metrics defined
- Support for propagating trace context across components
```

### 2. OpenTelemetry Middleware

The middleware (`didero/telemetry/middleware.py`) captures HTTP request metrics and creates properly structured request spans:

```python
class OpenTelemetryMiddleware:
    """
    Django middleware for collecting HTTP request metrics using OpenTelemetry.
    Captures request counts, durations, and errors by endpoint and method.
    """
    
    # Key features:
    # - Creates explicit request spans for proper trace hierarchy
    # - Records metrics for request counts, durations, and exceptions
    # - Normalizes endpoint paths to prevent high cardinality in metrics
    # - Filters OPTIONS requests to reduce noise
    # - Adds error tracking with proper span annotations
```

### 3. Django Settings Configuration

Update `didero/settings/common.py` with OpenTelemetry configuration:

```python
# Add the middleware
MIDDLEWARE = [
    "didero.telemetry.middleware.OpenTelemetryMiddleware",
] + MIDDLEWARE

# OpenTelemetry settings
OTEL_SETTINGS = {
    "SERVICE_NAME": "didero-api",
    "ENVIRONMENT": os.environ.get("DJANGO_SETTINGS_MODULE", "didero.settings.common").split(".")[-1],
    "EXPORTER_OTLP_ENDPOINT": os.environ.get("OTEL_EXPORTER_OTLP_ENDPOINT", "http://localhost:4317"),
}
```

### 4. Initialize Telemetry in WSGI/ASGI

Initialize OpenTelemetry in `didero/wsgi.py`:

```python
# Initialize OpenTelemetry
try:
    from didero.telemetry import setup_telemetry
    from django.conf import settings

    # Initialize OpenTelemetry with settings from Django
    tracer_provider, meter_provider = setup_telemetry(
        app_name=settings.OTEL_SETTINGS.get("SERVICE_NAME", "didero-api"),
        env=settings.OTEL_SETTINGS.get("ENVIRONMENT", "development"),
    )

    # Set up Celery monitoring
    from didero.telemetry import setup_celery_monitoring
    setup_celery_monitoring()

    logger.info(
        "OpenTelemetry instrumentation initialized",
        service_name=settings.OTEL_SETTINGS.get("SERVICE_NAME"),
        exporter_endpoint=settings.OTEL_SETTINGS.get("EXPORTER_OTLP_ENDPOINT"),
    )
except Exception as e:
    # Don't crash the application if telemetry setup fails
    logger.exception("Failed to initialize OpenTelemetry", error=str(e))
```

## Specialized Metrics

In addition to the basic HTTP and database metrics, we've implemented specialized metrics for:

1. **Celery Tasks**:
   - Task started count
   - Task completion count with status
   - Task duration

2. **Business Processes**:
   - Purchase order creation
   - Email processing
   - Document processing

3. **Workflow Metrics**:
   - Workflow triggers
   - Workflow skips with reason
   - Workflow execution counts and durations
   - Email categorization tracking

See the [metrics.md](./metrics.md) document for a comprehensive list of all metrics.

## AWS Distro for OpenTelemetry (ADOT) Configuration

### Local Development

For local development, we use a Docker container running the ADOT collector:

```yaml
# docker-compose.yml
services:
  # ...
  otel-collector:
    image: amazon/aws-otel-collector:latest
    volumes:
      - ./docker/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    command: ["--config=/etc/otel-collector-config.yaml"]
    environment:
      - AWS_REGION=us-east-1
      # AWS credentials are mounted from host via shared volume
    ports:
      - "4317:4317"  # OTLP gRPC
      - "4318:4318"  # OTLP HTTP
```

```yaml
# docker/otel-collector-config.yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    timeout: 30s

exporters:
  awsxray:
    region: us-east-1
  awsemf:
    region: us-east-1
    namespace: DideroAPI
    log_group_name: "didero-api-metrics"
    dimension_rollup_option: NoDimensionRollup
    metric_declarations:
      - dimensions: [[http.method, http.route]]
        metric_name_selectors:
          - http.server.*
      - dimensions: [[celery.task_name, celery.queue]]
        metric_name_selectors:
          - celery.*
      # Add more metric declarations as needed

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [awsxray]
    metrics:
      receivers: [otlp]
      processors: [batch]
      exporters: [awsemf]
```

### AWS ECS Integration

For production deployment in ECS:

1. **Task Definition Updates**:
   - Add the ADOT collector as a sidecar container
   - Configure shared environment variables for service name and environment

2. **Environment Variables**:
   ```
   OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
   OTEL_RESOURCE_ATTRIBUTES=service.name=didero-api,deployment.environment=production
   ```

3. **IAM Permissions**:
   - Ensure the ECS task role has permissions for:
     - X-Ray (`xray:PutTraceSegments`, `xray:PutTelemetryRecords`)
     - CloudWatch (`logs:PutLogEvents`, `logs:CreateLogGroup`, `logs:CreateLogStream`)

## Trace Context Propagation

To ensure proper parent-child relationships in traces:

1. All instrumentation providers use the same tracer provider
2. The middleware creates explicit request-level spans with proper attributes
3. Context propagation is enabled via environment variable
4. Celery tasks preserve context across process boundaries

## CloudWatch Dashboards

We recommend creating CloudWatch dashboards with the following components:

1. **API Performance**:
   - Request rate by endpoint
   - Error rate by endpoint
   - 95th percentile response time

2. **Background Tasks**:
   - Task execution rate by queue
   - Task duration distribution
   - Task error rate

3. **Business Metrics**:
   - Email processing rate
   - Purchase order creation rate
   - Document processing metrics

## AWS SSO Integration for Local Development

For local development with AWS SSO authentication:

1. Use the Makefile targets for AWS SSO credential refresh
2. Configure the ADOT collector to use shared credentials
3. Use `aws sso login --profile didero-dev` to authenticate

## References

- [OpenTelemetry Documentation](https://opentelemetry.io/docs/)
- [AWS Distro for OpenTelemetry](https://aws-otel.github.io/)
- [AWS X-Ray](https://docs.aws.amazon.com/xray/latest/devguide/aws-xray.html)
- [CloudWatch Metrics](https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/working_with_metrics.html)