# Temporal Workflow Refactoring: From Generic DAG to Core Workflows

**Author:** <PERSON>diran

**Date:** May 27, 2025

## Sub Documents

[Workflows Observability](https://www.notion.so/Workflows-Observability-200213e9d835808ea420fd82a4e90ae6?pvs=21)

## Executive Summary

This document outlines our approach to refactoring Didero's Temporal workflow system from a single generic DAG-based workflow executor (`DagBFSWorkflow`) to core, type-safe workflow implementations. The refactoring aims to improve maintainability, type safety, and testing while preserving flexibility for team-specific customizations.

## Current State Analysis

### Architecture Overview

Our current workflow system uses:

- **Single Generic Executor**: `DagBFSWorkflow` handles all workflow types
- **JSON Schema Definitions**: Workflows defined as directed acyclic graphs
- **Node-Based Execution**: Actions and conditions as graph nodes
- **String-Based Registration**: Node classes registered via dictionary mapping

### Current Workflow Types

1. **Purchase Order Approval Flow** - Conditional approval based on thresholds
2. **Purchase Order Creation** - Extract and create POs from emails
3. **Purchase Order Acknowledgement** - Multi-step validation workflow
4. **Purchase Order Shipment** - Process shipment confirmations
5. **Follow-up Workflow** - Already uses core implementation

### Pain Points

- **Type Safety**: String-based node resolution lacks compile-time validation
- **Testing Complexity**: Difficult to test workflows in isolation
- **Debugging**: Indirect execution through graph traversal
- **Performance**: Overhead from generic graph execution
- **Maintenance**: Changes require understanding generic framework

## Proposed Architecture

### Core Principles

1. **Core Over Generic**: Each workflow type gets its own Temporal workflow class
2. **Flexibility Through Composition**: Customization via extension points and configuration
3. **Type-Safe Configuration**: Replace team settings with workflow-specific behavior configs
4. **Gradual Migration**: Implement alongside existing system with feature flags

## Implementation Plan

### 1. Create Workflow Behavior Configuration System

Replace scattered team settings with unified workflow behavior configs:

```python
# models.py
class WorkflowBehaviorConfig(models.Model):
    """Configuration for workflow behavior - replaces team settings"""
    workflow = models.OneToOneField(
        UserWorkflow,
        on_delete=models.CASCADE,
        related_name='behavior_config'
    )
    config = models.JSONField(default=dict)

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)
    modified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    def get_config_as_dataclass(self) -> WorkflowBehaviorConfigBase:
        """Convert JSON to typed dataclass based on workflow type"""
        config_class = WORKFLOW_CONFIG_CLASSES[self.workflow.workflow_type]
        return config_class(**self.config)

```

Define type-safe configuration classes for each workflow:

```python
# schemas.py
from pydantic import BaseModel, Field
from typing import List, Optional
from decimal import Decimal

class WorkflowBehaviorConfigBase(BaseModel):
    """Base configuration for all workflows"""
    enabled: bool = True
    enable_notifications: bool = True
    enable_audit_logging: bool = True
    max_retry_attempts: int = 3
    retry_backoff_seconds: int = 60

class POCreationBehaviorConfig(WorkflowBehaviorConfigBase):
    """PO Creation specific configuration"""
    # Core behavior flags
    require_human_validation: bool = False  # Replaces TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED

    # Validation controls
    validate_supplier: bool = True
    validate_duplicate_po: bool = True
    skip_validation_for_trusted_suppliers: bool = False
    trusted_supplier_ids: List[str] = Field(default_factory=list)

    # Auto-approval rules
    auto_approve_threshold: Optional[Decimal] = None
    auto_approve_currency: str = "USD"

    # Extension points
    post_extraction_activities: List[str] = Field(default_factory=list)
    pre_creation_activities: List[str] = Field(default_factory=list)
    post_creation_activities: List[str] = Field(default_factory=list)

    # Notification settings
    notification_channels: List[str] = Field(default_factory=lambda: ["email"])
    notify_on_validation_required: bool = True

```

### 2. Build Explicit Workflows with Extension Points

Create explicit workflow classes that support team-specific customization:

```python
@workflow.defn
class POCreationWorkflow:
    """Explicit PO Creation workflow with extension points"""

    def __init__(self, workflow_id: str):
        # Load configuration from database
        self.workflow = UserWorkflow.objects.get(id=workflow_id)
        self.config = self.workflow.behavior_config.get_config_as_dataclass()
        self.team_id = str(self.workflow.team_id)

        # Load team-specific extensions
        self.extensions = self._load_extensions()

    def _load_extensions(self) -> Dict[str, List[str]]:
        """Load team-specific activity extensions"""
        # This could come from workflow config or a separate extension registry
        return {
            "after_extraction": self.config.post_extraction_activities,
            "before_creation": self.config.pre_creation_activities,
            "after_creation": self.config.post_creation_activities
        }

    async def run(self, email_id: str) -> POCreationResult:
        # Core workflow logic with extension points
        # See complete example below

```

### 3. Migrate Team Settings to Workflow Configs

Create a migration path from existing team settings:

```python
# management/commands/migrate_workflow_settings.py
def migrate_team_to_workflow_configs(team: Team):
    """Migrate team settings to workflow behavior configs"""

    # Get existing team settings
    po_human_validation = get_team_setting_boolean_value(
        TeamSettingEnums.TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED,
        team
    )

    # Find or create PO Creation workflow
    workflow, created = UserWorkflow.objects.get_or_create(
        team=team,
        workflow_type=WorkflowType.PURCHASE_ORDER_CREATION,
        defaults={
            'trigger': WorkflowTrigger.MANUAL,
            'current_snapshot': create_default_po_creation_snapshot()
        }
    )

    # Create behavior config
    WorkflowBehaviorConfig.objects.update_or_create(
        workflow=workflow,
        defaults={
            'config': {
                'enabled': True,
                'require_human_validation': po_human_validation,
                'validate_supplier': True,
                'enable_notifications': True,
                # Set other defaults
            }
        }
    )

```

## Key Decisions and Trade-offs

### What We're Optimizing For

- **Developer Experience**: Clear, debuggable, type-safe code
- **Maintainability**: Easy to understand and modify
- **Testing**: Comprehensive unit and integration tests
- **Performance**: Direct execution without graph overhead

### What We're Trading Off

- **Dynamic Flexibility**: Can't arbitrarily modify workflow structure
- **Visual Editing**: No GUI-based workflow builder

### Mitigation Strategies

- Extension points provide controlled flexibility
- Configuration handles most customization needs
- Composable activities enable reuse
- Clear patterns make new workflow creation straightforward

### Benefits of Explicit Workflow Metrics

1. **Granular Tracking**: Each workflow type can have specific metrics relevant to its business logic
2. **Performance Monitoring**: Easy to track duration and success rates per workflow type
3. **Business Metrics**: Track business-relevant metrics (PO values, supplier performance, etc.)
4. **Error Attribution**: Clear error tracking with workflow-specific context
5. **SLO Definition**: Define and monitor Service Level Objectives per workflow type

### Integration with Observability Stack

Since we use OpenTelemetry with AWS CloudWatch and X-Ray, we need to configure Temporal to export metrics via OpenTelemetry:

```python
# temporal_setup.py
from temporalio.runtime import Runtime, TelemetryConfig, OpenTelemetryConfig
from opentelemetry import metrics
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from didero.telemetry import get_meter

# Use the existing OTLP endpoint from our OpenTelemetry setup
otlp_endpoint = os.environ.get("OTEL_EXPORTER_OTLP_ENDPOINT", "<http://localhost:4317>")

# Configure Temporal with OpenTelemetry
runtime_metrics = OpenTelemetryConfig(
    # Temporal will use the global OpenTelemetry meter provider
    metrics=metrics.get_meter_provider()
)

runtime = Runtime(
    telemetry=TelemetryConfig(
        metrics=runtime_metrics
    )
)

# Create workflow-specific meters using our centralized meter access
workflow_meter = get_meter("didero.workflow.temporal")

# Define workflow-specific metrics that align with our existing patterns
po_workflow_duration = workflow_meter.create_histogram(
    "workflow.po_creation.duration",
    description="Duration of PO creation workflows",
    unit="ms"
)

po_workflow_errors = workflow_meter.create_counter(
    "workflow.po_creation.errors",
    description="Count of PO creation workflow errors",
    unit="1"
)

```

This integrates with our existing OpenTelemetry setup that exports to AWS CloudWatch via ADOT collector, providing:

- Unified metrics collection across HTTP, Celery, and Temporal workflows
- AWS X-Ray trace correlation
- CloudWatch dashboards for all metrics
- Consistent metric naming and labeling patterns

This approach to metrics provides much better observability than the generic DAG executor, where all workflows share the same metric names and distinguishing between workflow types requires complex label filtering.

## Complete Example: PO Creation Workflow

Here's a complete implementation of the refactored PO Creation workflow incorporating behavior configuration, extension points, and metrics:

```python
from pydantic import BaseModel, Field, validator
from decimal import Decimal
from typing import List, Optional, Dict

class POCreationBehaviorConfig(BaseModel):
    """PO Creation workflow configuration"""
    # Core behavior
    enabled: bool = True
    require_human_validation: bool = False

    # Validation settings
    validate_supplier: bool = True
    validate_duplicate_po: bool = True
    skip_validation_for_trusted_suppliers: bool = False
    trusted_supplier_ids: List[str] = Field(default_factory=list)

    # Auto-approval
    auto_approve_threshold: Optional[Decimal] = None
    auto_approve_currency: str = "USD"

    # Extension points
    post_extraction_activities: List[str] = Field(default_factory=list)
    pre_creation_activities: List[str] = Field(default_factory=list)
    post_creation_activities: List[str] = Field(default_factory=list)

    # Notifications
    enable_notifications: bool = True
    notification_channels: List[str] = Field(default_factory=lambda: ["email"])

    # Retry settings
    max_retry_attempts: int = Field(default=3, ge=1, le=10)
    retry_backoff_seconds: int = Field(default=60, ge=1, le=600)

    @validator('auto_approve_currency')
    def validate_currency(cls, v):
        valid_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD']
        if v not in valid_currencies:
            raise ValueError(f'Currency must be one of {valid_currencies}')
        return v

    class Config:
        json_encoders = {
            Decimal: str
        }

# workflows/po_creation.py
from temporalio import workflow
from temporalio.common import RetryPolicy
from datetime import timedelta
import time

@workflow.defn
class POCreationWorkflow:
    """Explicit PO Creation workflow with configuration and extension points"""

    def __init__(self, workflow_id: str):
        # Load workflow and configuration
        self.workflow = UserWorkflow.objects.get(id=workflow_id)
        self.config = self.workflow.behavior_config.get_config_as_pydantic()
        self.team = self.workflow.team

        # Initialize metrics
        meter = workflow.metric_meter()
        self.po_created_counter = meter.create_counter(
            "po_created_total",
            description="Total number of POs created",
            unit="1"
        )
        self.validation_failures = meter.create_counter(
            "po_validation_failures_total",
            description="Number of PO validation failures",
            unit="1"
        )
        self.po_creation_duration = meter.create_histogram(
            "po_creation_duration_milliseconds",
            description="Time taken to create PO",
            unit="s"
        )

    async def run(self, email_id: str) -> POCreationResult:
        """Main workflow execution"""
        start_time = workflow.now()

        # Check if workflow is enabled
        if not self.config.enabled:
            return POCreationResult(success=False, error="Workflow disabled for team")

        try:
            # Step 1: Extract PO from email
            po_data = await self._extract_po(email_id)

            # Step 2: Run post-extraction extensions
            po_data = await self._run_extensions(
                po_data,
                self.config.post_extraction_activities,
                "post_extraction"
            )

            # Step 3: Validate based on configuration
            validation_result = await self._validate_po(po_data)
            if not validation_result.is_valid:
                self.validation_failures.add(1, {
                    "team_id": str(self.team.id),
                    "reason": validation_result.reason
                })
                return POCreationResult(
                    success=False,
                    error=validation_result.reason
                )

            # Step 4: Check if human validation required
            if self._requires_human_validation(po_data):
                await self._create_human_validation_task(po_data)

                # Wait for human validation signal
                validation_signal = await workflow.wait_for_signal(
                    "human_validation_complete",
                    timeout=timedelta(hours=24)
                )

                if not validation_signal.approved:
                    return POCreationResult(
                        success=False,
                        error=f"Human validation rejected: {validation_signal.reason}"
                    )

            # Step 5: Run pre-creation extensions
            po_data = await self._run_extensions(
                po_data,
                self.config.pre_creation_activities,
                "pre_creation"
            )

            # Step 6: Create the PO
            po = await workflow.execute_activity(
                create_purchase_order,
                CreatePOParams(
                    team_id=str(self.team.id),
                    po_data=po_data,
                    created_from_email=email_id
                ),
                start_to_close_timeout=timedelta(minutes=3),
                retry_policy=self._get_retry_policy()
            )

            # Step 7: Run post-creation extensions
            await self._run_extensions(
                po,
                self.config.post_creation_activities,
                "post_creation"
            )

            # Step 8: Send notifications
            if self.config.enable_notifications:
                await self._send_notifications(po)

            # Record metrics
            duration = (workflow.now() - start_time).total_seconds()
            self.po_created_counter.add(1, {
                "team_id": str(self.team.id),
                "supplier_id": po.supplier_id,
                "auto_approved": str(not self._requires_human_validation(po_data))
            })
            self.po_creation_duration.record(duration, {
                "team_id": str(self.team.id),
                "success": "true"
            })

            return POCreationResult(
                success=True,
                po_id=po.id,
                po_number=po.number
            )

        except Exception as e:
            # Record failure metrics
            duration = (workflow.now() - start_time).total_seconds()
            self.po_creation_duration.record(duration, {
                "team_id": str(self.team.id),
                "success": "false",
                "error_type": type(e).__name__
            })

            # Log error and re-raise
            workflow.logger.error(f"PO creation failed: {str(e)}")
            raise

    async def _extract_po(self, email_id: str) -> ExtractedPO:
        """Extract PO data from email"""
        return await workflow.execute_activity(
            extract_po_from_email,
            email_id,
            start_to_close_timeout=timedelta(minutes=5),
            retry_policy=self._get_retry_policy()
        )

    async def _validate_po(self, po_data: ExtractedPO) -> ValidationResult:
        """Validate PO based on configuration"""
        # Skip validation for trusted suppliers
        if (self.config.skip_validation_for_trusted_suppliers and
            po_data.supplier_id in self.config.trusted_supplier_ids):
            return ValidationResult(is_valid=True)

        # Validate supplier if configured
        if self.config.validate_supplier:
            result = await workflow.execute_activity(
                validate_supplier,
                ValidateSupplierParams(
                    team_id=str(self.team.id),
                    supplier_id=po_data.supplier_id
                ),
                start_to_close_timeout=timedelta(minutes=2)
            )
            if not result.is_valid:
                return result

        # Check for duplicate POs if configured
        if self.config.validate_duplicate_po:
            result = await workflow.execute_activity(
                check_duplicate_po,
                CheckDuplicateParams(
                    team_id=str(self.team.id),
                    po_number=po_data.po_number,
                    supplier_id=po_data.supplier_id
                ),
                start_to_close_timeout=timedelta(minutes=1)
            )
            if not result.is_valid:
                return result

        return ValidationResult(is_valid=True)

    def _requires_human_validation(self, po_data: ExtractedPO) -> bool:
        """Determine if human validation is required"""
        # Always require if configured
        if self.config.require_human_validation:
            return True

        # Check auto-approval threshold
        if self.config.auto_approve_threshold:
            if po_data.total_amount > self.config.auto_approve_threshold:
                return True

        return False

    async def _create_human_validation_task(self, po_data: ExtractedPO):
        """Create a task for human validation"""
        await workflow.execute_activity(
            create_validation_task,
            CreateTaskParams(
                team_id=str(self.team.id),
                task_type="po_creation_validation",
                po_data=po_data,
                workflow_id=workflow.info().workflow_id
            ),
            start_to_close_timeout=timedelta(minutes=1)
        )

    async def _run_extensions(self, data: any, activities: List[str], phase: str) -> any:
        """Run extension activities"""
        result = data
        for activity_name in activities:
            try:
                result = await workflow.execute_activity(
                    activity_name,  # Activity names resolved by Temporal
                    ExtensionActivityParams(
                        team_id=str(self.team.id),
                        phase=phase,
                        data=result
                    ),
                    start_to_close_timeout=timedelta(minutes=2)
                )
            except Exception as e:
                workflow.logger.error(f"Extension activity {activity_name} failed: {e}")
                # Continue with other extensions

        return result

    async def _send_notifications(self, po: PurchaseOrder):
        """Send notifications based on configuration"""
        for channel in self.config.notification_channels:
            await workflow.execute_activity(
                send_notification,
                NotificationParams(
                    team_id=str(self.team.id),
                    channel=channel,
                    notification_type="po_created",
                    data={
                        "po_id": po.id,
                        "po_number": po.number,
                        "supplier_name": po.supplier.name,
                        "total_amount": str(po.total_amount)
                    }
                ),
                start_to_close_timeout=timedelta(minutes=1)
            )

    def _get_retry_policy(self) -> RetryPolicy:
        """Get retry policy from configuration"""
        return RetryPolicy(
            maximum_attempts=self.config.max_retry_attempts,
            initial_interval=timedelta(seconds=self.config.retry_backoff_seconds),
            maximum_interval=timedelta(seconds=self.config.retry_backoff_seconds * 10),
            backoff_coefficient=2.0
        )
```

## Conclusion

This refactoring provides:

- **Clear structure**: Each workflow is explicit and easy to understand
- **Type safety**: Full typing throughout the workflow with Pydantic validation
- **Flexibility**: Teams can customize behavior without code changes
- **Observability**: Built-in metrics and tracing
- **Maintainability**: No generic framework complexity

The migration from team settings to workflow behavior configs ensures all configuration is centralized and version-controlled per workflow instance.
