# Team Onboarding Documentation for Didero Workflows

## Introduction

This document outlines the process of onboarding a new team to the Didero platform, focusing on enabling the three core workflows:
- Purchase Order (PO) Creation
- Order Acknowledgment
- Shipment Tracking

These workflows automate the procurement process from initial PO creation through supplier acknowledgment to final shipment tracking.

## Prerequisites

- Django admin access
- Knowledge of the team's basic information (name, email domain)
- Access to the Django shell

## Step-by-Step Onboarding Process

### Option 1: Onboarding via Django Admin (Recommended)

The simplest way to onboard a new team is through the Django admin interface, which provides a user-friendly workflow setup process:

1. Log in to the Django admin interface
2. Navigate to `Users > Teams`
3. You have two options:
   
   **For a single team:**
   - Open the team's detail page by clicking on the team name
   - Click the green "Setup Workflows" button in the top right corner
   - Select your desired workflow settings and click "Set up workflows"
   
   **For multiple teams:**
   - From the teams list, select one or more teams using the checkboxes
   - From the "Actions" dropdown, select "Set up workflows for selected teams"
   - Configure your workflow settings and click "Set up workflows"

This approach automatically:
- Creates all necessary team settings
- Sets up the three core workflows (PO Creation, Order Acknowledgment, Shipment)
- Configures workflow options according to your selections

### Option 2: Manual Setup Process

If you prefer to set up teams manually or need more control over the process, follow these steps:

#### 1. Create the Team

Teams are automatically created when a user registers with a new email domain. However, you can also create teams manually through the Django admin interface:

1. Log in to the Django admin interface
2. Navigate to `Users > Teams`
3. Click "Add Team"
4. Fill in the required information:
   - **Name**: The company/team name
   - **Default Requestor**: The user who will be the default requestor for POs
   - **Default PO Email Credential**: The email credential to use for sending POs

#### 2. Create Team Settings

Team settings control how workflows behave. The platform provides default settings, but you should verify them:

1. Navigate to `Users > Team Settings`
2. Click "Add Team Setting" to add any missing settings
3. Ensure the following key settings are configured:

   **PO Creation Settings:**
   - `TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED`: Set to `True` if human validation is required
   - `TEAM_PO_DEFAULT_EMAIL_TEMPLATE`: Default email template for PO communication

   **Order Acknowledgment Settings:**
   - `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED`: Set to `True` if human validation is required

   **Shipment Settings:**
   - `TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED`: Set to `True` if human validation is required

   **Follow-up Settings:**
   - `TEAM_FOLLOWUP_GLOBAL_CONFIG`: Set `workflow_enabled` to `true` to enable automated follow-ups
   - `TEAM_FOLLOWUP_WAIT_TIME_HOURS`: Hours to wait before following up (default: 24)

Alternatively, you can use the management command to create default settings:

```bash
python manage.py create_default_user_team_settings --create-settings-for-team TEAM_ID
```

#### 3. Create Workflows

The three core workflows must be explicitly created for the team. This is done through the Django shell:

1. Open a Django shell:
   ```bash
   python manage.py shell
   ```

2. Create the PO Creation Workflow:
   ```python
   from didero.workflows.core.nodes.purchase_orders.po_creation.workflow_definition import create_purchase_order_creation_workflow_for_team
   create_purchase_order_creation_workflow_for_team("TEAM_ID")
   ```

3. Create the Order Acknowledgment Workflow:
   ```python
   from didero.workflows.core.nodes.purchase_orders.order_acknowledgement.workflow_definition import create_purchase_order_acknowledgement_workflow_for_team
   create_purchase_order_acknowledgement_workflow_for_team("TEAM_ID")
   ```

4. Create the Shipment Workflow:
   ```python
   from didero.workflows.core.nodes.purchase_orders.shipment.workflow_definition import create_purchase_order_shipment_workflow_for_team
   create_purchase_order_shipment_workflow_for_team("TEAM_ID")
   ```

#### 4. Configure Email Processing

For the workflows to function properly, email processing must be configured:

1. Navigate to `Emails > Nylas Credentials` in the Django admin
2. Add a new credential for the team's email account
3. Ensure the credential is associated with the team
4. Make sure the credential is set as the team's default PO email credential

### Verify Configuration (Both Options)

After completing the setup:

1. Navigate to `Workflows > User Workflows` in the Django admin
2. Verify that all three workflows are created for the team
3. Check `Users > Team Settings` to ensure all required settings are present

## Command-Line Automation

While the Django admin interface is the recommended way to set up team workflows, a command-line interface is also available for automation purposes or batch processing.

### Management Command

The platform includes a management command that automates the team onboarding process:

```bash
# Basic onboarding with default settings
python manage.py onboard_team_workflows --team-id 123

# Enable human validation for all workflows
python manage.py onboard_team_workflows --team-id 123 --enable-human-validation

# Enable automatic follow-ups
python manage.py onboard_team_workflows --team-id 123 --enable-followups

# Enable all features
python manage.py onboard_team_workflows --team-id 123 --enable-human-validation --enable-followups
```

This command is also accessible from the Django admin interface through:
1. The "Setup Workflows" button on a team's detail page
2. The "Set up workflows for selected teams" bulk action on the teams list page

### Implementation Details

The onboarding process (both via admin interface and command line) performs these steps:
1. Creates default team settings if they don't exist
2. Updates workflow-specific settings based on the provided options
3. Creates all three required workflows (PO Creation, Order Acknowledgment, Shipment)
4. Associates the workflows with the team

## Troubleshooting

Common issues and solutions:

1. **Team settings not applied**: Run the `create_default_user_team_settings` command for the specific team
2. **Workflows not triggering**: Verify that the workflows exist in the admin and are correctly linked to the team
3. **Email processing not working**: Check the Nylas credentials and ensure they are properly configured
4. **Human validation not functioning**: Verify the human validation settings are enabled and the Didero AI user exists

## Conclusion

Following this guide, you should be able to successfully onboard a new team with all the necessary workflows and configurations. The provided script automates most of this process, but always verify the setup in the Django admin interface to ensure everything is correctly configured.