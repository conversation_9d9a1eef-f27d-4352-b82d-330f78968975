# Task Form Performance Improvements

## Current Issue

The task creation form is experiencing performance issues when loading parent objects (Purchase Orders, Suppliers, Documents) in environments with large datasets. This is happening because:

1. When a parent type is selected, the `fetchParentObjectsV2` function makes an API request to `/get-parent-objects/`
2. The server endpoint `get_parent_objects()` in `tasks/admin.py` queries for ALL objects of that type for the team
3. For teams with thousands of records, this loads all objects into the select dropdown, causing slow performance
4. The current implementation has no pagination or limit on how many objects are returned

## Proposed Solutions

### 1. Add Pagination and Limit to the Endpoint

```python
def get_parent_objects(request):
    team_id = request.GET.get("team_id")
    parent_type = request.GET.get("parent_type")
    search_string = request.GET.get("search_string")
    limit = int(request.GET.get("limit", 100))  # Default limit
    data = []

    if team_id and parent_type:
        team = Team.objects.filter(pk=team_id).get()
        
        # Get objects with limit
        if parent_type == "orders":
            if search_string:
                objects = PurchaseOrder.objects.filter(
                    team=team
                ).filter(
                    models.Q(po_number__icontains=search_string) |
                    models.Q(id__icontains=search_string)
                ).order_by("-placement_time")[:limit]
            else:
                objects = PurchaseOrder.objects.filter(
                    team=team
                ).order_by("-placement_time")[:limit]
                
        # [Similar handling for suppliers and documents]
        
        data = [{"id": obj.id, "name": str(obj)} for obj in objects]
        
        # Indicate if results were limited
        if len(data) == limit and not search_string:
            data.append({
                "id": "limit_note",
                "name": f"--- Showing {limit} most recent records. Use search to find more. ---"
            })

    return JsonResponse(data, safe=False)
```

### 2. Enhance the Search Functionality

- Make the search input more prominent
- Add placeholders indicating users should search by PO number/supplier name/etc.
- Improve search to look at multiple fields (ID, name, number, etc.)

### 3. Frontend Improvements

- Add loading indicators when fetching objects
- Disable the parent object field until search is performed
- Add debounce to the search to prevent too many API calls

### 4. Database Optimizations

- Ensure proper indexing on fields used in the queries
- For purchase orders: add index on (team_id, po_number)
- For suppliers: add index on (team_id, name)
- For documents: add index on (team_id, name)

## Implementation Plan

1. Modify the `get_parent_objects` function in `tasks/admin.py` to add limit and enhanced search
2. Update the dropdown handling in the frontend to handle the "limit note" entry
3. Add clearer search instructions in the UI
4. Add appropriate database indexes via migrations
5. Test with large datasets to verify performance improvements

## Potential Impact

- Faster loading of the task creation form
- Better user experience for teams with large datasets
- Reduced server load when creating tasks