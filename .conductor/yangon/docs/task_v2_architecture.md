# Task V2 Architecture

This document provides a comprehensive overview of the Task V2 system in the Didero API. It describes how the different components work together and how to use the create_task_v2 form for operations teams.

## Overview

The Task V2 system is a flexible, configurable task management system that allows for creating tasks with actions that can be executed by users. It's designed to be more powerful and flexible than the original Task system, with better parameter validation, more customization options, and improved UI capabilities.

## Key Components and Their Relationships

### 1. TaskTypeV2

`TaskTypeV2` is a Django model that defines the types of tasks that can be created in the system. 

**Key fields:**
- `name`: Enum from `TaskTypeName` identifying the task type
- `preview_description`: Short description for task previews
- `preview_title`: Title for task previews (can include template parameters)
- `title`: Main title of the task (can include template parameters)
- `category`: Task category (from `TaskCategory` enum)
- `context_panels`: JSON field for multiple context panels with format: `[{"type": "DOCUMENT_COMPARE", "params": {...}}, {"type": "PO_DETAILS", "params": {...}}]` (replaces deprecated `context_panel_type`)
- `param_definition`: JSON field defining required parameters for the task

TaskTypeV2 definitions are stored in `didero/tasks/migrations/utils/task_v2_definitions.py` and are synced to the database using the `sync_task_type_v2()` function.

### 2. TaskActionType

`TaskActionType` defines the types of actions that can be performed on tasks. These are buttons or operations that users can trigger.

**Key fields:**
- `name`: Enum from `TaskActionTypeName` identifying the action type
- `title_template`: Template for the action button title (can include parameters)
- `sub_text_template`: Template for the action button description
- `param_definition`: JSON field defining parameters for rendering the action UI
- `execution_param_definition`: JSON field defining parameters needed to execute the action
- `execution_type`: Where the action executes (SERVER, CLIENT, SERVER_ASYNC)

TaskActionType definitions are stored in `didero/tasks/migrations/utils/task_action_definitions.py` and are synced to the database.

### 3. Task

`Task` represents an instance of a task assigned to a user. It connects a task type with a specific model object.

**Key fields:**
- `user`: The user assigned to the task
- `user_group`: Optional group the task is assigned to (all members co-own the task)
- `task_type_v2`: Reference to the TaskTypeV2
- `task_config`: JSON containing the rendered task configuration
- `status`: Task status (PENDING, ON_HOLD, COMPLETED, FAILED)
- `model_type` and `model_id`: Content type and ID of the related object
- `next_reminder_at`: When to remind the user about the task
- `email_sent_at`: When notification email was sent

### 4. TaskAction

`TaskAction` represents an action button/option associated with a task.

**Key fields:**
- `task`: The Task it belongs to
- `action_type`: Reference to the TaskActionType
- `title`: Rendered title from the template
- `sub_text`: Rendered description from the template
- `status`: Action status (PENDING, COMPLETED, FAILED, INVALIDATED)
- `execution_type`: Where the action should execute
- `button_type`: Visual style (STANDARD, GREEN, RED)
- `execution_params`: Parameters needed to execute the action
- Execution fields: `executed_by`, `executed_at`, `execution_status`

### 5. Task Action Handlers

Action handlers are defined in `didero/tasks/actions.py` and mapped to action types in `ACTION_TYPE_HANDLER_MAP`. Each handler function:
- Takes a context and parameters
- Performs business logic
- Returns a result or errors

## Data Flow and Lifecycle

1. **Task Creation**:
   - The `create_task_v2()` utility function creates a task using a TaskTypeV2 definition
   - Parameters are validated against the task type's param_definition
   - The task_config is populated by replacing parameters in templates
   - TaskActions are created based on the definitions provided

2. **Task Execution**:
   - User interacts with a task in the UI and triggers an action
   - For server-side actions:
     - The action's execute() method retrieves the appropriate handler
     - The handler function runs with the action's execution_params
     - Based on the result, the action's status is updated
   - For client-side actions, the frontend handles execution

3. **Task Completion**:
   - When a task is completed or status changes, task.save() is called
   - This triggers any lifecycle events (like notifications)

## Task Context Panels

Tasks can have context panels that provide additional information to help users complete the task. Context panels are defined in the `context_panels` JSON field of TaskTypeV2, which can contain multiple panels of different types:

- `DOCUMENT_COMPARE`: For comparing documents
- `PO_DETAILS`: For showing purchase order details
- `COMMUNICATION_DETAILS`: For showing communication details

Each panel has its own parameters that determine what information is displayed.

## Creating Tasks with create_task_v2 Form

The `create_task_v2` form provides an admin interface for operations teams to create tasks. Here's how to use it:

1. **Accessing the Form**:
   - Go to the Django admin interface
   - Navigate to the Tasks section
   - Click on the "Create Task V2" button

2. **Basic Task Information**:
   - Select a Team (required)
   - Select a User (the task assignee)
   - Optionally select a User Group (makes all members co-owners)
   - Select a Task Type (determines parameters and UI)
   - Select Parent Type and Object (what the task is related to)

3. **Task Parameters**:
   - After selecting a task type, relevant parameter fields appear
   - Fill in all required parameters (marked with *)
   - Parameters will be used to replace placeholders in task templates

4. **Context Panel Configuration**:
   - Select a context panel type if needed
   - Enter JSON parameters for the panel according to its requirements
   - For PO_DETAILS panels with Purchase Order parents, the PO ID is auto-populated

5. **Task Actions**:
   - Add one or more actions using the "Add Another Action" button
   - For each action:
     - Select an Action Type
     - Choose a Button Type (standard, green, or red)
     - Fill in Display Parameters (used in button UI)
     - Fill in Execution Parameters (used when action is triggered)
   - For email actions, you can select from existing email templates

6. **Preview Section**:
   - See a live preview of how the task will appear
   - View the rendered task title, preview title, and description
   - See previews of action buttons with their rendered text

7. **Submitting**:
   - Click "Create Task" to create the task
   - The system validates all parameters before creation

## Use Cases and Examples

### PO Approval Request

A common use case is creating a purchase order approval task:

1. Select the "PO_APPROVAL_REQUEST" task type
2. Choose a PO as the parent object
3. Fill in parameters like requestor_name and po_number
4. Add "APPROVE_PURCHASE_ORDER" and "DENY_PURCHASE_ORDER" actions
5. Submit to create a task for the approver

### Manual Notification

For ad-hoc notifications to users:

1. Select the "MANUAL_NOTIFICATION_SIMPLE_DISMISS" task type
2. Choose the relevant parent object
3. Add a description for the notification
4. Submit to create a dismissible notification

## Best Practices

1. **Parameter Selection**: Choose task parameters carefully - they should provide enough context for the user to complete the task.

2. **Action Configuration**: Configure actions with clear titles and descriptions so users understand what will happen when they click.

3. **Context Panels**: Use appropriate context panels to provide additional information and reduce the need for users to navigate elsewhere.

4. **Validation**: Ensure all required parameters have values before submitting.

5. **Email Templates**: For email actions, prefer using existing templates for consistency.

## Error Handling

Common errors when creating tasks include:

1. Missing required parameters
2. Invalid parameter formats
3. Execution parameters not matching action type requirements

The form provides validation and error messages for all these cases.

## Technical Details

The Task V2 system uses several technical patterns:

1. **Template Rendering**: String templates with {parameter} placeholders that get replaced with actual values
2. **Parameter Validation**: Using Pydantic models for parameter validation
3. **JSON Schema**: TaskParamDefinitionConfig for defining and validating parameters
4. **Content Types**: GenericForeignKeys for linking tasks to any model
5. **Handler Registry**: A map of action types to handler functions