# Team Onboarding Guide: Quick Setup

## Introduction

This guide shows how to onboard a new team to the Didero platform using the streamlined admin interface. It covers setting up the three core workflows:
- Purchase Order (PO) Creation
- Order Acknowledgment
- Shipment Tracking

## Prerequisites

- Django admin access
- Knowledge of the team's basic information (name, email domain)

## Step-by-Step Onboarding Process

### 1. Create a New User and Team

The easiest way to create a new team is by adding a user with a new email domain:

1. Navigate to Django admin at `Users > Users`
2. Click "Add User"
3. Enter an email address with the client's domain (e.g., `<EMAIL>`)
4. Fill in the required fields (first name, last name, etc.)
5. Save the user

This will automatically:
- Create a new team named after the domain
- Add the domain to the team's domains
- Make the user a member of the team
- Create default team settings

After creating the user, you should:
1. Navigate to `Users > Teams` to find the new team
2. Open the team details and set the newly created user as the "Default Requestor"

### 2. Set Up Workflows

The simplest way to set up workflows is through the Django admin interface:

1. Navigate to `Users > Teams`
2. You have two options:
   
   **For a single team:**
   - Open the team's detail page by clicking on the team name
   - Click the green "Setup Workflows" button in the top right corner
   - Select your desired workflow settings:
     - **Enable human validation**: Requires human review of POs, acknowledgments, and shipments
     - **Enable automatic follow-ups**: Automatically sends follow-up emails for orders
     - **Enable PO creation from emails**: Allows POs to be created from inbound emails
     - **Default Requestor**: User who will receive task notifications (success notifications go to this user)
     - **Enable task email notifications**: Turn on email notifications for both the default requestor and the team
   - Click "Set up workflows"
   
   **For multiple teams:**
   - From the teams list, select one or more teams using the checkboxes
   - From the "Actions" dropdown, select "Set up workflows for selected teams"
   - Configure your workflow settings:
     - For multiple teams, you can't set a default requestor (since it's team-specific)
     - But you can enable task notifications for existing default requestors and their teams
   - Click "Set up workflows"

This automatically sets up all necessary settings and workflows for the team(s).

### 3. Configure Email Processing

For the workflows to function properly, email processing must be configured:

1. Log in as a team user:
   - Navigate to `Users > Users` in Django admin
   - Find the user you created for the team
   - Click the "Login as user" button in the top right (this lets you impersonate the user without needing email access)
   - You'll be redirected to the application as that user

2. Add an email credential through the application:
   - Go to Settings > Email Accounts
   - Click "Add Email Account"
   - Enter an email address to use for PO communications
   - **Important**: This email account should be actively monitored as it will receive emails that trigger workflows (PO responses, order acknowledgments, shipping notifications)
   - Complete the authorization process

3. Set this as the default email for the team:
   - Navigate back to Django admin
   - Go to `Users > Teams`
   - Edit the team
   - Set the newly added credential as the team's "Default PO Email Credential"
   - Set the "Default Requestor" field to the appropriate user
     - This user will receive task notifications when success notifications are enabled
     - Tasks will be assigned to this user upon workflow completion
   - Save the team

4. Configure email notifications for the default requestor (optional):
   - Navigate to `Users > Team Settings`
   - Look for the setting `USER_EMAIL_TASK_NOTIFICATIONS_ENABLED` for the default requestor user
   - If you want the user to receive email notifications for tasks, set this to `True`
   - By default, this setting is `False`, so users won't receive task notification emails unless enabled

### 4. Verify Configuration

After completing the setup:

1. Navigate to `Workflows > User Workflows` in the Django admin
2. Verify that all three workflows are created for the team:
   - PO Creation workflow
   - Order Acknowledgment workflow
   - Shipment workflow
3. Check `Users > Team Settings` to ensure all required settings are present


### 5. Add Required Suppliers

Didero will only process emails from domains belonging to suppliers registered with a team (this is a security measure to ensure we only ingest emails from authorized sources). After setting up workflows, you must:

1. Log in to the Didero application as a team user
2. Navigate to the Suppliers section
3. Add the following suppliers:
   - **Didero**: Add `didero.ai` as a supplier to ensure system-generated emails are processed
   - **Any email domains that will trigger workflows**: Add all email domains that will be sending POs, order acknowledgments, or shipment notifications
   - **Test suppliers**: Add any domains you'll use for testing the workflows

This is a critical step – if a supplier's domain is not registered with the team, their emails will not be processed by the workflows, even if they're sent to the configured email address.

### 6. Send some emails!

Send an email containing a Purchase Order (give it a realistic subject and body + attachments) to the email
you connected in Step 2. It should take a few minutes but you should receive a notification and see the PO in Didero.

## Troubleshooting

Common issues and solutions:

1. **Team settings not applied**: Run the "Setup Workflows" action again
2. **Workflows not triggering**: Verify that the workflows exist in the admin and are correctly linked to the team
3. **Email processing not working**: Check the Nylas credentials and ensure they are properly configured
4. **Human validation not functioning**: Verify the human validation settings are enabled and the Didero AI user exists
5. **Emails not being processed**: Check that the sending domain is registered as a supplier for the team - Didero will not process emails from domains that aren't registered as suppliers

## Conclusion

By following this guide, you can quickly onboard new teams to the Didero platform with all the necessary workflows and configurations. The process only takes a few minutes through the admin interface.