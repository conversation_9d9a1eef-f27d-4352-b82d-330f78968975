# PO Creation Core Workflow Design

## Overview

This document outlines the design for converting the DAG-based PO Creation workflow to a core Temporal workflow implementation with self-contained activity logic.

## Workflow Structure

```python
from temporalio import workflow
from temporalio.common import RetryPolicy
from datetime import timed<PERSON><PERSON>
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import structlog

# Data classes for type safety
@dataclass
class POExtractionResult:
    """Result from PO extraction"""
    success: bool
    po_exists: bool = False
    po_data: Optional[Dict] = None
    error_message: Optional[str] = None
    extraction_method: str = "ai"  # "ai" or "netsuite"

@dataclass
class POCreationResult:
    """Result from PO creation"""
    success: bool
    po_id: Optional[str] = None
    po_number: Optional[str] = None
    error_message: Optional[str] = None
    created_in_draft: bool = False

class POStatus(str, Enum):
    DRAFT = "DRAFT"
    ISSUED = "ISSUED"

@workflow.defn
class POCreationWorkflow:
    """Core PO Creation workflow implementation"""
    
    @workflow.run
    async def run(self, workflow_id: str, params: Dict) -> Dict:
        """
        Main workflow execution
        
        Args:
            workflow_id: The UserWorkflow ID
            params: Must contain 'email_id' and 'team_id'
        
        Returns:
            Dict with success status and created PO details
        """
        self.logger = workflow.logger
        self.workflow_id = workflow_id
        
        email_id = params.get("email_id")
        team_id = params.get("team_id")
        
        if not email_id or not team_id:
            return {
                "success": False,
                "error": "Missing required parameters: email_id and team_id"
            }
        
        try:
            # Step 1: Extract PO details from email
            extraction_result = await self.extract_po_details(email_id, team_id)
            
            if not extraction_result.success:
                return {
                    "success": False,
                    "error": f"Failed to extract PO details: {extraction_result.error_message}"
                }
            
            if extraction_result.po_exists:
                return {
                    "success": False,
                    "error": "PO already exists",
                    "duplicate": True
                }
            
            # Step 2: Check team settings for human validation
            human_validation_enabled = await self.get_human_validation_setting(team_id)
            
            # Step 3: Create the Purchase Order
            creation_result = await self.create_purchase_order(
                team_id=team_id,
                po_data=extraction_result.po_data,
                extraction_method=extraction_result.extraction_method,
                human_validation_enabled=human_validation_enabled
            )
            
            if not creation_result.success:
                return {
                    "success": False,
                    "error": f"Failed to create PO: {creation_result.error_message}"
                }
            
            # Step 4: Link email and create tasks (non-critical)
            task_id = await self.link_email_and_create_tasks(
                email_id=email_id,
                team_id=team_id,
                po_id=creation_result.po_id,
                po_number=creation_result.po_number,
                created_in_draft=creation_result.created_in_draft
            )
            
            # Return success with PO details
            return {
                "success": True,
                "po_id": creation_result.po_id,
                "po_number": creation_result.po_number,
                "created_in_draft": creation_result.created_in_draft,
                "task_id": task_id
            }
            
        except Exception as e:
            self.logger.error(f"Workflow failed with unexpected error: {str(e)}")
            return {
                "success": False,
                "error": f"Workflow failed: {str(e)}"
            }
    
    async def extract_po_details(self, email_id: str, team_id: str) -> POExtractionResult:
        """
        Extract PO details from email using AI
        
        This method:
        1. Fetches the email by ID
        2. Uses AI to extract PO information
        3. Checks if PO already exists
        4. Determines if data is sufficient for direct creation
        """
        try:
            # Execute the actual extraction activity
            result = await workflow.execute_activity(
                "extract_po_from_email",
                args={
                    "email_id": email_id,
                    "team_id": team_id
                },
                start_to_close_timeout=timedelta(minutes=5),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=10),
                    maximum_interval=timedelta(seconds=60),
                    backoff_coefficient=2.0
                )
            )
            
            # Check if PO already exists
            if result.get("po_number") and result.get("supplier_id"):
                existing_po = await workflow.execute_activity(
                    "check_po_exists",
                    args={
                        "team_id": team_id,
                        "po_number": result["po_number"],
                        "supplier_id": result["supplier_id"]
                    },
                    start_to_close_timeout=timedelta(seconds=30)
                )
                
                if existing_po:
                    self.logger.info(
                        "PO already exists",
                        po_number=result["po_number"],
                        existing_po_id=existing_po["id"]
                    )
                    return POExtractionResult(
                        success=True,
                        po_exists=True,
                        po_data=result
                    )
            
            # Determine extraction method based on data completeness
            extraction_method = "ai" if result.get("is_complete", False) else "netsuite"
            
            return POExtractionResult(
                success=True,
                po_exists=False,
                po_data=result,
                extraction_method=extraction_method
            )
            
        except Exception as e:
            self.logger.error("Failed to extract PO details", error=str(e))
            return POExtractionResult(
                success=False,
                error_message=str(e)
            )
    
    async def get_human_validation_setting(self, team_id: str) -> bool:
        """Get team setting for human validation requirement"""
        try:
            result = await workflow.execute_activity(
                "get_team_setting",
                args={
                    "team_id": team_id,
                    "setting_name": "TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED"
                },
                start_to_close_timeout=timedelta(seconds=30)
            )
            return result.get("value", False)
        except Exception as e:
            self.logger.warning(f"Failed to get team setting, defaulting to False: {e}")
            return False
    
    async def create_purchase_order(
        self, 
        team_id: str, 
        po_data: Dict,
        extraction_method: str,
        human_validation_enabled: bool
    ) -> POCreationResult:
        """
        Create a purchase order
        
        This method:
        1. Determines PO status based on validation settings
        2. Creates or matches supplier
        3. Creates the PO with appropriate status
        4. Creates order items
        5. Handles both direct creation and NetSuite integration
        """
        try:
            # Determine PO status based on validation settings
            if human_validation_enabled:
                po_status = POStatus.DRAFT
                is_po_editable = False
            else:
                po_status = POStatus.ISSUED
                is_po_editable = True
            
            # Create the PO using appropriate method
            if extraction_method == "ai":
                # Direct creation with extracted data
                po_result = await workflow.execute_activity(
                    "create_po_direct",
                    args={
                        "team_id": team_id,
                        "po_data": po_data,
                        "status": po_status.value,
                        "is_editable": is_po_editable,
                        "source": "EMAIL_AI"
                    },
                    start_to_close_timeout=timedelta(minutes=3),
                    retry_policy=RetryPolicy(
                        maximum_attempts=3,
                        initial_interval=timedelta(seconds=10),
                        maximum_interval=timedelta(seconds=60),
                        backoff_coefficient=2.0
                    )
                )
            else:
                # NetSuite integration for incomplete data
                po_result = await workflow.execute_activity(
                    "create_po_via_netsuite",
                    args={
                        "team_id": team_id,
                        "po_data": po_data,
                        "status": po_status.value,
                        "is_editable": is_po_editable
                    },
                    start_to_close_timeout=timedelta(minutes=5),
                    retry_policy=RetryPolicy(
                        maximum_attempts=2,
                        initial_interval=timedelta(seconds=30),
                        maximum_interval=timedelta(seconds=120),
                        backoff_coefficient=2.0
                    )
                )
            
            return POCreationResult(
                success=True,
                po_id=str(po_result["id"]),
                po_number=po_result["po_number"],
                created_in_draft=(po_status == POStatus.DRAFT)
            )
            
        except Exception as e:
            self.logger.error("Failed to create PO", error=str(e))
            return POCreationResult(
                success=False,
                error_message=str(e)
            )
    
    async def link_email_and_create_tasks(
        self,
        email_id: str,
        team_id: str,
        po_id: str,
        po_number: str,
        created_in_draft: bool
    ) -> Optional[str]:
        """
        Link email to PO and create appropriate tasks
        
        This method:
        1. Links email thread to the PO
        2. Creates activity log entries
        3. Creates either confirmation task (draft) or success notification (issued)
        
        Returns task_id if successful, None if failed (non-critical)
        """
        try:
            # Link email thread to PO
            await workflow.execute_activity(
                "link_email_thread_to_po",
                args={
                    "email_id": email_id,
                    "po_id": po_id
                },
                start_to_close_timeout=timedelta(minutes=1),
                retry_policy=RetryPolicy(
                    maximum_attempts=2,
                    initial_interval=timedelta(seconds=5)
                )
            )
            
            # Create activity log
            await workflow.execute_activity(
                "create_activity_log",
                args={
                    "po_id": po_id,
                    "action": "PO_CREATED_FROM_EMAIL",
                    "details": {"email_id": email_id}
                },
                start_to_close_timeout=timedelta(seconds=30)
            )
            
            # Determine task assignee (special handling for team 47)
            task_assignee = await self.get_task_assignee(team_id)
            
            # Create appropriate task based on validation status
            if created_in_draft:
                # Create confirmation task for AI user
                task_result = await workflow.execute_activity(
                    "create_po_confirmation_task",
                    args={
                        "team_id": team_id,
                        "po_id": po_id,
                        "po_number": po_number,
                        "assignee": task_assignee,
                        "workflow_id": self.workflow_id
                    },
                    start_to_close_timeout=timedelta(minutes=1)
                )
            else:
                # Create success notification
                task_result = await workflow.execute_activity(
                    "create_po_success_notification",
                    args={
                        "team_id": team_id,
                        "po_id": po_id,
                        "po_number": po_number,
                        "assignee": task_assignee
                    },
                    start_to_close_timeout=timedelta(minutes=1)
                )
            
            return task_result.get("task_id") if task_result else None
            
        except Exception as e:
            # This is non-critical, so we log but don't fail the workflow
            self.logger.warning(f"Failed to link email or create tasks: {str(e)}")
            return None
    
    async def get_task_assignee(self, team_id: str) -> str:
        """Get the appropriate task assignee for the team"""
        # Special handling for team 47 (TotalHomeSupply)
        if team_id == "47":
            return "<EMAIL>"
        
        # Default to AI user
        return "ai_user"
```

## Key Design Benefits

### 1. **Self-Contained Workflow**
- All business logic is visible in the workflow class
- Each method represents a logical step in the process
- Easy to understand the flow by reading the main `run` method

### 2. **Clear Separation of Concerns**
- Workflow orchestration logic in the class methods
- Actual work delegated to activities
- Clean interfaces between workflow and activities

### 3. **Flexibility**
- Easy to add new steps or modify existing ones
- Team-specific logic can be added as methods
- Validation and error handling at each step

### 4. **Testability**
- Each method can be unit tested independently
- Mock activities for testing workflow logic
- Clear inputs and outputs for each step

### 5. **Type Safety**
- Strong typing throughout
- Return types clearly defined
- Dataclasses for complex data structures

## Activity Definitions

The workflow references these activities that need to be implemented:

```python
# Activities called by the workflow
REQUIRED_ACTIVITIES = [
    "extract_po_from_email",      # AI extraction of PO data
    "check_po_exists",             # Check for duplicate POs
    "get_team_setting",            # Retrieve team settings
    "create_po_direct",            # Direct PO creation
    "create_po_via_netsuite",      # NetSuite integration
    "link_email_thread_to_po",     # Link email to PO
    "create_activity_log",         # Log actions
    "create_po_confirmation_task", # Create draft confirmation task
    "create_po_success_notification" # Create success notification
]
```

## Migration Notes

1. **Activity Implementation**
   - Extract logic from existing DAG nodes
   - Wrap in Temporal activity decorators
   - Ensure idempotency

2. **Testing Strategy**
   - Unit test each workflow method
   - Integration test with real activities
   - Compare outputs with DAG version

3. **Deployment**
   - Use workflow router to gradually migrate
   - Monitor metrics and logs
   - Roll back if issues detected