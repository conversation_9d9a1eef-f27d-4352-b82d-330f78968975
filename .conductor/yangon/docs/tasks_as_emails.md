# Task Email Notification System Design

## Overview
Enable tasks to be delivered and actioned via email, allowing users to interact with <PERSON><PERSON> without requiring the web UI.

## System Components

### 1. Configuration
- **Model Changes:** Add `task_email_notifications_enabled` to `TeamSetting`
- **New Fields:** Add user-level notification preferences to `User` model

### 2. Email Templates
- **New Model:** `TaskEmailTemplate`
  - Fields: `task_type` (FK to TaskTypeV2), `subject_template`, `body_template_html`, `body_template_text`
  - Supports dynamic content via existing placeholder system

### 3. Email Action Execution System
- **New Module:** `tasks/email_actions.py` 
  - Implements secure token generation/validation for email actions
    - This is needed to ensure TaskActions are only executed once and that they are executed by the right person.
  - Provides URL generation for actionable email links
- **New Setting:** `TASK_ACTION_EMAIL_SALT` for token security
- **New Endpoint:** `/api/v1/tasks/action-execute/<token>/` for email-based actions

### 4. Asynchronous Notification Delivery
- **Model Changes:** Add `email_sent_at` field to `Task` model
- **Signal Handler:** Implement lightweight post-save task queuing
- **Celery Task:** Create `send_task_email_notification` task
- **Integration:** Leverage existing `postmark_service.py` for email delivery

## Implementation Strategy
1. **Foundation (Phase 1):** 
   - Team settings configuration
   - Basic TaskEmailTemplate model
   - Asynchronous email generation and delivery
   
2. **Action System (Phase 2):**
   - Secure token implementation
   - Action execution endpoint
   - Actionable email templates

3. **Personalization (Future):**
   - User preferences
   - Enhanced templates
   - Digest options

## Technical Implementation

### Post-Save Trigger
```python
@receiver(post_save, sender=Task)
def queue_task_email_notification(sender, instance, created, **kwargs):
    # Quick eligibility check before queuing
    team = instance.user.teams.filter(id=instance.team_id).first()
    if not team or not TeamSetting.get_boolean(team, "task_email_notifications_enabled"):
        return
        
    # Queue the async task
    send_task_email_notification.delay(
        task_id=str(instance.id),
        created=created
    )
```

### Celery Task
```python
@shared_task(bind=True, max_retries=3)
def send_task_email_notification(self, task_id, created):
    # Full eligibility check, template processing, and email delivery
    # Updates task.email_sent_at when complete
```

### Action Token System - Technical Notes

#### Token Generation and Validation
```python
# In didero/tasks/email_actions.py

from django.core import signing
from django.conf import settings
from django.utils import timezone
import uuid


def generate_action_token(task_action, user):
    """Generate a secure, time-limited token for email action execution."""
    # Include one-time nonce to prevent replay attacks
    nonce = str(uuid.uuid4())
    
    payload = {
        'task_id': str(task_action.task.id),
        'action_id': str(task_action.id),
        'user_id': str(user.id),
        'created': timezone.now().timestamp(),
        'nonce': nonce,
    }
    
    # Store nonce in database to verify token is used only once
    ActionTokenNonce.objects.create(
        nonce=nonce,
        task_action=task_action,
        user=user,
        expires_at=timezone.now() + timezone.timedelta(days=7)
    )
    
    # Sign with configurable salt
    return signing.dumps(payload, salt=settings.TASK_ACTION_EMAIL_SALT, compress=True)


def verify_action_token(token):
    """Verify and decode an action token."""
    try:
        # Set max_age to 7 days (in seconds)
        payload = signing.loads(
            token, 
            salt=settings.TASK_ACTION_EMAIL_SALT, 
            max_age=7 * 24 * 60 * 60
        )
        
        # Verify nonce hasn't been used
        nonce = payload.get('nonce')
        if not nonce:
            return None
            
        nonce_obj = ActionTokenNonce.objects.filter(
            nonce=nonce, 
            used_at__isnull=True,
            expires_at__gt=timezone.now()
        ).first()
        
        if not nonce_obj:
            return None
            
        # Mark nonce as used
        nonce_obj.used_at = timezone.now()
        nonce_obj.save()
        
        return payload
        
    except (signing.BadSignature, signing.SignatureExpired):
        return None
```

#### Nonce Model for Single-Use Tokens
```python
# In didero/tasks/models.py

class ActionTokenNonce(models.Model):
    """Ensures email action tokens can only be used once."""
    nonce = models.UUIDField(primary_key=True)
    task_action = models.ForeignKey(TaskAction, on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        indexes = [
            models.Index(fields=['nonce']),
            models.Index(fields=['task_action', 'used_at']),
            models.Index(fields=['expires_at']),
        ]
```

#### Action Execution Endpoint
```python
# In didero/tasks/views.py

@api_view(['GET'])
def execute_email_action(request, token):
    """Handle action execution from email links."""
    payload = verify_action_token(token)
    if not payload:
        # Render a user-friendly error page
        return render(request, 'task_action_error.html', {
            'error': 'This action link has expired or already been used.'
        })
    
    try:
        action = TaskAction.objects.get(id=payload['action_id'])
        task = Task.objects.get(id=payload['task_id'])
        user = User.objects.get(id=payload['user_id'])
        
        # Execute the action
        result = action.execute(user_id=str(user.id), origin="email")
        
        # Render success page
        return render(request, 'task_action_success.html', {
            'task': task,
            'action': action,
            'result': result
        })
        
    except (TaskAction.DoesNotExist, Task.DoesNotExist, User.DoesNotExist):
        return render(request, 'task_action_error.html', {
            'error': 'The requested action could not be found.'
        })
```

#### Environment Configuration
```python
# In didero/settings/common.py

# Email action security
TASK_ACTION_EMAIL_SALT = os.environ.get(
    'TASK_ACTION_EMAIL_SALT', 
    'develop-only-salt-change-in-production'
)
```

#### Action URL Generation for Templates
```python
def get_action_url(task_action, user, base_url=None):
    """Generate a complete action URL for emails."""
    token = generate_action_token(task_action, user)
    if base_url is None:
        base_url = settings.API_BASE_URL
    
    return f"{base_url}/api/v1/tasks/action-execute/{token}/"


# Usage in template context
def _generate_email_content(task, template):
    """Generate email content with action URLs."""
    context = {
        'task': task,
        'user': task.user,
        # Generate URLs for each action
        'actions': [
            {
                'id': action.id,
                'title': action.title,
                'sub_text': action.sub_text,
                'button_type': action.button_type,
                'url': get_action_url(action, task.user)
            }
            for action in task.actions.all()
        ]
    }
    
    # Render templates with context
    subject = template.render_subject(context)
    html_body = template.render_html_body(context)
    text_body = template.render_text_body(context)
    
    return {
        'subject': subject,
        'html_body': html_body,
        'text_body': text_body
    }
```

### Postmark Integration

#### Leveraging Existing Email Service
```python
# In didero/emails/postmark_service.py

# Add new template ID constant
TASK_EMAIL_TEMPLATE_ID = 12345678  # Replace with actual Postmark template ID

def send_task_notification_email(self, task, user, actions=None):
    """Send an email notification for a specific task with actionable buttons.
    
    Args:
        task: Task object to send notification for
        user: User to send notification to
        actions: Optional list of action data with URLs
    """
    # Skip if email service is disabled
    if self.service_disabled():
        return None
        
    # Check if user has opted out
    if not self.user_email_setting_enabled(user, "task_emails"):
        return None
        
    # Get task details
    task_type = task.task_type_v2
    
    # Build template data
    template_data = {
        "task_id": str(task.id),
        "task_title": task.task_config.get("title", "Task Notification"),
        "task_description": task.task_config.get("preview_description", ""),
        "task_category": task.task_config.get("category", ""),
        "action_url": f"{settings.APP_BASE_URL}/tasks/{task.id}",
        "created_at": task.created_at.strftime("%B %d, %Y at %I:%M %p"),
        "user_name": user.get_full_name() or user.email,
        "actions": actions or []
    }
    
    # Add model-specific details if available
    if hasattr(task, 'model_details') and task.model_details:
        template_data.update({
            "model_type": task.model_type.name if task.model_type else "Unknown",
            "model_details": task.model_details
        })
    
    # Send the email
    return self.send_email_with_template(
        template_id=TASK_EMAIL_TEMPLATE_ID,
        template_model=template_data,
        from_email=settings.NOTIFICATION_FROM_EMAIL,
        to_email=user.email
    )
```

#### Integration with Celery Task
```python
@shared_task(bind=True, max_retries=3)
def send_task_email_notification(self, task_id, created):
    try:
        task = Task.objects.get(id=task_id)
        user = task.user
        
        # Skip if not eligible
        if not _should_send_notification(task, created):
            return
            
        # Generate action URLs for email
        actions = []
        for action in task.actions.all():
            actions.append({
                'id': str(action.id),
                'title': action.title,
                'sub_text': action.sub_text,
                'button_type': action.button_type,
                'url': get_action_url(action, user)
            })
        
        # Send email via postmark service
        from didero.emails.postmark_service import PostmarkEmailService
        service = PostmarkEmailService()
        result = service.send_task_notification_email(
            task=task,
            user=user,
            actions=actions
        )
        
        # Update task status if email was sent
        if result:
            task.email_sent_at = timezone.now()
            task.save(update_fields=['email_sent_at'])
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to send task email: {e}")
        self.retry(exc=e, countdown=2 ** self.request.retries * 60)
```

#### Postmark Template Example
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{task_title}}</title>
    <style>
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px 5px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .button.red { background-color: #f44336; }
        .button.blue { background-color: #2196F3; }
    </style>
</head>
<body>
    <h1>{{task_title}}</h1>
    <p>{{task_description}}</p>
    
    <div>
        {{#each actions}}
        <a href="{{this.url}}" class="button {{#if this.button_type}}{{this.button_type}}{{/if}}">
            {{this.title}}
        </a>
        {{/each}}
    </div>
    
    <p>
        <a href="{{action_url}}">View this task in Didero</a>
    </p>
</body>
</html>
```

## Out of Scope (Initial Release)
- Email open/click tracking
- Advanced analytics on email engagement
- Email reply processing
- Notification dashboards

## Rollout Plan
- Implement feature toggles for controlled release
- Start with non-critical task types
- Monitor email delivery success and action completion rates
- Gather user feedback for future enhancements