## Didero Terraform 

### Prerequisites

Install Terraform 
`brew install terraform`

Make sure you're on `1.5.7`

### Setup

Dev Folder is for our Development AWS account
Prod Folder is for our Production AWS account.

- Go into the account you want to make/modify resources in
- `terraform init`
- Go into the AWS service directory you want to make/modify resources in
  - For example if you want to make an S3 bucket, go to `s3/main.tf`
- Read TF docs and make the resources in the terraform file
- `terraform plan` -> Make sure the changes are what you expect
- `terraform apply` -> This makes/modifies/deletes the resources on the AWS end. Be very careful, especially when doing things in prod. 
  - NOTE - *Deleting a resource definition WILL DELETE THE RESOURCE IN AWS*