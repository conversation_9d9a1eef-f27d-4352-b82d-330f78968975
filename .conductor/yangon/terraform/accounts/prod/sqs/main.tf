
resource "aws_sqs_queue" "didero_prod_ai_workflows" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_ai_workflows_dlq" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_bulk_supplier_imports" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_bulk_supplier_imports_dlq" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_default" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_default_dlq" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_dlq" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_email_backfills" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_email_backfills_dlq" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_periodic_tasks" {
  # (resource arguments)
}

resource "aws_sqs_queue" "didero_prod_periodic_tasks_dlq" {
  # (resource arguments)
}

