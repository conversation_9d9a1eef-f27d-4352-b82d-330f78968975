#!/bin/bash

# Fetch the list of SQS queue URLs using AWS CLI
queue_urls=$(aws sqs list-queues --query "QueueUrls[]" --output text --profile didero-deploy)

# Loop through each queue URL and import it into Terraform
for queue_url in $queue_urls; do
  # Extract the queue name from the URL
  queue_name=$(basename "$queue_url")
  
  # Generate the Terraform resource name by replacing hyphens with underscores
  resource_name="module.sqs.aws_sqs_queue.${queue_name//-/_}"
  
  echo "Importing $queue_name into $resource_name"
  terraform import "$resource_name" "$queue_url"
done
