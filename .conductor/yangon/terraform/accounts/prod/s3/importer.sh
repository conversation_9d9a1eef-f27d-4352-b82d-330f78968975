#!/bin/bash

# Fetch the list of S3 bucket names using AWS CLI
bucket_names=$(aws s3api list-buckets --query "Buckets[].Name" --output text --profile didero-deploy)

# Loop through each bucket and import it into Terraform
for bucket_name in $bucket_names; do
  # Generate the Terraform resource name by replacing hyphens with underscores
  resource_name="module.s3.aws_s3_bucket.${bucket_name}"
  encryption_resource_name="module.s3.aws_s3_bucket_server_side_encryption_configuration.${bucket_name}"
  
  echo "Importing $bucket_name into $resource_name"
  terraform import "$resource_name" "$bucket_name"
  
  echo "Importing encryption configuration for $bucket_name into $encryption_resource_name"
  terraform import "$encryption_resource_name" "$bucket_name"
done


