
resource "aws_s3_bucket" "cf-templates-1ktvexvfda873-us-east-2" {
  bucket = "cf-templates-1ktvexvfda873-us-east-2"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "cf-templates-1ktvexvfda873-us-east-2" {
  bucket = aws_s3_bucket.cf-templates-1ktvexvfda873-us-east-2.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = false
  }
}

resource "aws_s3_bucket" "codepipeline-us-east-2-133545602250" {
  bucket = "codepipeline-us-east-2-133545602250"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "codepipeline-us-east-2-133545602250" {
  bucket = aws_s3_bucket.codepipeline-us-east-2-133545602250.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = false
  }
}

resource "aws_s3_bucket" "didero-ai-cost-estimations-prod" {
  bucket = "didero-ai-cost-estimations-prod"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-ai-cost-estimations-prod" {
  bucket = aws_s3_bucket.didero-ai-cost-estimations-prod.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-ai-cost-estimations-staging" {
  bucket = "didero-ai-cost-estimations-staging"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-ai-cost-estimations-staging" {
  bucket = aws_s3_bucket.didero-ai-cost-estimations-staging.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-balancer-prod-access-logs" {
  bucket = "didero-balancer-prod-access-logs"
  tags = {
    "prodBalancerAccessLogs" : ""
  }
  tags_all = {
    "prodBalancerAccessLogs" : ""
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-balancer-prod-access-logs" {
  bucket = aws_s3_bucket.didero-balancer-prod-access-logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-balancer-staging-access-logs" {
  bucket = "didero-balancer-staging-access-logs"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-balancer-staging-access-logs" {
  bucket = aws_s3_bucket.didero-balancer-staging-access-logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-docs-dev" {
  bucket = "didero-docs-dev"

  tags = {
    "customerData" = "true"
  }
  tags_all = {
    "customerData" = "true"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-docs-dev" {
  bucket = aws_s3_bucket.didero-docs-dev.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-docs-prod" {
  bucket = "didero-docs-prod"
  tags = {
    "customerData" = "true"
  }
  tags_all = {
    "customerData" = "true"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-docs-prod" {
  bucket = aws_s3_bucket.didero-docs-prod.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-docs-staging-east-2" {
  bucket = "didero-docs-staging-east-2"
  tags = {
    "customerData" = "true"
  }
  tags_all = {
    "customerData" = "true"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-docs-staging-east-2" {
  bucket = aws_s3_bucket.didero-docs-staging-east-2.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-env-files" {
  bucket = "didero-env-files"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-env-files" {
  bucket = aws_s3_bucket.didero-env-files.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "elasticbeanstalk-us-east-2-855701137767" {
  bucket = "elasticbeanstalk-us-east-2-855701137767"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "elasticbeanstalk-us-east-2-855701137767" {
  bucket = aws_s3_bucket.elasticbeanstalk-us-east-2-855701137767.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = false
  }
}

resource "aws_s3_bucket" "didero-terraform-state-prod" {
  bucket = "didero-terraform-state-prod"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-terraform-state-prod" {
  bucket = aws_s3_bucket.didero-terraform-state-prod.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

