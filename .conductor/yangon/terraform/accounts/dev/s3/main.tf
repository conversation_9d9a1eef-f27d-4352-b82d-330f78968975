
resource "aws_s3_bucket" "didero-ai-cost-estimations-dev" {
  bucket = "didero-ai-cost-estimations-dev"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-ai-cost-estimations-dev" {
  bucket = aws_s3_bucket.didero-ai-cost-estimations-dev.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-docs-development" {
  bucket = "didero-docs-development"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-docs-development" {
  bucket = aws_s3_bucket.didero-docs-development.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket" "didero-terraform-state-dev" {
  bucket = "didero-terraform-state-dev"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "didero-terraform-state-dev" {
  bucket = aws_s3_bucket.didero-terraform-state-dev.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}