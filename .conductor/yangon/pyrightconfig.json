{"include": ["didero/ai", "didero/shipping_documents"], "exclude": ["**/tests/**", "./venv/**", "./typings"], "typeCheckingMode": "standard", "venvPath": ".venv", "reportIncompleteStub": "error", "reportMissingTypeArgument": "error", "reportMissingParameterType": "error", "reportReturnType": "error", "reportRedeclaration": "error", "reportUnusedImport": "warning", "reportUnusedVariable": "warning", "reportPrivateUsage": "warning", "reportUnknownMemberType": "none"}