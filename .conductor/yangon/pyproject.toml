[project]
name = "didero"
version = "0.1.0"
description = ""
readme = "README.md"
requires-python = ">=3.11,<3.12"
dependencies = [
    "beautifulsoup4>=4.12.3",
    "boto3>=1.35.74",
    "celery-singleton==0.3.1",
    "celery[redis,sqs]==5.3.6",
    "coverage==7.5.3",
    "cryptography==42.0.7",
    "currency-symbols==2.0.3",
    "django-activity-stream==2.0.0",
    "django-admin-extra-buttons==1.5.6",
    "django-admin-inline-paginator==0.4.0",
    "django-auditlog==3.0.0",
    "django-celery-beat==2.6.0",
    "django-celery-results==2.5.1",
    "django-cleanup==8.1.0",
    "django-cors-headers==4.2.0",
    "django-countries==7.5.1",
    "django-cryptography==1.1",
    "django-extensions==3.2.3",
    "django-filter==24.2",
    "django-money[exchange]==3.5.2",
    "django-opensearch-dsl==0.6.2",
    "django-storages==1.14.2",
    "django-structlog[celery]==7.1.0",
    "django-taggit==5.0.1",
    "Django==4.2.7",
    "djangorestframework-camel-case==1.4.2",
    "djangorestframework==3.14.0",
    "drf-spectacular==0.27.2",
    "drf-standardized-errors==0.12.6",
    "google-api-python-client==2.119.0",
    "google-auth-httplib2==0.2.0",
    "google-auth-oauthlib==1.1.0",
    "google-auth>=2.36.0",
    "google-cloud-pubsub==2.23.0",
    "gunicorn==20.1.0",
    "imap-tools==1.5.0",
    "jinja2==3.1.3",
    "json-log-formatter==1.0",
    "lxml==4.9.3",
    "llama-index==0.10.11",
    "msal==1.26.0",
    "openai>=1.75.0",
    "opensearch-dsl==2.1.0",
    "pandas==2.2.0",
    "pdf2image==1.17.0",
    "pdfkit==1.0.0",
    "pillow==10.0.0",
    "postmarker==1.0",
    "pre-commit==3.7.0",
    "psycopg2-binary==2.9.9",
    "pubnub==8.0.0",
    "pycurl==7.45.3",
    "pydantic>=2.10.0",
    "pytesseract==0.3.10",
    "python-dotenv==1.0.0",
    "regex==2023.12.25",
    "requests>=2.32.2",
    "ruff==0.6.3",
    "structlog==23.1.0",
    "tiktoken==0.8.0",
    "transitions==0.9.0",
    "wheel==0.42.0",
    "whitenoise==6.5.0",
    "xhtml2pdf==0.2.15",
    "sympy>=1.13.2",
    "django-currentuser>=0.8.0",
    "tblib>=3.0.0",
    "django-linear-migrations>=2.16.0",
    "pdfplumber>=0.11.4",
    "tabulate>=0.9.0",
    "temporalio>=1.8.0",
    "rapidfuzz>=3.10.1",
    "langfuse>=2.54.1",
    "googlemaps>=4.10.0",
    "anthropic>=0.40.0",
    "django-silk>=5.3.1",
    "logfire>=3.17.0",
    "pinecone-client==5.0.0",
    "llama-index-vector-stores-pinecone>=0.1.9",
    "opentelemetry-api>=1.33.0",
    "opentelemetry-sdk>=1.33.0",
    "opentelemetry-exporter-otlp>=1.33.0",
    "opentelemetry-instrumentation-django>=0.44b0",
    "opentelemetry-instrumentation-celery>=0.44b0",
    "opentelemetry-instrumentation-psycopg2>=0.44b0",
    "opentelemetry-instrumentation-redis>=0.44b0",
    "opentelemetry-instrumentation-openai>=0.40.7",
    "opentelemetry-propagator-aws-xray>=1.0.2",
    "pypdf2>=3.0.1",
    "pydantic-ai==0.2.6",
    "playwright>=1.48.0",
    "stagehand>=0.1.0",
    "holidays>=0.76",
]

[tool.uv]
dev-dependencies = [
    "ipdb>=0.13.13,<0.14.0",
    "jupyterlab>=4.2.3,<5.0.0",
    "graphviz==0.20.3",
    "pyright>=1.1.390",
    "django-types>=0.20.0",
    "djangorestframework-types>=0.9.0",
]

[tool.ds.scripts]
_.env = { DJANGO_SETTINGS_MODULE = "didero.settings.common" }

# Basic config from https://docs.astral.sh/ruff/configuration/
[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "site-packages",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.8
target-version = "py38"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["E4", "E7", "E9", "F"]
# F403 is "from module import *", which we use a lot
# E402 is "module level import not at top of file", which isn't great but has to be done some places
ignore = ["F403", "E402"]

# Allow fix for all enabled rules (when `--fix`) is provided, except unused-imports (F401).
fixable = ["ALL"]
unfixable = ["F401", "E402", "F811", "F403"]

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = false

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.

docstring-code-line-length = "dynamic"
