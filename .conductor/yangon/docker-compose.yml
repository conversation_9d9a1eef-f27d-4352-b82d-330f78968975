# This yml is used for local development (`docker compose up`).
# AWS uses json task definitions instead

services:
  redis:
    restart: unless-stopped
    image: redis:7.0.5-alpine
    ports:
      - "6379:6379"
    expose:
      - 6379
  db:
    build:
      context: .
      dockerfile: pg/Dockerfile
    image: didero-postgres
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - ./.env.docker
    ports:
      - "5432:5432"
    expose:
      - 5432
  opensearch:
    image: opensearchproject/opensearch:2.11.0
    restart: unless-stopped
    volumes:
      - opensearch_data:/usr/share/opensearch/data
    ports:
      - 9200:9200
    expose:
      - 9200
    environment:
      "discovery.type": "single-node"
      "plugins.security.disabled": true
  otel-collector:
    image: amazon/aws-otel-collector:latest
    command: ["--config=/etc/otel-collector-config.yaml"]
    env_file:
      - ./.env.docker
    volumes:
      - ./docker/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317" # OTLP gRPC
      - "4318:4318" # OTLP HTTP
    environment:
      - AWS_REGION=us-east-2
      # These will be populated by make docker-dev-start from AWS SSO
      - AWS_ACCESS_KEY_ID
      - AWS_SECRET_ACCESS_KEY
      - AWS_SESSION_TOKEN
      - LANGFUSE_AUTH_TOKEN

volumes:
  static_volume: {}
  postgres_data: {}
  opensearch_data: {}
