migrate() {
    uv run python manage.py migrate
}

init_usergroups() {
    uv run python manage.py group_permissions --create-default
}

gen_demo_data() {
    uv run python manage.py generate_demo_data
}

init_search() {
    uv run python manage.py opensearch index delete 
    uv run python manage.py opensearch index create
    uv run python manage.py opensearch document index
}

update_fx_rates() {
    uv run python manage.py update_rates
}

usage() {
    echo "Usage: $0 check script for functions"
    exit 1
}

if [ "$#" -lt 1 ]; then
    usage
fi

flag=$1

if [ "$flag" == "migrate" ]; then
    migrate
elif [ "$flag" == "init_usergroups" ]; then
    init_usergroups
elif [ "$flag" == "init_search" ]; then
    init_search
elif [ "$flag" == "fx" ]; then
    update_fx_rates
elif [ "$flag" == "gendata" ]; then
    gen_demo_data
elif [ "$flag" == "all" ]; then
    migrate
    init_usergroups
    gen_demo_data
    init_search
    update_fx_rates
else
    usage
fi