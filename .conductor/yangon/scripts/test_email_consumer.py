#!/usr/bin/env python
"""
Test email consumer with existing suppliers in the database.

This script allows you to test email processing with suppliers that already exist
in your database, rather than creating new test suppliers.

Usage:
    # List existing suppliers
    python scripts/test_email_with_existing_supplier.py --list

    # Test with existing supplier by ID
    python scripts/test_email_with_existing_supplier.py --supplier-id 123

    # Test with existing supplier by name
    python scripts/test_email_with_existing_supplier.py --supplier-name "Acme Corp"

    # Test with existing supplier by domain
    python scripts/test_email_with_existing_supplier.py --supplier-domain "acme.com"
"""

import os
import sys

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.local")

import django

django.setup()

import argparse
from datetime import datetime, timedelta
from typing import Optional

from django.db.models import Q

from didero.email_consumer.main import handle_message_dict
from didero.emails.models import EmailCredentialNylas
from didero.suppliers.models import (
    Communication,
    Supplier,
    SupplierContact,
    SupplierDomain,
)
from didero.users.models import Team


def list_suppliers(team_name: Optional[str] = None):
    """List all suppliers, optionally filtered by team."""
    query = Supplier.objects.filter(archived_at__isnull=True)

    if team_name:
        query = query.filter(team__name=team_name)

    suppliers = query.select_related("team").prefetch_related("domains", "contacts")

    if not suppliers.exists():
        print("No active suppliers found.")
        return

    print(f"\n{'='*80}")
    print(f"{'ID':<6} {'Name':<30} {'Team':<20} {'Domains/Contacts'}")
    print(f"{'='*80}")

    for supplier in suppliers:
        domains = list(supplier.domains.values_list("domain", flat=True))[:3]
        contacts = list(
            supplier.contacts.filter(archived_at__isnull=True).values_list(
                "email", flat=True
            )
        )[:3]

        domain_str = ", ".join(domains)
        if supplier.domains.count() > 3:
            domain_str += f" (+{supplier.domains.count() - 3} more)"

        contact_str = ", ".join(contacts)
        if supplier.contacts.filter(archived_at__isnull=True).count() > 3:
            contact_str += f" (+{supplier.contacts.filter(archived_at__isnull=True).count() - 3} more)"

        print(
            f"{supplier.id:<6} {supplier.name[:30]:<30} {supplier.team.name[:20]:<20}"
        )
        if domain_str:
            print(f"{'':6} Domains: {domain_str}")
        if contact_str:
            print(f"{'':6} Contacts: {contact_str}")
        print()


def find_supplier(
    supplier_id: Optional[int] = None,
    supplier_name: Optional[str] = None,
    supplier_domain: Optional[str] = None,
    team: Optional[Team] = None,
) -> Optional[Supplier]:
    """Find a supplier by ID, name, or domain."""

    if supplier_id:
        return Supplier.objects.filter(id=supplier_id, archived_at__isnull=True).first()

    if supplier_name:
        query = Supplier.objects.filter(
            name__icontains=supplier_name, archived_at__isnull=True
        )
        if team:
            query = query.filter(team=team)

        suppliers = list(query)
        if len(suppliers) == 1:
            return suppliers[0]
        elif len(suppliers) > 1:
            print(f"Multiple suppliers found matching '{supplier_name}':")
            for s in suppliers:
                print(f"  - ID: {s.id}, Name: {s.name}, Team: {s.team.name}")
            print("Please use --supplier-id to specify exactly which one.")
            return None
        else:
            print(f"No supplier found matching name '{supplier_name}'")
            return None

    if supplier_domain:
        domain_obj = (
            SupplierDomain.objects.filter(domain=supplier_domain)
            .select_related("supplier")
            .first()
        )

        if domain_obj and not domain_obj.supplier.is_archived():
            return domain_obj.supplier
        else:
            print(f"No active supplier found with domain '{supplier_domain}'")
            return None

    return None


def get_email_for_supplier(supplier: Supplier, direction: str = "incoming") -> str:
    """Get an appropriate email address for the supplier."""
    # Try to get from domains first
    domain = supplier.domains.first()
    if domain:
        return f"noreply@{domain.domain}"

    # Try to get from contacts
    contact = supplier.contacts.filter(archived_at__isnull=True).first()
    if contact and contact.email:
        return contact.email

    # Fallback
    return f"supplier@{supplier.name.lower().replace(' ', '')}.com"


def test_with_supplier(
    supplier: Supplier,
    direction: str = "incoming",
    credential_email: Optional[str] = None,
    subject: Optional[str] = None,
    body: Optional[str] = None,
):
    """Test email processing with a specific supplier."""

    # Get or create credential
    cred_email = credential_email or "<EMAIL>"
    # Try to get existing credential first
    try:
        cred = EmailCredentialNylas.objects.get(
            email_address=cred_email, team=supplier.team
        )
        created = False
    except EmailCredentialNylas.DoesNotExist:
        # Create with unique grant_id
        cred = EmailCredentialNylas.objects.create(
            email_address=cred_email,
            team=supplier.team,
            grant_id=f"test-grant-{datetime.now().timestamp()}",
            status="valid",
            expires_at=datetime.now() + timedelta(days=30),
        )
        created = True

    if created:
        print(f"Created test credential for {cred_email}")

    # Get supplier email
    supplier_email = get_email_for_supplier(supplier, direction)

    # Create email message
    if direction == "incoming":
        from_addr = supplier_email
        to_addr = cred_email
        default_subject = f"Update from {supplier.name}"
        default_body = f"This is a test message from {supplier.name}."
    else:
        from_addr = cred_email
        to_addr = supplier_email
        default_subject = f"Message to {supplier.name}"
        default_body = f"This is a test message to {supplier.name}."

    email_message = {
        "id": f"msg-test-{datetime.now().timestamp()}",
        "grant_id": cred.grant_id,
        "thread_id": f"thread-test-{datetime.now().timestamp()}",
        "date": int(datetime.now().timestamp()),
        "folders": ["sent"] if direction == "outgoing" else ["inbox"],
        "subject": subject or default_subject,
        "body": body or default_body,
        "from": [{"name": from_addr.split("@")[0], "email": from_addr}],
        "to": [{"name": to_addr.split("@")[0], "email": to_addr}],
        "cc": [],
        "bcc": [],
        "reply_to": [],
        "attachments": [],
    }

    print(f"\n{'='*80}")
    print(f"Testing {direction} email with {supplier.name}")
    print(f"{'='*80}")
    print(f"Supplier ID: {supplier.id}")
    print(f"Team: {supplier.team.name}")
    print(f"From: {from_addr}")
    print(f"To: {to_addr}")
    print(f"Subject: {email_message['subject']}")
    print(f"{'='*80}\n")

    # Show supplier's domains and contacts
    domains = list(supplier.domains.values_list("domain", flat=True))
    contacts = list(
        supplier.contacts.filter(archived_at__isnull=True).values_list(
            "email", flat=True
        )
    )

    if domains:
        print(f"Supplier domains: {', '.join(domains)}")
    if contacts:
        print(f"Supplier contacts: {', '.join(contacts)}")
    print()

    # Process the email
    pubsub_message = {
        "id": f"pubsub-{datetime.now().timestamp()}",
        "type": "message.created",
        "data": {"object": email_message},
    }
    handle_message_dict(pubsub_message)

    # Check result
    comm = Communication.objects.filter(email_message_id=email_message["id"]).first()

    if comm:
        print("✅ SUCCESS: Communication created!")
        print(f"  - ID: {comm.id}")
        print(f"  - Supplier: {comm.supplier.name}")
        print(f"  - Direction: {comm.get_direction_display()}")
        print(f"  - Time: {comm.comm_time}")
    else:
        print("❌ ERROR: No communication created")
        print("\nTroubleshooting:")
        print(f"  - Check if email matches supplier domains: {domains}")
        print(f"  - Check if email matches supplier contacts: {contacts}")
        print("  - Verify supplier is not archived")
        print(f"  - Ensure credential exists for {cred_email}")


def main():
    parser = argparse.ArgumentParser(
        description="Test email consumer with existing suppliers"
    )

    # Actions
    parser.add_argument("--list", action="store_true", help="List all active suppliers")

    # Supplier selection
    parser.add_argument("--supplier-id", type=int, help="Test with supplier by ID")
    parser.add_argument(
        "--supplier-name", help="Test with supplier by name (partial match)"
    )
    parser.add_argument("--supplier-domain", help="Test with supplier by domain")

    # Email options
    parser.add_argument(
        "--direction",
        choices=["incoming", "outgoing"],
        default="incoming",
        help="Email direction",
    )
    parser.add_argument(
        "--credential-email",
        help="Email address for the credential (default: <EMAIL>)",
    )
    parser.add_argument("--subject", help="Custom email subject")
    parser.add_argument("--body", help="Custom email body")

    # Filtering
    parser.add_argument("--team", help="Filter by team name")

    args = parser.parse_args()

    # Handle list action
    if args.list:
        list_suppliers(args.team)
        return

    # Find team if specified
    team = None
    if args.team:
        team = Team.objects.filter(name=args.team).first()
        if not team:
            print(f"Team '{args.team}' not found")
            return

    # Find supplier
    supplier = find_supplier(
        supplier_id=args.supplier_id,
        supplier_name=args.supplier_name,
        supplier_domain=args.supplier_domain,
        team=team,
    )

    if not supplier:
        if not any([args.supplier_id, args.supplier_name, args.supplier_domain]):
            print(
                "Please specify a supplier using --supplier-id, --supplier-name, or --supplier-domain"
            )
            print("Use --list to see available suppliers")
        return

    # Test with the supplier
    test_with_supplier(
        supplier=supplier,
        direction=args.direction,
        credential_email=args.credential_email,
        subject=args.subject,
        body=args.body,
    )


if __name__ == "__main__":
    main()
