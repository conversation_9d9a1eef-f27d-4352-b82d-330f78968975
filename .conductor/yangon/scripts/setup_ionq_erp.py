#!/usr/bin/env python
"""
Setup script for IonQ NetSuite ERP Integration.

This script:
1. Creates or configures IonQ team for ERP integration
2. Sets up ERPIntegrationConfig with field mappings
3. Updates workflow behavior to enable ERP sync
4. Handles both local and production environments

Usage:
    # Local development (creates test team)
    python scripts/setup_ionq_erp.py --create-test-data

    # Production (uses existing team)
    python scripts/setup_ionq_erp.py --team-id=47

    # Dry run
    python scripts/setup_ionq_erp.py --team-id=47 --dry-run
"""

import argparse
import os
import sys
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")
import django

django.setup()

import structlog
from django.contrib.auth import get_user_model
from django.db import transaction

from didero.addresses.models import Address
from didero.integrations.models import ERPIntegrationConfig
from didero.suppliers.models import Supplier
from didero.users.models import Team
from didero.users.models.team_models import TeamDomain
from didero.workflows.models import UserWorkflow, WorkflowBehaviorConfig

logger = structlog.get_logger(__name__)
User = get_user_model()


def is_local_environment():
    """Check if we're running in local development"""
    return os.environ.get("DJANGO_SETTINGS_MODULE", "").endswith(".common")


def create_test_ionq_team():
    """Create test IonQ team for local development"""
    print("\n📋 Creating test IonQ team for local development...")

    with transaction.atomic():
        # Create team with high ID to avoid conflicts
        team = Team.objects.create(id=99999, name="IonQ Test (Local)")
        print(f"✅ Created team: {team.name} (ID: {team.id})")

        # Create team domain
        domain = TeamDomain.objects.create(team=team, domain="ionqtest.com")
        print(f"✅ Created domain: {domain.domain}")

        # Create AI user with correct format
        ai_user = User.objects.create(
            email=f"contractors+{domain.domain}@didero.ai",
            first_name="Didero",
            last_name="AI",
            is_active=True,
        )
        ai_user.teams.add(team)
        print(f"✅ Created AI user: {ai_user.email}")

        # Create test supplier
        supplier = Supplier.objects.create(
            name="Test NetSuite Supplier",
            team=team,
            website_url="https://testsupplier.com",
            description="Test supplier for NetSuite integration",
        )

        # Create supplier address
        Address.objects.create(
            supplier=supplier,
            team=team,
            line_1="123 Test Street",
            city="College Park",
            state_or_province="MD",
            postal_code="20740",
            country="US",
            is_default=True,
        )
        print(f"✅ Created test supplier: {supplier.name}")

    return team


def setup_erp_config(team, dry_run=False):
    """Create or update ERP integration configuration"""
    print(f"\n⚙️  Setting up ERP configuration for {team.name}...")

    # Check if config already exists
    existing_config = ERPIntegrationConfig.objects.filter(team=team).first()

    if existing_config:
        print(f"⚠️  ERP configuration already exists for {team.name}")
        if not dry_run:
            # Skip prompt in non-interactive mode
            import sys

            if sys.stdin.isatty():
                response = input("Update existing configuration? (y/n): ")
                if response.lower() != "y":
                    print("Skipping ERP configuration update")
                    return existing_config
            else:
                print("Skipping ERP configuration update (non-interactive mode)")
                return existing_config

    # Import field mappings from constants
    from didero.integrations.erp.customers.ionq.constants import FIELD_MAP

    config_data = {
        "erp_type": "netsuite",
        "enabled": True,
        "field_mappings": FIELD_MAP,
        "config": {"api_version": "2023_2"},
    }

    if dry_run:
        print("🔍 DRY RUN - Would create/update ERP config:")
        for key, value in config_data.items():
            print(f"   {key}: {value}")
        return None

    if existing_config:
        # Update existing
        for key, value in config_data.items():
            setattr(existing_config, key, value)
        existing_config.save()
        print("✅ Updated existing ERP configuration")
        return existing_config
    else:
        # Create new
        config = ERPIntegrationConfig.objects.create(team=team, **config_data)
        print("✅ Created new ERP configuration")
        return config


def setup_workflow_behavior(team, dry_run=False):
    """Update workflow behavior to enable ERP sync"""
    print(f"\n🔧 Setting up workflow behavior for {team.name}...")

    # Get shipment workflow
    try:
        workflow = UserWorkflow.objects.get(
            team=team, workflow_type="purchase_order_shipped"
        )
    except UserWorkflow.DoesNotExist:
        print("❌ Shipment workflow not found for team")
        return None

    # Get or create behavior config
    try:
        behavior_config = workflow.behavior_config
        print("📋 Found existing behavior config")
    except WorkflowBehaviorConfig.DoesNotExist:
        if dry_run:
            print("🔍 DRY RUN - Would create new behavior config")
            return None
        behavior_config = WorkflowBehaviorConfig.objects.create(
            workflow=workflow, config={}
        )
        print("✅ Created new behavior config")

    # Update config
    current_config = behavior_config.config or {}

    # Preserve existing settings, add ERP settings
    updated_config = {
        **current_config,
        "enable_erp_sync": True,
        "erp_sync_mode": "manual",  # Start with manual mode for safety
        "require_human_validation": current_config.get(
            "require_human_validation", True
        ),
    }

    if dry_run:
        print("🔍 DRY RUN - Would update behavior config:")
        print("   enable_erp_sync: True")
        print("   erp_sync_mode: manual")
        print(
            f"   require_human_validation: {updated_config['require_human_validation']}"
        )
        return None

    behavior_config.config = updated_config
    behavior_config.save()
    print("✅ Updated workflow behavior config")
    print(f"   ERP sync enabled: {updated_config['enable_erp_sync']}")
    print(f"   Sync mode: {updated_config['erp_sync_mode']}")
    print(f"   Human validation: {updated_config['require_human_validation']}")

    return behavior_config


def verify_credentials(team):
    """Verify that credentials are configured in environment"""
    print(f"\n🔑 Checking credentials for {team.name}...")

    # Determine credential prefix - IonQ always uses IONQ prefix
    if team.id in [4, 173]:  # IonQ test and production teams
        prefix = "IONQ_NETSUITE"
    else:
        prefix = f"{team.id}_NETSUITE"

    required_creds = [
        f"{prefix}_ACCOUNT_ID",
        f"{prefix}_CONSUMER_KEY",
        f"{prefix}_CONSUMER_SECRET",
        f"{prefix}_TOKEN_ID",
        f"{prefix}_TOKEN_SECRET",
    ]

    missing = []
    for cred in required_creds:
        if not os.environ.get(cred):
            missing.append(cred)

    if missing:
        print("⚠️  Missing credentials in environment:")
        for cred in missing:
            print(f"   - {cred}")
        print("\n📝 Add these to your .env file")
        return False
    else:
        print("✅ All required credentials found")
        return True


def main():
    parser = argparse.ArgumentParser(description="Setup IonQ ERP Integration")
    parser.add_argument("--team-id", type=int, help="Team ID (for production)")
    parser.add_argument(
        "--create-test-data",
        action="store_true",
        help="Create test team (for local dev)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes",
    )

    args = parser.parse_args()

    print("=" * 60)
    print("🚀 IonQ NetSuite ERP Integration Setup")
    print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # Determine team
    if args.create_test_data and is_local_environment():
        if args.dry_run:
            print("🔍 DRY RUN - Would create test team")
            team = None
        else:
            # Check if test team already exists
            existing_team = Team.objects.filter(id=99999).first()
            if existing_team:
                print(f"✅ Using existing test team: {existing_team.name}")
                team = existing_team
            else:
                team = create_test_ionq_team()
    elif args.team_id:
        try:
            team = Team.objects.get(id=args.team_id)
            print(f"✅ Found team: {team.name} (ID: {team.id})")
        except Team.DoesNotExist:
            print(f"❌ Team with ID {args.team_id} not found")
            return
    else:
        print("❌ Please specify --team-id or --create-test-data")
        return

    if args.dry_run:
        print("\n🔍 Running in DRY RUN mode - no changes will be made")

    # Setup ERP configuration
    if team or args.dry_run:
        erp_config = setup_erp_config(team, args.dry_run)

        # Setup workflow behavior
        behavior_config = setup_workflow_behavior(team, args.dry_run)

        # Verify credentials
        if team:
            verify_credentials(team)

    print("\n" + "=" * 60)

    if args.dry_run:
        print("🔍 DRY RUN COMPLETE - No changes were made")
    else:
        print("✅ SETUP COMPLETE!")

        if team:
            print("\n📋 Next steps:")
            print("1. Ensure credentials are in .env file")
            print(f"2. Run: python scripts/test_erp_connection.py --team-id={team.id}")
            print(
                f"3. Test with: python scripts/test_erp_shipment_e2e.py --team-id={team.id}"
            )

            if is_local_environment():
                print("\n💡 Local development tips:")
                print(f"   - Team ID: {team.id}")
                domain = team.team_domains.first()
                if domain:
                    print(f"   - AI User: contractors+{domain.domain}@didero.ai")
                print("   - Credential prefix: IONQ_NETSUITE_*")

    print("=" * 60)


if __name__ == "__main__":
    main()
