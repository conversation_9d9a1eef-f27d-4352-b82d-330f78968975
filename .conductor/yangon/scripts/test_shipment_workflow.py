#!/usr/bin/env python
"""
Test script for triggering the Shipment workflow.

This script can be used to test the Shipment workflow with various scenarios:
1. Test normal shipment notifications with tracking
2. Test pickup notifications
3. Test validation errors (missing tracking, invalid carrier, etc.)
4. List all configured shipment workflows across teams
5. Check workflow status (DAG vs Core implementation)

Usage:
    # Run with Django environment
    DJANGO_SETTINGS_MODULE=didero.settings.local python scripts/test_shipment_workflow.py

    # Or use Django management command
    python manage.py runscript test_shipment_workflow
"""

import logging
import os
import sys
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional

import django
from django.utils import timezone

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.local")
django.setup()

from didero.addresses.models import Address
from didero.emails.models import EmailThread
from didero.orders.models import (
    OrderItem,
    PurchaseOrder,
)
from didero.suppliers.models import Communication, CommunicationEmailRecipient, Supplier
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow
from didero.workflows.schemas import (
    WorkflowTrigger,
    WorkflowType,
)
from didero.workflows.utils import ShipmentWorkflowContext, trigger_workflow_if_exists

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Shipment notification scenarios
class ShipmentScenario:
    NORMAL_SHIPMENT = "normal_shipment"  # Normal shipment with tracking
    PICKUP_READY = "pickup_ready"  # Ready for pickup notification
    MISSING_TRACKING = "missing_tracking"  # Shipment without tracking number
    INVALID_CARRIER = "invalid_carrier"  # Invalid or missing carrier name
    UNMATCHED_ITEMS = "unmatched_items"  # Items don't match PO
    MISSING_PO = "missing_po"  # PO number doesn't exist
    MISSING_SHIPMENT_DATE = "missing_date"  # No shipment date
    PARTIAL_SHIPMENT = "partial_shipment"  # Only some items shipped


def create_test_po(team: Team, supplier: Supplier, po_number: str) -> PurchaseOrder:
    """Create a test purchase order with items."""
    # Create address
    address = Address.objects.create(
        line_1="123 Test Street",
        city="Test City",
        state_or_province="CA",
        postal_code="90210",
        country="US",
    )

    # Create PO
    po = PurchaseOrder.objects.create(
        team=team,
        supplier=supplier,
        po_number=po_number,
        placement_time=timezone.now() - timedelta(days=3),
        total_cost=Decimal("1500.00"),
        total_cost_currency="USD",
        shipping_address=address,
        sender_address=address,
    )

    # Create items
    from didero.items.models import Item

    items_data = [
        {"sku": "WIDGET-001", "name": "Test Widget", "quantity": 10, "price": 50.00},
        {"sku": "GADGET-002", "name": "Test Gadget", "quantity": 5, "price": 100.00},
        {"sku": "PART-003", "name": "Test Part", "quantity": 20, "price": 25.00},
    ]

    for item_data in items_data:
        item, _ = Item.objects.get_or_create(
            item_sku=item_data["sku"],
            team=team,
            defaults={
                "description": item_data["name"],
                "item_number": item_data["sku"],
                "price": Decimal(str(item_data["price"])),
                "price_currency": "USD",
            },
        )

        OrderItem.objects.create(
            purchase_order=po,
            item=item,
            quantity=item_data["quantity"],
            price=Decimal(str(item_data["price"])),
            price_currency="USD",
        )

    logger.info(f"Created test PO: {po_number} with {len(items_data)} items")
    return po


def generate_shipment_email(
    po: PurchaseOrder, scenario: str = ShipmentScenario.NORMAL_SHIPMENT
) -> str:
    """Generate shipment notification email content based on scenario."""
    supplier_name = po.supplier.name if po.supplier else "Test Supplier"
    po_number = po.po_number

    if scenario == ShipmentScenario.PICKUP_READY:
        return f"""
Dear Valued Customer,

Your order {po_number} from {supplier_name} is ready for pickup.

PICKUP DETAILS:
Location: {supplier_name} Warehouse
Address: 456 Supplier Avenue, Industrial District, Commerce City, CA 90210
Hours: Monday-Friday 8:00 AM - 5:00 PM

Please bring your order confirmation and a valid ID when picking up your order.

If you have any questions, please contact <NAME_EMAIL>

Best regards,
{supplier_name} Shipping Department
"""

    # Get items from PO
    items = OrderItem.objects.filter(purchase_order=po)

    # Build items section based on scenario
    if scenario == ShipmentScenario.UNMATCHED_ITEMS:
        # Include items that don't exist in the PO
        items_section = """
ITEMS SHIPPED:
- Widget XYZ (SKU: WRONG-001) - Quantity: 5
- Gadget ABC (SKU: WRONG-002) - Quantity: 10
- Part 123 (SKU: WRONG-003) - Quantity: 15
"""
    elif scenario == ShipmentScenario.PARTIAL_SHIPMENT:
        # Only ship some items
        items_section = f"""
ITEMS SHIPPED:
- {items[0].item.description} (SKU: {items[0].item.item_sku}) - Quantity: {items[0].quantity // 2}
"""
    else:
        # Normal shipment - all items
        items_section = "ITEMS SHIPPED:\n"
        for item in items:
            items_section += f"- {item.item.description} (SKU: {item.item.item_sku}) - Quantity: {item.quantity}\n"

    # Carrier and tracking info based on scenario
    if scenario == ShipmentScenario.INVALID_CARRIER:
        carrier_info = "Carrier: [CARRIER NAME NOT FOUND]"
    elif scenario == ShipmentScenario.MISSING_TRACKING:
        carrier_info = "Carrier: FedEx\nTracking Number: Not available at this time"
    else:
        carrier_info = "Carrier: FedEx\nTracking Number: 123456789012"

    # Shipment date based on scenario
    if scenario == ShipmentScenario.MISSING_SHIPMENT_DATE:
        date_info = "Shipment Date: [DATE NOT PROVIDED]"
    else:
        date_info = f"Shipment Date: {datetime.now().strftime('%B %d, %Y')}"

    # PO reference based on scenario
    if scenario == ShipmentScenario.MISSING_PO:
        po_reference = "PO-NONEXISTENT-999"
    else:
        po_reference = po_number

    return f"""
Subject: Shipment Confirmation - Order {po_reference}

Dear Customer,

This email confirms that your order has been shipped.

ORDER DETAILS:
Purchase Order: {po_reference}
Supplier: {supplier_name}
{date_info}

SHIPPING INFORMATION:
{carrier_info}
Estimated Delivery: {(datetime.now() + timedelta(days=3)).strftime('%B %d, %Y')}

{items_section}

You can track your shipment using the tracking number provided above.

Thank you for your business!

Best regards,
{supplier_name} Shipping Department
"""


def test_shipment_scenario(
    team_id: str,
    scenario: str = ShipmentScenario.NORMAL_SHIPMENT,
    existing_po: Optional[PurchaseOrder] = None,
):
    """Test shipment workflow with a specific scenario."""
    try:
        team = Team.objects.get(id=team_id)
        logger.info(f"Testing Shipment workflow for team: {team.name} (ID: {team_id})")
        logger.info(f"Scenario: {scenario}")

        # Get or create supplier
        supplier, _ = Supplier.objects.get_or_create(
            name="Test Shipping Supplier",
            team=team,
            defaults={"website_url": "https://testshipper.example.com"},
        )

        # Create or use existing PO
        if existing_po:
            po = existing_po
        else:
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            po_number = f"PO-SHIP-TEST-{timestamp}"
            po = create_test_po(team, supplier, po_number)

        # Generate unique email ID
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S-%f")

        # Determine notification type
        notification_type = (
            "pickup" if scenario == ShipmentScenario.PICKUP_READY else "shipped"
        )

        # Generate email content
        email_content = generate_shipment_email(po, scenario)

        # Create email thread
        email_thread = EmailThread.objects.create(
            team=team,
            thread_id=f"thread-ship-{unique_id}",
        )

        # Create email
        email = Communication.objects.create(
            team=team,
            supplier=supplier,
            email_thread=email_thread,
            email_message_id=f"ship-test-{unique_id}@example.com",
            email_subject=f"Shipment Notification - {po.po_number}",
            email_from="<EMAIL>",
            email_content=email_content,
            comm_type="email",
            direction="incoming",
            comm_time=timezone.now(),
        )

        # Add recipient
        CommunicationEmailRecipient.objects.create(
            email_address="<EMAIL>",
            communication_to=email,
        )

        logger.info(f"Created test email: {email.email_message_id}")

        # Check if workflow exists
        workflow = UserWorkflow.objects.filter(
            team=team,
            workflow_type=WorkflowType.PURCHASE_ORDER_SHIPPED,
            trigger=WorkflowTrigger.ON_ORDER_SHIPPED_EMAIL_RECEIVED,
        ).first()

        if not workflow:
            logger.error("No shipment workflow configured for this team")
            return

        # Check if it's core or DAG
        is_core = workflow.current_snapshot is None
        logger.info(f"Workflow type: {'Core' if is_core else 'DAG'}")

        if is_core:
            # Show behavior config
            try:
                config = workflow.behavior_config.get_config_as_pydantic()
                logger.info(f"Behavior config: {config.model_dump_json(indent=2)}")
            except Exception:
                logger.warning("No behavior config found")

        # Show which queue will be used
        from didero.workflows.queue_config import get_queue_for_workflow_type

        queue_name = get_queue_for_workflow_type(
            WorkflowType.PURCHASE_ORDER_SHIPPED.value
        )
        logger.info(f"Queue to be used: {queue_name}")

        # Trigger workflow
        logger.info(
            f"Triggering shipment workflow with notification_type: {notification_type}"
        )
        workflow_run = trigger_workflow_if_exists(
            WorkflowType.PURCHASE_ORDER_SHIPPED.value,
            WorkflowTrigger.ON_ORDER_SHIPPED_EMAIL_RECEIVED.value,
            team,
            context_object=ShipmentWorkflowContext(
                email=email, notification_type=notification_type
            ),
        )

        if workflow_run:
            logger.info("✓ Workflow triggered successfully")
            if workflow_run:  # DAG workflows return WorkflowRun
                logger.info(f"  Workflow run ID: {workflow_run.uuid}")
        else:
            logger.info("✓ Core workflow triggered (no WorkflowRun object)")

    except Exception as e:
        logger.error(f"Error testing shipment workflow: {str(e)}", exc_info=True)


def list_shipment_workflows():
    """List all configured shipment workflows across teams."""
    workflows = UserWorkflow.objects.filter(
        workflow_type=WorkflowType.PURCHASE_ORDER_SHIPPED
    ).select_related("team", "behavior_config")

    if not workflows:
        logger.info("No shipment workflows found")
        return

    logger.info(f"\nFound {len(workflows)} shipment workflows:\n")

    for workflow in workflows:
        team_name = workflow.team.name if workflow.team else "Unknown"
        workflow_type = "Core" if workflow.current_snapshot is None else "DAG"

        logger.info(f"Team: {team_name} (ID: {workflow.team.id})")
        logger.info(f"  - Workflow ID: {workflow.id}")
        logger.info(f"  - Type: {workflow_type}")
        logger.info(f"  - Trigger: {workflow.trigger}")

        if workflow_type == "Core":
            try:
                _ = workflow.behavior_config
                logger.info("  - Has behavior config: Yes")
            except Exception:
                logger.info("  - Has behavior config: No")

        logger.info("")


def main():
    """Main function to run test scenarios."""
    print("\n" + "=" * 60)
    print("SHIPMENT WORKFLOW TEST TOOL")
    print("=" * 60)

    while True:
        print("\nOptions:")
        print("1. List all configured shipment workflows")
        print("2. Test shipment workflow for a specific team")
        print("3. Exit")

        choice = input("\nSelect an option (1-3): ").strip()

        if choice == "1":
            list_shipment_workflows()
            input("\nPress Enter to continue...")

        elif choice == "2":
            # Get team ID
            team_id = input("\nEnter team ID: ").strip()
            if not team_id:
                print("Team ID is required!")
                continue

            try:
                team = Team.objects.get(id=team_id)
                print(f"Selected team: {team.name}")
            except Team.DoesNotExist:
                print(f"Team with ID {team_id} not found!")
                continue

            # Check if workflow exists
            workflow = UserWorkflow.objects.filter(
                team=team,
                workflow_type=WorkflowType.PURCHASE_ORDER_SHIPPED.value,
                trigger=WorkflowTrigger.ON_ORDER_SHIPPED_EMAIL_RECEIVED.value,
            ).first()

            if not workflow:
                print(f"\n⚠️  No shipment workflow configured for team {team.name}")
                create = input("Would you like to create one? (y/n): ").lower()
                if create == "y":
                    from didero.workflows.core_workflows.shipments.setup import (
                        create_core_shipment_workflow_for_team,
                    )

                    workflow = create_core_shipment_workflow_for_team(team_id)
                    print(f"✅ Created core shipment workflow for team {team.name}")
                else:
                    continue
            else:
                workflow_type = "Core" if workflow.current_snapshot is None else "DAG"
                print(
                    f"\n✅ Found {workflow_type} shipment workflow for team {team.name}"
                )

            # Ask about existing PO
            use_existing = input(
                "\nDo you have an existing PO to test with? (y/n): "
            ).lower()
            existing_po = None

            if use_existing == "y":
                po_number = input("Enter PO number: ").strip()
                try:
                    existing_po = PurchaseOrder.objects.get(
                        po_number=po_number, team=team
                    )
                    print(f"✅ Found PO: {po_number}")
                    print(
                        f"   Supplier: {existing_po.supplier.name if existing_po.supplier else 'Unknown'}"
                    )
                    print(f"   Items: {existing_po.items.count()}")
                except PurchaseOrder.DoesNotExist:
                    print(f"❌ PO {po_number} not found for this team")
                    continue

            # Select scenario
            print("\nSelect test scenario:")
            scenarios = [
                (
                    "1",
                    ShipmentScenario.NORMAL_SHIPMENT,
                    "Normal shipment with tracking",
                ),
                ("2", ShipmentScenario.PICKUP_READY, "Ready for pickup notification"),
                (
                    "3",
                    ShipmentScenario.MISSING_TRACKING,
                    "Shipment without tracking number",
                ),
                ("4", ShipmentScenario.INVALID_CARRIER, "Invalid or missing carrier"),
                ("5", ShipmentScenario.UNMATCHED_ITEMS, "Items don't match PO"),
                ("6", ShipmentScenario.MISSING_PO, "PO number doesn't exist"),
                ("7", ShipmentScenario.MISSING_SHIPMENT_DATE, "No shipment date"),
                ("8", ShipmentScenario.PARTIAL_SHIPMENT, "Only some items shipped"),
            ]

            for num, _, desc in scenarios:
                print(f"{num}. {desc}")

            scenario_choice = input("\nSelect scenario (1-8): ").strip()
            scenario_map = {num: scenario for num, scenario, _ in scenarios}

            if scenario_choice not in scenario_map:
                print("Invalid scenario choice!")
                continue

            selected_scenario = scenario_map[scenario_choice]

            # Confirm before running
            print("\n" + "=" * 50)
            print("Ready to test shipment workflow:")
            print(f"  Team: {team.name} (ID: {team_id})")
            print(f"  Scenario: {selected_scenario}")
            if existing_po:
                print(f"  Using PO: {existing_po.po_number}")
            else:
                print("  Will create new test PO")
            print("=" * 50)

            proceed = input("\nProceed with test? (y/n): ").lower()
            if proceed == "y":
                test_shipment_scenario(team_id, selected_scenario, existing_po)
                input("\nPress Enter to continue...")

        elif choice == "3":
            print("\nExiting...")
            break
        else:
            print("Invalid choice! Please select 1-3.")


if __name__ == "__main__":
    main()
