import os
import sys

import boto3
from dotenv import load_dotenv


def purge_dev_queues():
    # Load environment variables from .env file
    load_dotenv()

    developer_name = os.getenv("DIDERO_DEVELOPER_NAME")
    if not developer_name:
        print("Error: DIDERO_DEVELOPER_NAME environment variable is not set.")
        sys.exit(1)

    profile_name = "didero-dev"
    session = boto3.Session(profile_name=profile_name)
    sqs = session.client("sqs", region_name="us-east-2")  # Update region as needed

    prefix = f"didero-dev-{developer_name}"

    try:
        # List all queues with the specified prefix
        response = sqs.list_queues(QueueNamePrefix=prefix)
        queue_urls = response.get("QueueUrls", [])

        if not queue_urls:
            print(f"No queues found with prefix '{prefix}'.")
            return

        for queue_url in queue_urls:
            print(f"Purging queue: {queue_url}")
            sqs.purge_queue(QueueUrl=queue_url)
            print(f"Queue {queue_url} purged successfully.")

        print(f"Purged {len(queue_urls)} queues with prefix '{prefix}'.")
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    purge_dev_queues()
