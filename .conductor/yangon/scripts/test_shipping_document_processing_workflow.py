#!/usr/bin/env python
"""
Integration test for shipping document processing workflow.

This script tests the shipping document workflow end-to-end:
1. Creates a test shipping document email with PDF attachment
2. Triggers the shipping document processing workflow
3. Verifies the workflow extracts data and stores ShippingDocument
4. Checks that ShippingItems are created

Usage:
    DJANGO_SETTINGS_MODULE=didero.settings.local python scripts/test_shipping_document_processing_workflow.py
"""

import logging
import os
import sys
import time
from datetime import datetime

import django
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.local")
django.setup()

from didero.documents.models import Document, DocumentLink
from didero.documents.schemas import DocumentType
from didero.emails.models import EmailThread
from didero.shipping_documents.models import ShippingDocument, ShippingItem
from didero.suppliers.models import Communication, CommunicationEmailRecipient, Supplier
from didero.users.models.team_models import Team
from didero.workflows.models import UserWorkflow
from didero.workflows.schemas import WorkflowTrigger, WorkflowType
from didero.workflows.utils import (
    EmailWorkflowContext,
    trigger_workflow_if_exists_with_status,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_shipping_document_pdf() -> bytes:
    """Create a comprehensive Bill of Lading PDF for testing."""
    # Note: This is still a minimal PDF structure, but with rich BOL content
    # In production, the AI would extract from actual PDF content
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources << /Font << /F1 5 0 R >> >>
>>
endobj

4 0 obj
<<
/Length 2000
>>
stream
BT
/F1 16 Tf
200 750 Td
(BILL OF LADING - ORIGINAL) Tj
/F1 12 Tf
72 720 Td
(Document Reference: BOL-TEST-2024-12345) Tj
0 -15 Td
(Bill of Lading Date: 2024-01-15) Tj
0 -20 Td
(SHIPPER/EXPORTER:) Tj
0 -15 Td
(Global Manufacturing Ltd.) Tj
0 -15 Td
(123 Industrial Park Road) Tj
0 -15 Td
(Shanghai, China 200001) Tj
0 -20 Td
(CONSIGNEE:) Tj
0 -15 Td
(Tech Imports USA Inc.) Tj
0 -15 Td
(456 Commerce Boulevard, Suite 200) Tj
0 -15 Td
(Los Angeles, CA 90001, United States) Tj
0 -20 Td
(NOTIFY PARTY:) Tj
0 -15 Td
(Logistics Solutions LLC) Tj
0 -15 Td
(789 Distribution Way, Long Beach, CA 90802) Tj
0 -15 Td
(Email: <EMAIL>) Tj
0 -20 Td
(VESSEL: MV Pacific Express) Tj
0 -15 Td
(VOYAGE: PE2024-088) Tj
0 -15 Td
(PORT OF LOADING: Shanghai Port (CNSHA)) Tj
0 -15 Td
(PORT OF DISCHARGE: Los Angeles Port (USLAX)) Tj
0 -20 Td
(CARRIER: Pacific Ocean Lines) Tj
0 -15 Td
(TRACKING: TRK-MASTER-2024-001234) Tj
0 -15 Td
(PO NUMBER: PO-TEST-67890) Tj
0 -20 Td
(CONTAINER INFORMATION:) Tj
0 -15 Td
(Container 1: POLU1234567 / Seal: SL789012) Tj
0 -15 Td
(Container 2: POLU2345678 / Seal: SL890123) Tj
0 -20 Td
(CARGO DETAILS:) Tj
0 -15 Td
(1. WIDGET-A-1000: Electronic Widget Type A) Tj
0 -15 Td
(   Ordered: 1000 units, Shipped: 1000 units) Tj
0 -15 Td
(   Condition: good) Tj
0 -15 Td
(2. WIDGET-B-2000: Industrial Widget Type B) Tj
0 -15 Td
(   Ordered: 500 units, Shipped: 500 units) Tj
0 -15 Td
(   Condition: good) Tj
0 -15 Td
(3. COMP-C-3000: Component C for Assembly) Tj
0 -15 Td
(   Ordered: 2000 units, Shipped: 1950 units) Tj
0 -15 Td
(   Condition: good) Tj
0 -15 Td
(   Note: 50 units short - next shipment) Tj
0 -20 Td
(RECEIVED DATE: 2024-01-10) Tj
0 -15 Td
(SPECIAL INSTRUCTIONS: Handle with care - fragile) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000229 00000 n 
0000002280 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
2358
%%EOF"""
    return pdf_content


def create_test_shipping_email_with_attachment(team_id: str) -> Communication:
    """Create a test shipping document email with PDF attachment."""
    import uuid

    team = Team.objects.get(id=team_id)

    # Get or create a test supplier
    supplier, _ = Supplier.objects.get_or_create(
        name="Test Shipping Supplier",
        team=team,
        defaults={
            "website_url": "https://testshipping.example.com",
        },
    )

    unique_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    bol_number = f"BOL-TEST-{timestamp}"
    po_number = f"PO-TEST-{timestamp}"

    shipping_content = f"""
SHIPPING NOTIFICATION - BILL OF LADING

Dear Customer,

We are pleased to inform you that your shipment has been dispatched and is en route to your facility.

SHIPMENT DETAILS:
=================
Bill of Lading Number: {bol_number}
Purchase Order: {po_number}
Master Tracking: TRK-MASTER-{timestamp}

VESSEL INFORMATION:
==================
Vessel: MV Pacific Express
Voyage Number: PE2024-088
Carrier: Pacific Ocean Lines
Booking Reference: POL-BK-{timestamp}

ROUTING:
========
Port of Loading: Shanghai Port (CNSHA)
Loading Date: 2024-01-10
Port of Discharge: Los Angeles Port (USLAX)
ETA: 2024-02-15

CONTAINER DETAILS:
==================
Container 1: POLU1234567 (Seal: SL789012)
Container 2: POLU2345678 (Seal: SL890123)
Type: 40' High Cube

CARGO SUMMARY:
==============
1. WIDGET-A-1000: Electronic Widget Type A
   - Ordered: 1,000 units
   - Shipped: 1,000 units
   - Condition: good

2. WIDGET-B-2000: Industrial Widget Type B  
   - Ordered: 500 units
   - Shipped: 500 units
   - Condition: good

3. COMP-C-3000: Component C for Assembly
   - Ordered: 2,000 units
   - Shipped: 1,950 units (50 units short - will follow in next shipment)
   - Condition: good

SPECIAL INSTRUCTIONS:
====================
- Handle with care - contains fragile electronic components
- Temperature controlled storage required (15-25°C)
- Notify consignee 48 hours before arrival

DOCUMENTS ATTACHED:
==================
- Original Bill of Lading (PDF)
- Packing List
- Commercial Invoice
- Certificate of Origin

For tracking updates, please visit: https://tracking.pacificoceanlines.com/track/{bol_number}

Should you have any questions, please don't hesitate to contact us.

Best regards,
Global Manufacturing Ltd.
Shanghai, China
Tel: +86-21-5555-0000
Email: <EMAIL>
"""

    # Create email thread
    email_thread = EmailThread.objects.create(
        team=team,
        thread_id=f"thread-shipping-{unique_id}",
    )

    # Create email
    email = Communication.objects.create(
        team=team,
        supplier=supplier,
        email_subject=f"Shipment Delivered - BOL {bol_number} - PO {po_number}",
        email_content=shipping_content,
        email_from="<EMAIL>",
        email_message_id=f"<shipping-{unique_id}@testshipping.com>",
        direction=Communication.DIRECTION_INCOMING,
        comm_type=Communication.TYPE_EMAIL,
        comm_time=timezone.now(),
        email_thread=email_thread,
    )

    CommunicationEmailRecipient.objects.create(
        email_address="<EMAIL>", communication_to=email
    )

    # Create and attach PDF document
    pdf_content = create_test_shipping_document_pdf()

    document = Document.objects.create(
        name=f"{bol_number}.pdf",
        document=SimpleUploadedFile(
            f"{bol_number}.pdf", pdf_content, content_type="application/pdf"
        ),
        team=team,
        doc_type=DocumentType.SHIPPING_DOCUMENT.value,
        content_type="application/pdf",
        filesize_bytes=len(pdf_content),
    )

    # Link document to email
    DocumentLink.objects.create(
        document=document,
        parent_object=email,
    )

    logger.info(f"Created test shipping email: {email.id}")
    logger.info(f"  Subject: {email.email_subject}")
    logger.info(f"  BOL Number: {bol_number}")
    logger.info(f"  PO Number: {po_number}")
    logger.info(f"  Attached PDF: {document.id} ({len(pdf_content)} bytes)")

    return email


def test_shipping_document_workflow(team_id: str):
    """Test the complete shipping document workflow."""
    try:
        team = Team.objects.get(id=team_id)
        logger.info(f"Testing shipping document workflow for team: {team.name}")

        # Check if workflow exists
        workflow = UserWorkflow.objects.filter(
            workflow_type=WorkflowType.SHIPPING_DOCUMENT_PROCESSING.value,
            trigger=WorkflowTrigger.ON_SHIPPING_DOCUMENT_EMAIL_RECEIVED.value,
            team=team,
        ).first()

        if not workflow:
            logger.error(
                "❌ No Shipping Document Processing workflow found for this team."
            )
            logger.info("Create the workflow through:")
            logger.info(
                "  1. Django Admin → Users → Teams → Select team → 'Setup Workflows' button"
            )
            logger.info("  2. Check 'Shipping Document Processing workflow'")
            logger.info("  3. Configure validation settings as needed")
            logger.info("  4. Save the configuration")
            return False

        logger.info(f"Found workflow: {workflow.id}")

        # Step 1: Create test shipping email with attachment
        logger.info("📧 Creating test shipping document email...")
        email = create_test_shipping_email_with_attachment(team_id)

        # Step 2: Trigger workflow
        logger.info("🚀 Triggering shipping document processing workflow...")
        context = EmailWorkflowContext(email=email)
        result = trigger_workflow_if_exists_with_status(
            WorkflowType.SHIPPING_DOCUMENT_PROCESSING.value,
            WorkflowTrigger.ON_SHIPPING_DOCUMENT_EMAIL_RECEIVED.value,
            team,
            context_object=context,
        )

        if not result.triggered:
            logger.error(f"❌ Failed to trigger workflow: {result.error_message}")
            return False

        logger.info("✅ Workflow triggered successfully!")
        logger.info(f"  Temporal ID: {result.temporal_id}")

        # Step 3: Wait for workflow completion and check results
        logger.info("⏳ Waiting for workflow to complete...")
        max_wait = 60  # seconds
        shipping_document = None

        for i in range(max_wait):
            time.sleep(2)

            # Check if ShippingDocument was created
            shipping_documents = ShippingDocument.objects.filter(
                team=team,
            ).order_by("-created_at")

            if shipping_documents.exists():
                # Find the one created from our email
                for doc in shipping_documents:
                    # Check if this document was created recently (within last 2 minutes)
                    time_diff = timezone.now() - doc.created_at
                    if time_diff.total_seconds() < 120:  # 2 minutes
                        shipping_document = doc
                        break

                if shipping_document:
                    logger.info(f"📋 Found shipping document: {shipping_document.id}")
                    break

            logger.info(f"  Waiting... ({i+1}/{max_wait})")

        if not shipping_document:
            logger.error("❌ No shipping document was created within timeout")
            logger.info("Check the workflow logs for errors")
            return False

        # Step 4: Verify the extracted data
        logger.info("🔍 Verifying extracted data...")
        logger.info(f"  Document Type: {shipping_document.document_type}")
        logger.info(f"  Reference Number: {shipping_document.reference_number}")
        logger.info(f"  Carrier: {shipping_document.carrier_name}")
        logger.info(f"  Tracking Number: {shipping_document.tracking_number}")
        logger.info(f"  Status: {shipping_document.status}")
        logger.info(f"  Purchase Order: {shipping_document.purchase_order}")

        # Check shipping items
        items = ShippingItem.objects.filter(shipping_document=shipping_document)
        logger.info(f"  Shipping Items: {items.count()}")

        for item in items:
            logger.info(
                f"    - Quantity: {item.received_quantity}, Condition: {item.condition}"
            )
            logger.info(f"      Notes: {item.notes}")

        # Step 5: Verify workflow completed successfully
        logger.info("✅ Shipping document workflow test completed!")
        logger.info("📊 Summary:")
        logger.info(f"  - Email processed: {email.id}")
        logger.info(f"  - Shipping document created: {shipping_document.id}")
        logger.info(f"  - Shipping items created: {items.count()}")
        logger.info(f"  - Document type: {shipping_document.document_type}")
        logger.info(f"  - Status: {shipping_document.status}")

        return True

    except Exception as e:
        logger.error(
            f"Error during shipping document workflow test: {str(e)}", exc_info=True
        )
        return False


def main():
    """Main function."""
    print("\n=== Shipping Document Processing Workflow Test ===\n")

    # Test with team 1
    success = test_shipping_document_workflow(team_id="1")

    if success:
        print("\n✅ Shipping document workflow test completed successfully!")
        print(
            "The workflow extracted data from the email and created shipping records."
        )
    else:
        print("\n❌ Shipping document workflow test failed!")
        print("Check the error messages above for details.")

    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
