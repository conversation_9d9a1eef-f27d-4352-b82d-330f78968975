#!/bin/bash

# Usage: source ./scripts/access_aws_container.sh --prod|--staging

# Function to handle script logic
access_aws_container() {

    # Default environment is staging
    ENVIRONMENT="staging"
    # Both our staging and prod environments are on the prod AWS account
    PROFILE="didero-prod"

    # Parse command-line arguments
    if [ "$1" == "--prod" ]; then
        ENVIRONMENT="prod"
        echo "[INFO] Environment set to production."
    elif [ "$1" == "--staging" ]; then
        ENVIRONMENT="staging"
        echo "[INFO] Environment set to staging."
    else
        echo "[ERROR] Invalid argument."
        echo "Usage: $0 --prod|--staging"
        # Use 'return' if sourced, 'exit' if executed
        if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
            return 1
        else
            exit 1
        fi
    fi

    # Set variables based on the environment
    if [ "$ENVIRONMENT" == "prod" ]; then
        CLUSTER="didero-prod-cluster"
        SERVICE="didero-service-prod"
    else
        CLUSTER="didero-staging-cluster"
        SERVICE="didero-service-staging"
    fi

    echo "[INFO] Using cluster: $CLUSTER"
    echo "[INFO] Using service: $SERVICE"
    echo "[INFO] Using AWS profile: $PROFILE"

    # Retrieve the Task ARN
    echo "[INFO] Retrieving the task ARN..."
    TASK_ARN=$(aws ecs list-tasks \
        --cluster "$CLUSTER" \
        --service-name "$SERVICE" \
        --profile "$PROFILE" \
        --query 'taskArns[0]' \
        --output text)

    # Check if TASK_ARN is empty or "None"
    if [ -z "$TASK_ARN" ] || [ "$TASK_ARN" == "None" ]; then
        echo "[ERROR] No tasks found for service '$SERVICE' in cluster '$CLUSTER'."
        # Use 'return' if sourced, 'exit' if executed
        if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
            return 1
        else
            exit 1
        fi
    else
        echo "[INFO] Task ARN retrieved: $TASK_ARN"
    fi

    # Execute the command to access the container
    echo "[INFO] Executing command to access the container..."
    aws ecs execute-command \
        --cluster "$CLUSTER" \
        --task "$TASK_ARN" \
        --container didero-api \
        --command "/bin/bash" \
        --interactive \
        --profile "$PROFILE"

    if [ $? -eq 0 ]; then
        echo "[INFO] Successfully exited the container."
    else
        echo "[ERROR] Failed to execute command within the container."
        # Use 'return' if sourced, 'exit' if executed
        if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
            return 1
        else
            exit 1
        fi
    fi
}

# Call the function with provided arguments
access_aws_container "$@"
