#!/bin/bash

# Script: get_prod_staging_diff.sh
# Usage: ./get_prod_staging_diff.sh [staging_commit_hash]
#
# 1. Retrieves the currently deployed production commit hash from ECS.
# 2. Compares it to the provided staging commit hash (or fetches if not provided).
# 3. Prints a formatted git log of the changes that will be deployed.

PROFILE="didero-deploy"
TASK_DEF_NAME="didero-app-prod-definition"

STAGING_COMMIT_HASH="$1"

if [ -z "$STAGING_COMMIT_HASH" ]; then
    echo "[INFO] No staging commit hash provided. Fetching the latest staging commit..."
    STAGING_COMMIT_HASH=$(./scripts/deploy/get_latest_staging_commit.sh)
    if [ -z "$STAGING_COMMIT_HASH" ]; then
        echo "[ERROR] Failed to retrieve staging commit hash."
        exit 1
    fi
fi

get_prod_commit_hash() {
    echo "[INFO] Fetching the current production task definition..."
    CURRENT_TASK_DEF=$(aws ecs describe-task-definition \
        --task-definition ${TASK_DEF_NAME} \
        --query 'taskDefinition' \
        --profile $PROFILE 2>&1)
    EXIT_CODE=$?

    if [ $EXIT_CODE -ne 0 ]; then
        echo "[DEBUG] aws ecs describe-task-definition output:"
        echo "$CURRENT_TASK_DEF"

        # Check for SSO errors (expired token, missing token, etc.)
        if echo "$CURRENT_TASK_DEF" | grep -E "Token has expired and refresh failed|Error loading SSO Token"; then
            echo "[WARN] SSO token issue detected. Initiating re-login..."
            aws sso login --profile $PROFILE
            if [ $? -ne 0 ]; then
                echo "[ERROR] SSO login failed. Please run 'aws sso login --profile $PROFILE' manually."
                exit 1
            fi

            echo "[INFO] Retrying task definition retrieval..."
            CURRENT_TASK_DEF=$(aws ecs describe-task-definition \
                --task-definition ${TASK_DEF_NAME} \
                --query 'taskDefinition' \
                --profile $PROFILE 2>&1)
            EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
                echo "[DEBUG] Second attempt output:"
                echo "$CURRENT_TASK_DEF"
                if echo "$CURRENT_TASK_DEF" | grep -E "Token has expired and refresh failed|Error loading SSO Token"; then
                    echo "[ERROR] Failed to refresh SSO token. Please run 'aws sso login --profile $PROFILE' manually and try again."
                else
                    echo "[ERROR] Unexpected error after SSO login: $CURRENT_TASK_DEF"
                fi
                exit 1
            fi
        else
            # Some other error occurred
            echo "[ERROR] Unexpected error retrieving task definition: $CURRENT_TASK_DEF"
            exit 1
        fi
    fi


    API_IMAGE=$(echo "$CURRENT_TASK_DEF" | jq -r '.containerDefinitions[0].image')
    PROD_COMMIT_HASH=$(echo "$API_IMAGE" | awk -F: '{print $2}')

    if [ -z "$PROD_COMMIT_HASH" ] || [ "$PROD_COMMIT_HASH" == "null" ]; then
        echo "[ERROR] Unable to retrieve the production commit hash."
        exit 1
    else
        echo "[INFO] Production commit hash: $PROD_COMMIT_HASH"
    fi
}

get_prod_commit_hash

echo "[INFO] Generating git log from $PROD_COMMIT_HASH to $STAGING_COMMIT_HASH..."
GIT_LOG=$(git log --pretty=format:"%an    %s" $PROD_COMMIT_HASH..$STAGING_COMMIT_HASH 2>&1)
EXIT_CODE=$?

if [ $EXIT_CODE -ne 0 ]; then
    echo "[ERROR] git log command failed: $GIT_LOG"
    exit 1
fi

if [ -z "$GIT_LOG" ]; then
    echo "[INFO] No new commits between production ($PROD_COMMIT_HASH) and staging ($STAGING_COMMIT_HASH)."
    # This is not an error; just means nothing to deploy.
    exit 0
fi

echo ""
echo "=================== Paste the following in Slack ==================="
echo ""
echo "Deploying the following changes from staging ($STAGING_COMMIT_HASH) to production ($PROD_COMMIT_HASH):"
echo ""
echo "$GIT_LOG"
echo ""
echo "===================================================================="
