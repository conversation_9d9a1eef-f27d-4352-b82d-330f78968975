#!/bin/bash

# Script: generate-task-definition.sh
# Usage: ./generate-task-definition.sh <environment> <git_hash> [output_file]
#
# Generates an ECS task definition from template with proper image tags

set -e

if [ -z "$1" ] || [ -z "$2" ]; then
    echo "[ERROR] Missing required arguments."
    echo "Usage: $0 <environment> <git_hash> [output_file]"
    echo "Example: $0 staging abc123def task-def.json"
    exit 1
fi

ENVIRONMENT="$1"
GIT_HASH="$2"
OUTPUT_FILE="${3:-task-definition.json}"

# Set environment-specific values
if [ "$ENVIRONMENT" == "production" ] || [ "$ENVIRONMENT" == "prod" ]; then
    TASK_FAMILY="didero-app-prod-definition"
    ENV_FILE_ARN="arn:aws:s3:::didero-env-files/docker.prod.env"
    ENVIRONMENT_NAME="production"
    AWS_ACCOUNT="************"
elif [ "$ENVIRONMENT" == "staging" ]; then
    TASK_FAMILY="didero-app-staging-definition"
    ENV_FILE_ARN="arn:aws:s3:::didero-env-files/docker.staging.env"
    ENVIRONMENT_NAME="staging"
    AWS_ACCOUNT="************"
else
    echo "[ERROR] Unknown environment: $ENVIRONMENT"
    echo "Valid environments: staging, production (or prod)"
    exit 1
fi

# Common values
AWS_REGION="us-east-2"
ECR_REPO="${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com"
TASK_ROLE_ARN="arn:aws:iam::${AWS_ACCOUNT}:role/ecsTaskExecutionRole"
EXECUTION_ROLE_ARN="arn:aws:iam::${AWS_ACCOUNT}:role/ecsTaskExecutionRole"

# Image URLs
API_IMAGE="${ECR_REPO}/didero-api:${GIT_HASH}"
WORKER_IMAGE="${ECR_REPO}/didero-worker:${GIT_HASH}"
EMAIL_CONSUMER_IMAGE="${ECR_REPO}/email-consumer:${GIT_HASH}"
TEMPORAL_WORKER_IMAGE="${ECR_REPO}/temporal-worker:${GIT_HASH}"

# Path to template
TEMPLATE_FILE="$(dirname "$0")/../../ecs/task-definitions/didero-app-template.json"

if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "[ERROR] Template file not found: $TEMPLATE_FILE"
    exit 1
fi

echo "[INFO] Generating task definition for $ENVIRONMENT_NAME environment..."
echo "[INFO] Using git hash: $GIT_HASH"

# Generate task definition by replacing placeholders
sed -e "s|\${TASK_FAMILY}|${TASK_FAMILY}|g" \
    -e "s|\${TASK_ROLE_ARN}|${TASK_ROLE_ARN}|g" \
    -e "s|\${EXECUTION_ROLE_ARN}|${EXECUTION_ROLE_ARN}|g" \
    -e "s|\${API_IMAGE}|${API_IMAGE}|g" \
    -e "s|\${WORKER_IMAGE}|${WORKER_IMAGE}|g" \
    -e "s|\${EMAIL_CONSUMER_IMAGE}|${EMAIL_CONSUMER_IMAGE}|g" \
    -e "s|\${TEMPORAL_WORKER_IMAGE}|${TEMPORAL_WORKER_IMAGE}|g" \
    -e "s|\${ENV_FILE_ARN}|${ENV_FILE_ARN}|g" \
    -e "s|\${ENVIRONMENT}|${ENVIRONMENT_NAME}|g" \
    -e "s|\${AWS_REGION}|${AWS_REGION}|g" \
    "$TEMPLATE_FILE" > "$OUTPUT_FILE"

echo "[INFO] Task definition generated: $OUTPUT_FILE"

# Validate JSON
if command -v jq &> /dev/null; then
    jq . "$OUTPUT_FILE" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "[INFO] JSON validation passed"
    else
        echo "[ERROR] Generated task definition is not valid JSON"
        exit 1
    fi
else
    echo "[WARNING] jq not installed, skipping JSON validation"
fi