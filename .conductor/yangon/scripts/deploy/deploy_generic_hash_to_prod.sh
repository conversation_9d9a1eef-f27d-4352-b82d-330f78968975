#!/bin/bash

# Script: deploy_generic_hash_to_prod.sh
# Usage: ./deploy_generic_hash_to_prod.sh <git_hash>
#
# Deploys the provided git hash to production using versioned task definitions.

set -e

if [ -z "$1" ]; then
    echo "[ERROR] No git hash provided."
    echo "Usage: $0 <git_hash>"
    exit 1
fi

GIT_HASH="$1"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GENERATE_SCRIPT="$SCRIPT_DIR/generate-task-definition.sh"

# Configuration
CLUSTER_NAME="didero-prod-cluster"
SERVICE_NAME="didero-service-prod"
PROFILE="didero-deploy"
ENVIRONMENT="production"

# Verify if the specified docker tag (commit hash) exists in the ECR repository
echo "[INFO] Verifying docker images exist for hash: $GIT_HASH"
if ! aws ecr describe-images --repository-name didero-api --image-ids imageTag=$GIT_HASH --profile $PROFILE >/dev/null 2>&1; then
    echo "[ERROR] Docker tag $GIT_HASH does not exist in the ECR repository."
    exit 1
fi

# Generate task definition from template
TEMP_TASK_DEF="/tmp/task-definition-${GIT_HASH}.json"
echo "[INFO] Generating task definition from template..."
$GENERATE_SCRIPT "$ENVIRONMENT" "$GIT_HASH" "$TEMP_TASK_DEF"

if [ ! -f "$TEMP_TASK_DEF" ]; then
    echo "[ERROR] Failed to generate task definition"
    exit 1
fi

# Display what will be deployed
echo "[INFO] Promoting these packages to production:"
grep -E '"image":|"name":' "$TEMP_TASK_DEF" | grep -B1 '"image"' | grep -E 'didero-api|didero-worker|email-consumer|temporal-worker' | head -20

# Register the new task definition
echo "[INFO] Registering new task definition..."
NEW_TASK_DEF=$(aws ecs register-task-definition \
    --cli-input-json file://"$TEMP_TASK_DEF" \
    --profile $PROFILE 2>&1)
EXIT_CODE=$?

if [ $EXIT_CODE -ne 0 ]; then
    echo "[ERROR] Failed to register new task definition: $NEW_TASK_DEF"
    rm -f "$TEMP_TASK_DEF"
    exit 1
fi

NEW_TASK_DEF_ARN=$(echo $NEW_TASK_DEF | jq -r '.taskDefinition.taskDefinitionArn')

if [ -z "$NEW_TASK_DEF_ARN" ] || [ "$NEW_TASK_DEF_ARN" == "null" ]; then
    echo "[ERROR] Could not extract new task definition ARN."
    rm -f "$TEMP_TASK_DEF"
    exit 1
fi

echo "[INFO] New task definition registered: $NEW_TASK_DEF_ARN"

# Update ECS service to use the new task definition
echo "[INFO] Updating ECS service to use the new task definition..."
UPDATE_SERVICE_OUTPUT=$(aws ecs update-service \
    --cluster $CLUSTER_NAME \
    --service $SERVICE_NAME \
    --task-definition $NEW_TASK_DEF_ARN \
    --profile $PROFILE 2>&1)
EXIT_CODE=$?

if [ $EXIT_CODE -ne 0 ]; then
    echo "[ERROR] Failed to update ECS service: $UPDATE_SERVICE_OUTPUT"
    rm -f "$TEMP_TASK_DEF"
    exit 1
fi

# Clean up temp file
rm -f "$TEMP_TASK_DEF"

echo "[INFO] Waiting for the service to stabilize..."
aws ecs wait services-stable \
    --cluster $CLUSTER_NAME \
    --services $SERVICE_NAME \
    --profile $PROFILE

echo "[INFO] ECS service updated successfully with new task definition: $NEW_TASK_DEF_ARN"