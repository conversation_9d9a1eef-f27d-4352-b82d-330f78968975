#!/bin/bash

# Script: get_latest_staging_commit.sh
# Usage: ./get_latest_staging_commit.sh
#
# This script retrieves the latest commit hash from the remote staging branch
# without affecting the local repository state.


REMOTE="origin"
STAGING_BRANCH="staging"

LATEST_COMMIT_HASH=$(git ls-remote $REMOTE refs/heads/$STAGING_BRANCH | awk '{print $1}')
if [ -z "$LATEST_COMMIT_HASH" ]; then
    echo "[ERROR] Unable to retrieve the latest commit hash from $REMOTE/$STAGING_BRANCH."
    exit 1
else
    # Only print the commit hash here.
    echo "$LATEST_COMMIT_HASH"
fi