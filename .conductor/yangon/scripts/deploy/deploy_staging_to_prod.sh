#!/bin/bash

# Script: deploy_staging_to_prod.sh
# Usage: ./deploy_staging_to_prod.sh
#
# Uses the deployment script that generates task definitions from templates
#
# Steps:
# 1. Get the latest staging commit hash.
# 2. Run get_prod_staging_diff.sh with that commit hash to show changes.
# 3. Prompt the user if they've posted to Slack and want to proceed with the deploy.
# 4. If yes, call deploy_generic_hash_to_prod.sh with the staging commit hash.

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 1. Get latest staging commit hash
LATEST_STAGING_HASH=$($SCRIPT_DIR/get_latest_staging_commit.sh)
# If this fails, script exits due to set -e
echo "[INFO] Latest staging hash: $LATEST_STAGING_HASH"

# 2. Show diff between prod and this staging commit
$SCRIPT_DIR/get_prod_staging_diff.sh "$LATEST_STAGING_HASH"
# If this fails, script exits due to set -e

echo ""
echo "Have you posted these changes to Slack and want to proceed with the deploy? (yes/no)"
read USER_CONFIRMATION

if [ "$USER_CONFIRMATION" == "yes" ]; then
    echo "[INFO] Proceeding with deploy using task definition from template..."
    $SCRIPT_DIR/deploy_generic_hash_to_prod.sh "$LATEST_STAGING_HASH"
    # If this fails, script exits due to set -e
else
    echo "[INFO] Deployment cancelled by user."
fi