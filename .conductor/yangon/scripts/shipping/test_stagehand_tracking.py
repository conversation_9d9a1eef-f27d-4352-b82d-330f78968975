#!/usr/bin/env python
"""
End-to-end test script for async Stagehand-based carrier (ZIM, ONE, MSC, COSCO) shipment tracking.

This script simulates the production async tracking workflow:
1. Creates a CSV file with carrier tracking codes (as BOL numbers)
2. Imports the CSV to create shipments
3. Calls update_pending_shipments to schedule async Stagehand jobs
4. Monitors ShipmentSync progress with real-time updates
5. Periodically calls check_stagehand_sync_results to check for completed jobs
6. Exports the results to CSV
7. Analyzes the results including sync statistics

The script shows:
- Real-time progress of async jobs (in progress, completed, failed)
- Individual shipment completion/failure notifications
- Sync job statistics (success rate, average duration)
- Shipment update statistics (ETAs, ports, delivery status)

Usage:
    # Test all carriers in full mode
    DJANGO_SETTINGS_MODULE=didero.settings.common uv run python scripts/shipping/test_stagehand_tracking.py

    # Quick mode (1-2 codes per carrier)
    DJANGO_SETTINGS_MODULE=didero.settings.common uv run python scripts/shipping/test_stagehand_tracking.py --quick

    # Clean up CSV files
    DJANGO_SETTINGS_MODULE=didero.settings.common uv run python scripts/shipping/test_stagehand_tracking.py --cleanup-csv
"""

import argparse
import csv
import glob
import io
import os
import signal
import sys
import time
from collections import defaultdict
from datetime import datetime, timedelta

# Add the project directory to the Python path
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.insert(0, project_root)

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

import django

django.setup()

from django.utils import timezone

from didero.orders.models import Shipment, ShipmentSync
from didero.orders.schemas import ShipmentStatus, ShipmentSyncStatus
from didero.shipments.models import import_shipments_from_csv
from didero.shipments.serializers import ShipmentExportSerializer
from didero.shipments.tasks import (
    check_stagehand_sync_results,
    update_pending_shipments,
)
from didero.users.models import Team

# Carrier-specific test data (limited to ~10 tracking codes per carrier)
CARRIER_TEST_DATA = {
    "zim": {
        "name": "ZIM",
        "tracking_codes": [
            "ZIMUDMT800244171",
            "ZIMUDMT80024418",
            "ZIMUDMT80024702",
            "ZIMUDMT800248661",
            "ZIMUDMT80024867",
            "ZIMUHAI80148056",
            "ZIMUHAI80152969",
            "ZIMUHCM80532497",
            "ZIMUPKH003126085",
            "ZIMUSHH31725685",
        ],
    },
    "one": {
        "name": "ONE",
        "tracking_codes": [
            "ONEYDACF09757700",
            "ONEYDACF09757701",
            "ONEYDACF09757702",
            "ONEYDACF09758800",
            "ONEYDACF09758801",
            "ONEYDACF09758802",
            "ONEYDACF10202500",
            "ONEYDACF10202501",
            "ONEYDACF10202502",
            "ONEYDACF10804600",
        ],
    },
    "msc": {
        "name": "MSC",
        "tracking_codes": [
            "MEDUEV908912",
            "MEDUEV955319",
            "MEDUEV955343",
            "MEDUEV955350",
            "MEDUEV957000",
            "MEDUEV963289",
            "MEDUEV985241",
            "MEDUEV985274",
            "MEDUEV985282",
            "MEDUEV985290",
        ],
    },
    "cosco": {
        "name": "COSCO",
        "tracking_codes": [
            "COSU6417229150",
            "COSU6417354860",
            "COSU6417949420",
            "COSU6418142730",
            "COSU6418279380",
            "COSU6418403800",
            "COSU6418528550",
            "COSU6418531370",
            "COSU6418639930",
            "COSU6419088550",
        ],
    },
    "evergreen": {
        "name": "EVERGREEN",
        "tracking_codes": [
            "EGLV082500083679",
            "EGLV082500092139",
            "EGLV142501249080",
            "EGLV142501348291",
            "EGLV142501348959",
            "EGLV142501351411",
            "EGLV142501378394",
            "EGLV142501378408",
            "EGLV142501378416",
            "EGLV142501378424",
        ],
    },
}

terminate_flag = False
cleanup_tracking_codes = {}  # Dict of carrier -> tracking codes
cleanup_team = None


def signal_handler(signum, frame):
    """Handle termination signals gracefully."""
    global terminate_flag, cleanup_tracking_codes, cleanup_team
    print("\n\n⚠️  Received termination signal. Cleaning up...")
    terminate_flag = True

    # Perform cleanup if we have test data
    if cleanup_team and cleanup_tracking_codes:
        try:
            total_deleted = 0
            for carrier, tracking_codes in cleanup_tracking_codes.items():
                if tracking_codes:
                    deleted = Shipment.objects.filter(
                        team=cleanup_team, bol_number__in=tracking_codes
                    ).delete()[0]
                    total_deleted += deleted
            print(f"   Deleted {total_deleted} test shipments")
        except Exception as e:
            print(f"   Error during cleanup: {e}")

    print("   Exiting...")
    sys.exit(0)


def generate_multi_carrier_csv_content(carriers_data):
    """Generate CSV content for importing multiple carriers' shipments.

    Args:
        carriers_data: List of tuples (carrier_type, tracking_codes)
    """
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(
        [
            "tracking_number",
            "bol_number",
            "container_number",
            "carrier_type",
            "shipment_date",
            "estimated_delivery_date",
            "actual_delivery_date",
            "port_of_departure",
            "port_of_arrival",
        ]
    )

    # Write data for all carriers
    for carrier_type, tracking_codes in carriers_data:
        for code in tracking_codes:
            writer.writerow(
                [
                    "",  # tracking_number
                    code,  # bol_number (for ocean carriers)
                    "",  # container_number
                    carrier_type.upper(),  # carrier_type
                    "",  # shipment_date
                    "",  # estimated_delivery_date
                    "",  # actual_delivery_date
                    "",  # port_of_departure
                    "",  # port_of_arrival
                ]
            )

    return output.getvalue()


def export_shipments_to_csv(shipments):
    """Export shipments to CSV format."""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(
        [
            "tracking_number",
            "bol_number",
            "container_number",
            "carrier_type",
            "shipment_date",
            "estimated_delivery_date",
            "actual_delivery_date",
            "port_of_departure",
            "port_of_arrival",
            "last_successful_sync_at",
        ]
    )

    # Write data rows
    for shipment in shipments:
        serializer = ShipmentExportSerializer(shipment)
        data = serializer.data
        writer.writerow(
            [
                data.get("tracking_number", ""),
                data.get("bol_number", ""),
                data.get("container_number", ""),
                data.get("carrier_type", ""),
                data.get("shipment_date", ""),
                data.get("estimated_delivery_date", ""),
                data.get("actual_delivery_date", ""),
                data.get("port_of_departure", ""),
                data.get("port_of_arrival", ""),
                data.get("last_successful_sync_at", ""),
            ]
        )

    return output.getvalue()


def analyze_results(initial_shipments, final_shipments):
    """Analyze the differences between initial and final shipment states."""
    results = {
        "total": len(final_shipments),
        "updated": 0,
        "with_eta": 0,
        "with_ports": 0,
        "delivered": 0,
        "failed": 0,
        "by_status": defaultdict(int),
    }

    # Create lookup for initial state
    initial_state = {s.id: s for s in initial_shipments}

    for shipment in final_shipments:
        initial = initial_state.get(shipment.id)

        # Count by status
        results["by_status"][shipment.status] += 1

        # Check if updated
        if initial and (
            shipment.estimated_delivery_date != initial.estimated_delivery_date
            or shipment.actual_delivery_date != initial.actual_delivery_date
            or shipment.metadata.get("port_of_departure")
            != initial.metadata.get("port_of_departure")
            or shipment.metadata.get("port_of_arrival")
            != initial.metadata.get("port_of_arrival")
            or shipment.status != initial.status
        ):
            results["updated"] += 1

        # Check specific fields
        if shipment.estimated_delivery_date:
            results["with_eta"] += 1

        if shipment.metadata and (
            shipment.metadata.get("port_of_departure")
            or shipment.metadata.get("port_of_arrival")
        ):
            results["with_ports"] += 1

        if shipment.status == ShipmentStatus.RECEIVED.value:
            results["delivered"] += 1

    results["failed"] = results["total"] - results["updated"]

    return results


def run_all_carriers_test(quick_mode=False):
    """Run tests for all slow carriers."""
    global cleanup_tracking_codes, cleanup_team

    # Get team
    team = Team.objects.first()
    if not team:
        print("❌ No team found in database. Please create a team first.")
        return

    cleanup_team = team

    # Define which carriers to test (slow carriers only)
    carriers_to_test = ["zim", "one", "msc", "cosco", "evergreen"]

    print("🚀 Starting multi-carrier tracking test")
    print(f"   Testing carriers: {', '.join([c.upper() for c in carriers_to_test])}")
    print(
        f"   Mode: {'QUICK (1-2 codes per carrier)' if quick_mode else 'FULL (up to 10 codes per carrier)'}"
    )
    print(f"   Using team: {team.name}")
    print("")

    # Collect all carrier data for CSV generation
    carriers_data = []
    all_tracking_codes = []

    for carrier_type in carriers_to_test:
        carrier_info = CARRIER_TEST_DATA[carrier_type]
        tracking_codes = carrier_info["tracking_codes"]

        # In quick mode, only use first 2 tracking codes
        if quick_mode:
            tracking_codes = tracking_codes[:2]

        carriers_data.append((carrier_type, tracking_codes))
        all_tracking_codes.extend(tracking_codes)
        cleanup_tracking_codes[carrier_type] = tracking_codes

        print(f"   {carrier_info['name']}: {len(tracking_codes)} tracking codes")

    # Clean up any existing test shipments
    print("\n🧹 Cleaning up any existing test shipments...")
    deleted = Shipment.objects.filter(
        team=team, bol_number__in=all_tracking_codes
    ).delete()[0]
    if deleted:
        print(f"   Deleted {deleted} existing test shipments")

    try:
        # Step 1: Generate single CSV for all carriers
        print(
            f"\n📝 Generating CSV with {len(all_tracking_codes)} tracking codes across {len(carriers_to_test)} carriers..."
        )
        csv_content = generate_multi_carrier_csv_content(carriers_data)

        # Save CSV for reference
        csv_filename = (
            f"stagehand_carriers_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )
        with open(csv_filename, "w") as f:
            f.write(csv_content)
        print(f"   Saved import CSV as: {csv_filename}")

        # Step 2: Import CSV
        print("\n📥 Importing CSV to create shipments...")
        import_results = import_shipments_from_csv(csv_content, team)
        print(f"   Created: {import_results.get('created', 0)}")
        print(f"   Updated: {import_results.get('updated', 0)}")
        print(f"   Errors: {len(import_results.get('errors', []))}")

        if import_results.get("errors"):
            print("\n   Import errors:")
            for error in import_results["errors"][:5]:  # Show first 5 errors
                print(f"     Row {error['row']}: {error['errors']}")

        # Get all created shipments
        shipments = list(
            Shipment.objects.filter(
                team=team, bol_number__in=all_tracking_codes
            ).order_by("id")
        )

        if not shipments:
            print("\n❌ No shipments were created. Check the import errors above.")
            return

        print(f"\n✅ Successfully created {len(shipments)} shipments")

        # Store initial state
        initial_shipments = []
        for s in shipments:
            initial_shipments.append(Shipment.objects.get(pk=s.pk))

        # Step 3: Schedule async tracking jobs
        print("\n🚀 Scheduling async tracking jobs for all carriers...")
        print(f"   Total shipments to process: {len(shipments)}")

        # Clear any existing syncs for these shipments
        ShipmentSync.objects.filter(shipment__in=shipments).delete()

        print("   Calling update_pending_shipments to schedule jobs...")

        try:
            # Schedule async jobs
            update_pending_shipments()
            print("   ✅ Jobs scheduled successfully")

            # Wait for jobs to be submitted to Stagehand
            print("   ⏳ Waiting 5 seconds for jobs to be submitted...")
            time.sleep(5)

        except Exception as e:
            print(f"\n   ❌ Failed to schedule jobs: {e}")
            import traceback

            traceback.print_exc()
            return

        # Step 4: Monitor progress and check for results
        print("\n📊 Monitoring async job progress...")
        print("   Checking for completed jobs every minute...")
        print("   (Press Ctrl+C to stop monitoring)\n")

        start_time = time.time()
        timeout_minutes = 20
        check_interval = 60  # seconds
        last_check_time = 0

        # Progress tracking
        total_syncs = 0
        last_in_progress = -1
        last_success = 0
        last_failed = 0

        while True:
            # Check sync status
            syncs = ShipmentSync.objects.filter(shipment__in=shipments).select_related(
                "shipment"
            )

            total_syncs = syncs.count()
            in_progress = syncs.filter(
                status=ShipmentSyncStatus.IN_PROGRESS.value
            ).count()
            success = syncs.filter(status=ShipmentSyncStatus.SUCCESS.value).count()
            failed = syncs.filter(status=ShipmentSyncStatus.FAILED.value).count()

            # Display progress
            elapsed = time.time() - start_time
            progress_msg = (
                f"\r⏳ [{int(elapsed/60)}:{int(elapsed%60):02d}] "
                f"Progress: {in_progress} in progress, "
                f"{success} completed, {failed} failed "
                f"(Total: {total_syncs}/{len(shipments)})"
            )
            print(progress_msg, end="", flush=True)

            # Log changes
            if (
                in_progress != last_in_progress
                or success != last_success
                or failed != last_failed
            ):
                print()  # New line for status changes

                # Show recently completed syncs
                if success > last_success:
                    recent_success = syncs.filter(
                        status=ShipmentSyncStatus.SUCCESS.value,
                        completed_at__gte=timezone.now()
                        - timedelta(seconds=check_interval + 10),
                    )
                    for sync in recent_success[:5]:  # Show max 5
                        print(
                            f"   ✅ Completed: {sync.shipment.carrier_type} - {sync.shipment.bol_number}"
                        )

                # Show recently failed syncs
                if failed > last_failed:
                    recent_failed = syncs.filter(
                        status=ShipmentSyncStatus.FAILED.value,
                        completed_at__gte=timezone.now()
                        - timedelta(seconds=check_interval + 10),
                    )
                    for sync in recent_failed[:5]:  # Show max 5
                        print(
                            f"   ❌ Failed: {sync.shipment.carrier_type} - {sync.shipment.bol_number}: {sync.error_message}"
                        )

                last_in_progress = in_progress
                last_success = success
                last_failed = failed

            # Check if all done
            if in_progress == 0 and total_syncs >= len(shipments):
                print(
                    f"\n\n✅ All jobs completed! Success: {success}, Failed: {failed}"
                )
                break

            # Check timeout
            if elapsed > timeout_minutes * 60:
                print(f"\n\n⚠️  Timeout reached after {timeout_minutes} minutes")
                print(f"   Still {in_progress} jobs in progress")

                # Ask if user wants to continue
                try:
                    response = input("   Continue monitoring? (y/N): ").strip().lower()
                    if response == "y":
                        timeout_minutes += 10  # Add 10 more minutes
                        print(f"   Extended timeout to {timeout_minutes} minutes")
                        continue
                except KeyboardInterrupt:
                    pass

                break

            # Run check task if enough time has passed
            current_time = time.time()
            if current_time - last_check_time >= check_interval:
                # Check for completed jobs
                try:
                    check_stagehand_sync_results()
                    last_check_time = current_time
                except Exception as e:
                    print(f"\n   ⚠️  Error checking results: {e}")

            # Short sleep to avoid busy waiting
            time.sleep(1)

            # Handle interruption
            if terminate_flag:
                break

        # Final timing
        total_time = time.time() - start_time
        print(f"\n   Total monitoring time: {total_time/60:.1f} minutes")

        # Step 5: Export results
        print("\n📤 Exporting results to CSV...")

        # Refresh shipments from DB
        final_shipments = []
        for s in shipments:
            s.refresh_from_db()
            final_shipments.append(s)

        export_csv = export_shipments_to_csv(final_shipments)

        # Save export CSV
        export_filename = (
            f"stagehand_carriers_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )
        with open(export_filename, "w") as f:
            f.write(export_csv)
        print(f"   Saved export CSV as: {export_filename}")

        # Step 6: Analyze results including sync statistics
        print("\n📊 Analyzing results...")

        # Get final sync statistics
        final_syncs = ShipmentSync.objects.filter(
            shipment__in=shipments
        ).select_related("shipment")

        sync_stats = {
            "total": final_syncs.count(),
            "success": final_syncs.filter(
                status=ShipmentSyncStatus.SUCCESS.value
            ).count(),
            "failed": final_syncs.filter(
                status=ShipmentSyncStatus.FAILED.value
            ).count(),
            "average_duration": 0,
            "failed_shipments": [],
        }

        # Calculate average duration for successful syncs
        successful_syncs = final_syncs.filter(status=ShipmentSyncStatus.SUCCESS.value)
        if successful_syncs:
            durations = [
                sync.duration_seconds
                for sync in successful_syncs
                if sync.duration_seconds
            ]
            if durations:
                sync_stats["average_duration"] = sum(durations) / len(durations)

        # Collect failed shipments
        failed_syncs = final_syncs.filter(status=ShipmentSyncStatus.FAILED.value)
        for sync in failed_syncs:
            sync_stats["failed_shipments"].append(
                {
                    "carrier": sync.shipment.carrier_type,
                    "bol": sync.shipment.bol_number,
                    "error": sync.error_message,
                }
            )

        # Group shipments by carrier for analysis
        carrier_shipments = {}
        for shipment in final_shipments:
            carrier = shipment.carrier_type.lower()
            if carrier not in carrier_shipments:
                carrier_shipments[carrier] = []
            carrier_shipments[carrier].append(shipment)

        # Analyze each carrier
        all_results = {}
        total_shipments = 0
        total_updated = 0
        total_with_eta = 0
        total_with_ports = 0
        total_delivered = 0

        for carrier_type in carriers_to_test:
            if carrier_type in carrier_shipments:
                # Get initial and final shipments for this carrier
                carrier_initial = [
                    s
                    for s in initial_shipments
                    if s.carrier_type.lower() == carrier_type
                ]
                carrier_final = carrier_shipments[carrier_type]

                results = analyze_results(carrier_initial, carrier_final)
                all_results[carrier_type] = results

                total_shipments += results["total"]
                total_updated += results["updated"]
                total_with_eta += results["with_eta"]
                total_with_ports += results["with_ports"]
                total_delivered += results["delivered"]

        # Print combined summary
        print(f"\n\n{'='*60}")
        print("COMBINED RESULTS - ALL CARRIERS")
        print(f"{'='*60}")

        # Sync statistics
        print("\nAsync Job Statistics:")
        print(f"  - Total sync jobs: {sync_stats['total']}")
        print(
            f"  - Successful: {sync_stats['success']} ({sync_stats['success']/sync_stats['total']*100:.1f}%)"
            if sync_stats["total"] > 0
            else "  - Successful: 0"
        )
        print(
            f"  - Failed: {sync_stats['failed']} ({sync_stats['failed']/sync_stats['total']*100:.1f}%)"
            if sync_stats["total"] > 0
            else "  - Failed: 0"
        )
        if sync_stats["average_duration"] > 0:
            print(f"  - Average duration: {sync_stats['average_duration']:.1f} seconds")

        print("\nShipment Update Statistics:")
        print(f"Carriers tested: {len(all_results)}")
        print(f"Total shipments: {total_shipments}")
        print(
            f"Successfully updated: {total_updated} ({total_updated/total_shipments*100:.1f}%)"
            if total_shipments > 0
            else "Successfully updated: 0"
        )
        print(
            f"Failed to update: {total_shipments - total_updated} ({(total_shipments - total_updated)/total_shipments*100:.1f}%)"
            if total_shipments > 0
            else "Failed to update: 0"
        )

        print("\nAcross all carriers:")
        print(f"  - Shipments with ETA: {total_with_eta}")
        print(f"  - Shipments with port info: {total_with_ports}")
        print(f"  - Delivered shipments: {total_delivered}")

        print("\nPer-carrier summary:")
        for carrier, results in all_results.items():
            carrier_name = CARRIER_TEST_DATA[carrier]["name"]
            print(f"\n  {carrier_name}:")
            print(f"    - Total: {results['total']}")
            print(
                f"    - Updated: {results['updated']} ({results['updated']/results['total']*100:.1f}%)"
                if results["total"] > 0
                else "    - Updated: 0"
            )
            print(f"    - With ETA: {results['with_eta']}")
            print(f"    - With ports: {results['with_ports']}")
            print(f"    - Delivered: {results['delivered']}")

        # Print failed shipments if any
        if sync_stats["failed_shipments"]:
            print(
                f"\n\n❌ Failed Sync Jobs ({len(sync_stats['failed_shipments'])} total):"
            )
            for idx, failed in enumerate(
                sync_stats["failed_shipments"][:10], 1
            ):  # Show max 10
                print(f"  {idx}. {failed['carrier']} - {failed['bol']}")
                print(f"     Error: {failed['error']}")
            if len(sync_stats["failed_shipments"]) > 10:
                print(f"  ... and {len(sync_stats['failed_shipments']) - 10} more")

    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error during test: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # Final cleanup
        if not terminate_flag:
            print("\n\n🧹 Cleaning up all test shipments...")
            total_deleted = 0
            for carrier, tracking_codes in cleanup_tracking_codes.items():
                if tracking_codes:
                    deleted = Shipment.objects.filter(
                        team=team, bol_number__in=tracking_codes
                    ).delete()[0]
                    total_deleted += deleted
            print(f"   Deleted {total_deleted} test shipments")

        print("\n✅ All tests complete!")


def main():
    """Main entry point."""
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Parse arguments
    parser = argparse.ArgumentParser(
        description="Test Stagehand-based carrier tracking (ZIM, ONE, MSC, COSCO)"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run quick test with only 1-2 tracking codes per carrier",
    )
    parser.add_argument(
        "--cleanup-csv",
        action="store_true",
        help="Clean up all CSV files generated by this script and exit",
    )

    args = parser.parse_args()

    # Handle CSV cleanup
    if args.cleanup_csv:
        print("🧹 Cleaning up CSV files...")

        # Find all CSV files matching our patterns
        patterns = [
            "stagehand_carriers_import_*.csv",
            "stagehand_carriers_export_*.csv",
        ]

        csv_files = []
        for pattern in patterns:
            csv_files.extend(glob.glob(pattern))

        # Remove duplicates
        csv_files = list(set(csv_files))

        if not csv_files:
            print("   No CSV files found to clean up")
        else:
            print(f"   Found {len(csv_files)} CSV files:")
            for file in sorted(csv_files):
                print(f"     - {file}")

            # Confirm deletion
            response = input("\n   Delete these files? (y/N): ").strip().lower()
            if response == "y":
                deleted_count = 0
                for file in csv_files:
                    try:
                        os.remove(file)
                        deleted_count += 1
                    except Exception as e:
                        print(f"     Error deleting {file}: {e}")
                print(f"\n   ✅ Deleted {deleted_count} CSV files")
            else:
                print("   Cleanup cancelled")

        return

    # Run the multi-carrier test
    print("\n💡 Press Ctrl+C to stop\n")
    run_all_carriers_test(quick_mode=args.quick)


if __name__ == "__main__":
    main()
