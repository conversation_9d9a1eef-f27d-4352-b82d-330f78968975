#!/bin/bash
# Run from the root of the project directory
repo="855701137767.dkr.ecr.us-east-2.amazonaws.com"

docker login -u AWS -p $(aws ecr get-login-password --region us-east-2) $repo

usage() {
    echo "Usage: $0 --web|--worker <version> [--deploy-staging-service]"
    exit 1
}

if [ "$#" -lt 2 ]; then
    usage
fi

flag=$1
version=$2
deploy_staging=$3

if [ "$flag" != "--web" ] && [ "$flag" != "--worker" ]; then
    echo "Invalid flag. Please specify either --web or --worker."
    usage
fi

image=""
service=""

if [ "$flag" == "--web" ]; then
    image="didero-api"
    service="web"
elif [ "$flag" == "--worker" ]; then
    image="didero-worker"
    service="worker"
fi

# Create and push the image
docker build -t didero-base:latest -f docker/base.Dockerfile .
docker build -t $image:latest -f docker/$service.Dockerfile .

echo "Pushing $repo/$image:$version to ECR..."
docker tag $image:latest $repo/$image:latest
docker tag $image:latest $repo/$image:$version
docker push $repo/$image:latest
docker push $repo/$image:$version

# Deploy and point to the new image
if [ "$deploy_staging" == "--deploy-staging-service" ]; then
    echo "Updating ECS to use new image"
    aws ecs update-service --cluster arn:aws:ecs:us-east-2:855701137767:cluster/didero-staging-cluster --service arn:aws:ecs:us-east-2:855701137767:service/didero-staging-cluster/didero-service-staging --force-new-deployment
fi


':
Below is a new version that will enable multiple workers / images, to support multiple queues.
For now, however, we will keep one worker that runs both celery beat and all the queues.
'

# usage() {
#     echo "Usage: $0 --web|--celery-default-worker|--celery-beat|--celery-email-import|--build-all <version> [--deploy-staging-service]"
#     exit 1
# }

# if [ "$#" -lt 2 ]; then
#     usage
# fi

# flag=$1
# version=$2
# deploy_staging=$3

# if [ "$flag" != "--web" ] && [ "$flag" != "--celery-default-worker" ] && [ "$flag" != "--celery-beat" ] && [ "$flag" != "--celery-email-import" ] && [ "$flag" != "--build-all" ]; then
#     echo "Invalid flag. Please specify either --web, --celery-default-worker, --celery-beat, --celery-email-import, or --build-all."
#     usage
# fi

# build_and_push() {
#     local image=$1
#     local service=$2
#     local version=$3

#     # Create and push the image
#     docker build -t $image:latest -f docker/$service.Dockerfile .

#     echo "Pushing $repo/$image:$version to ECR..."
#     docker tag $image:latest $repo/$image:latest
#     docker tag $image:latest $repo/$image:$version
#     docker push $repo/$image:latest
#     docker push $repo/$image:$version
# }

# image=""
# service=""

# # Always build the base image
# if [ "$flag" == "--build-all" ] || [ "$flag" == "--web" ] || [ "$flag" == "--celery-default-worker" ] || [ "$flag" == "--celery-beat" ] || [ "$flag" == "--celery-email-import" ]; then
#     docker build -t didero-base:latest -f docker/base.Dockerfile .
# fi

# if [ "$flag" == "--web" ]; then
#     image="didero-api"
#     service="web"
#     build_and_push $image $service $version
# elif [ "$flag" == "--celery-default-worker" ]; then
#     image="didero-celery-default-worker"
#     service="worker"
#     build_and_push $image $service $version
# elif [ "$flag" == "--celery-beat" ]; then
#     image="didero-celery-beat"
#     service="worker"
#     build_and_push $image $service $version
# elif [ "$flag" == "--celery-email-import" ]; then
#     image="didero-celery-email-import"
#     service="worker"
#     build_and_push $image $service $version
# elif [ "$flag" == "--build-all" ]; then
#     build_and_push "didero-api" "web" $version
#     build_and_push "didero-celery-default-worker" "worker" $version
#     build_and_push "didero-celery-beat" "worker" $version
#     build_and_push "didero-celery-email-import" "worker" $version
# fi

# # Deploy and point to the new image
# if [ "$deploy_staging" == "--deploy-staging-service" ]; then
#     echo "Updating ECS to use new image"
#     aws ecs update-service --cluster arn:aws:ecs:us-east-2:855701137767:cluster/didero-staging-cluster --service arn:aws:ecs:us-east-2:855701137767:service/didero-staging-cluster/didero-service-staging --force-new-deployment
# fi
