# Agent-Based Email Categorization System - Entity Relationship Diagram

## Core Entity Definitions

```mermaid
erDiagram
    EMAIL {
        string email_id PK
        string thread_id FK
        string from_address
        string to_address
        datetime received_at
        string subject
        text body_text
        json headers
        string status
        datetime processed_at
    }

    THREAD {
        string thread_id PK
        string original_email_id
        datetime created_at
        text thread_context
        int email_count
    }

    ATTACHMENT {
        string attachment_id PK
        string email_id FK
        string document_type
        string file_name
        binary raw_file
        string storage_path
        int file_size
        datetime uploaded_at
    }

    EXTRACTED_DOCUMENT {
        string document_id PK
        string attachment_id FK
        string email_id FK
        text extracted_text
        string extraction_method
        float confidence_score
        json metadata
        datetime extracted_at
    }

    REFERENCE {
        string reference_id PK
        string email_id FK
        string number_type
        string number_value
        float confidence
        string source_location
        datetime extracted_at
    }

    PURCHASE_ORDER {
        string po_id PK
        string po_number UK
        string customer_id FK
        string status
        decimal total_amount
        datetime created_at
        datetime issued_at
        json po_data
    }

    SHIPMENT {
        string shipment_id PK
        string po_id FK
        string tracking_number
        string status
        datetime shipped_at
        datetime expected_delivery
        json shipment_details
    }

    INVOICE {
        string invoice_id PK
        string invoice_number UK
        string po_id FK
        decimal amount
        string status
        datetime issued_at
        datetime due_date
        json invoice_data
    }

    CUSTOMER {
        string customer_id PK
        string customer_name
        json field_mappings
        json business_rules
        boolean active
        datetime created_at
    }

    WORKFLOW {
        string workflow_id PK
        string workflow_type
        string trigger_condition
        json workflow_config
        boolean active
    }

    WORKFLOW_EXECUTION {
        string execution_id PK
        string workflow_id FK
        string email_id FK
        string reference_id FK
        string status
        json execution_context
        datetime started_at
        datetime completed_at
    }

    TASK {
        string task_id PK
        string email_id FK
        string task_type
        string status
        json full_context
        string assigned_to
        datetime created_at
        datetime due_date
        datetime completed_at
    }

    DATABASE_UPDATE {
        string update_id PK
        string email_id FK
        string model_name
        string field_name
        text old_value
        text new_value
        string customer_id FK
        datetime updated_at
    }

    PROCESSING_LOG {
        string log_id PK
        string email_id FK
        string phase
        string tool_name
        json input_data
        json output_data
        string status
        text error_message
        datetime processed_at
    }
```

## Entity Relationships

```mermaid
erDiagram
    EMAIL ||--o{ ATTACHMENT : has
    EMAIL }o--|| THREAD : belongs_to
    EMAIL ||--o{ EXTRACTED_DOCUMENT : generates
    EMAIL ||--o{ REFERENCE : contains
    EMAIL ||--o{ WORKFLOW_EXECUTION : triggers
    EMAIL ||--o{ TASK : creates
    EMAIL ||--o{ DATABASE_UPDATE : initiates
    EMAIL ||--o{ PROCESSING_LOG : logs

    THREAD ||--o{ EMAIL : contains

    ATTACHMENT ||--o| EXTRACTED_DOCUMENT : produces

    REFERENCE }o--o| PURCHASE_ORDER : matches
    REFERENCE }o--o| INVOICE : matches
    REFERENCE }o--o| SHIPMENT : matches
    REFERENCE ||--o{ WORKFLOW_EXECUTION : triggers

    PURCHASE_ORDER ||--o{ SHIPMENT : has
    PURCHASE_ORDER ||--o{ INVOICE : has
    PURCHASE_ORDER }o--|| CUSTOMER : belongs_to

    CUSTOMER ||--o{ DATABASE_UPDATE : defines_rules
    CUSTOMER ||--o{ WORKFLOW : configures

    WORKFLOW ||--o{ WORKFLOW_EXECUTION : executes

    INVOICE }o--|| CUSTOMER : belongs_to
    SHIPMENT }o--|| CUSTOMER : belongs_to
```

## Key Relationships Explained

### Email-Centric Relationships
- **EMAIL → THREAD**: Many emails belong to one thread (conversation history)
- **EMAIL → ATTACHMENT**: One email can have many attachments
- **EMAIL → EXTRACTED_DOCUMENT**: Email processing generates extracted documents
- **EMAIL → REFERENCE**: One email can contain multiple PO/Invoice references
- **EMAIL → PROCESSING_LOG**: Each email has multiple processing log entries (one per phase)

### Document Processing Flow
- **ATTACHMENT → EXTRACTED_DOCUMENT**: Each attachment is processed to extract text
- **EXTRACTED_DOCUMENT → REFERENCE**: Extracted text is analyzed to find references

### Business Entity Relationships
- **REFERENCE → PURCHASE_ORDER/INVOICE/SHIPMENT**: References are matched to existing business entities
- **PURCHASE_ORDER → SHIPMENT**: One PO can have multiple shipments
- **PURCHASE_ORDER → INVOICE**: One PO can have multiple invoices
- **CUSTOMER → PURCHASE_ORDER/INVOICE/SHIPMENT**: All business entities belong to a customer

### Action Relationships
- **EMAIL/REFERENCE → WORKFLOW_EXECUTION**: Emails and references trigger workflow executions
- **EMAIL → TASK**: Unhandled emails create tasks for manual review
- **EMAIL → DATABASE_UPDATE**: Emails can initiate database updates
- **CUSTOMER → WORKFLOW**: Customers have specific workflow configurations

## Processing Phases Data Flow

### Phase 1: Document Extraction
```
ATTACHMENT → EXTRACTED_DOCUMENT
- Input: raw_file, document_type
- Output: document_id, extracted_text, metadata
```

### Phase 2: Thread Enrichment
```
EMAIL → THREAD → EMAIL[]
- Input: email_id
- Output: thread_id, all thread emails, thread_context
```

### Phase 3: Reference Extraction (The Umbrella)
```
EXTRACTED_DOCUMENT + EMAIL.body_text → REFERENCE[]
- Input: Combined text from all sources
- Output: Array of references with type, number, confidence, source
```

### Phase 4: Database Context Fetching
```
REFERENCE → PURCHASE_ORDER | SHIPMENT | INVOICE
- Input: reference_numbers, number_type
- Output: exists, model_name, object_json
```

### Phase 5: Status Check & Decision
```
(PURCHASE_ORDER | SHIPMENT | INVOICE) → (WORKFLOW_EXECUTION | DATABASE_UPDATE | TASK)
- Input: Model data, status, business rules
- Output: Action type and parameters
```

## Key Attributes by Entity

### Critical Identifiers
- **EMAIL**: email_id (PK), thread_id (FK)
- **REFERENCE**: reference_id (PK), number_type, number_value
- **PURCHASE_ORDER**: po_id (PK), po_number (unique)
- **INVOICE**: invoice_id (PK), invoice_number (unique)
- **SHIPMENT**: shipment_id (PK), tracking_number

### Status Fields (for workflow decisions)
- **EMAIL.status**: pending, processed, failed
- **PURCHASE_ORDER.status**: ISSUED, AWAITING_SHIPMENT, SHIPPED, COMPLETED
- **SHIPMENT.status**: PENDING, IN_TRANSIT, DELIVERED
- **INVOICE.status**: DRAFT, SENT, PAID, OVERDUE
- **TASK.status**: PENDING, IN_PROGRESS, COMPLETED

### Confidence & Validation
- **EXTRACTED_DOCUMENT.confidence_score**: OCR/extraction confidence
- **REFERENCE.confidence**: Reference extraction confidence

### Audit & Tracking
- **PROCESSING_LOG**: Complete audit trail of all processing phases
- **DATABASE_UPDATE**: Track all database modifications
- **WORKFLOW_EXECUTION**: Track all workflow runs

## Customer-Specific Configuration

The **CUSTOMER** entity contains:
- **field_mappings**: JSON object defining how fields map for this customer
- **business_rules**: JSON object defining customer-specific business logic
- These configurations allow the system to adapt without code changes

## The Umbrella Pattern

The **REFERENCE** entity implements the "Umbrella" pattern:
- Aggregates all reference numbers found across email and attachments
- Provides a single point for matching against business entities
- Maintains source tracking for audit purposes

## Notes

1. **Scalability**: The design supports horizontal scaling by partitioning on email_id or customer_id
2. **Flexibility**: JSON fields (metadata, context, config) allow schema evolution without migrations
3. **Auditability**: Complete processing trail through PROCESSING_LOG entity
4. **Fault Tolerance**: Failed processing creates TASK entries for manual review
5. **Multi-tenancy**: Customer-specific configurations enable SaaS deployment