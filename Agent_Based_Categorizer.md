# Agent-Based Email Categorization System

## Enhanced Tool Flow Diagram with Input/Output Specifications

```mermaid
flowchart TB
    %% Input Layer
    subgraph Inputs["📥 Input Sources"]
        Email["📧 Email<br/>- Body text<br/>- Headers<br/>- Email ID"]
        Attachments["📎 Attachments<br/>- PDFs<br/>- CSVs (templated/random)<br/>- Images<br/>- Word/Excel docs"]
    end

    %% Central Orchestrator
    subgraph Orchestra["🎭 Orchestration Layer"]
        CA["🧠 Context Agent<br/>Central Orchestrator<br/>- Manages tool sequence<br/>- Accumulates context<br/>- Makes decisions"]
    end

    %% Tool 1: Extraction
    subgraph ExtractionPhase["Phase 1: Document Extraction"]
        ExtInput["📄 INPUT<br/>- document: raw file<br/>- document_type: PDF/CSV/IMG/etc"]
        ExtTool["🔧 Extraction Tool<br/>- OCR (Mistral/Alternative)<br/>- CSV parsing<br/>- Text extraction"]
        ExtOutput["📤 OUTPUT<br/>{<br/>  document_id: string,<br/>  extracted_text: string,<br/>  metadata: {<br/>    document_type: string,<br/>    extraction_method: string,<br/>    confidence: float<br/>  }<br/>}"]
        
        ExtInput --> ExtTool
        ExtTool --> ExtOutput
    end

    %% Tool 2: Thread Enrichment
    subgraph ThreadPhase["Phase 2: Thread Enrichment"]
        ThreadInput["📥 INPUT<br/>- email_id: string"]
        ThreadTool["🔗 Thread Enrichment Tool<br/>- Fetch thread ID<br/>- Retrieve all thread emails<br/>- Extract attachments"]
        ThreadOutput["📤 OUTPUT<br/>{<br/>  thread_id: string,<br/>  emails: [{<br/>    email_id: string,<br/>    extracted_text: string,<br/>    attachments: [string]<br/>  }],<br/>  thread_context: string<br/>}"]
        
        ThreadInput --> ThreadTool
        ThreadTool --> ThreadOutput
    end

    %% Tool 3: Reference Extraction
    subgraph RefPhase["Phase 3: Reference Extraction"]
        RefInput["📥 INPUT<br/>- Combined text from:<br/>  - Email body<br/>  - All attachments<br/>  - Thread context"]
        RefTool["🔍 Reference Extraction Tool<br/>- Pattern matching<br/>- PO number extraction<br/>- Invoice number extraction<br/>- Validation"]
        RefOutput["📤 OUTPUT (The Umbrella)<br/>[{<br/>  email_id: string,<br/>  references: [{<br/>    number_type: 'PO'|'INVOICE',<br/>    number: string,<br/>    confidence: float,<br/>    source: string<br/>  }]<br/>}]"]
        
        RefInput --> RefTool
        RefTool --> RefOutput
    end

    %% Tool 4: Context Fetching
    subgraph FetchPhase["Phase 4: Database Context"]
        FetchInput["📥 INPUT<br/>- reference_numbers: [string]<br/>- number_type: 'PO'|'INVOICE'"]
        FetchTool["🗄️ Fetch Context Tool<br/>- Query PurchaseOrder models<br/>- Query Shipment models<br/>- Query Invoice models"]
        FetchOutput["📤 OUTPUT<br/>{<br/>  exists: boolean,<br/>  model_name: 'PurchaseOrder'|<br/>              'Shipment'|'Invoice',<br/>  object_json: {<br/>    // Full model data<br/>  }<br/>}"]
        
        FetchInput --> FetchTool
        FetchTool --> FetchOutput
    end

    %% Tool 5: Status Check & Decision
    subgraph StatusPhase["Phase 5: Status & Decision"]
        StatusInput["📥 INPUT<br/>- PO/Invoice model data<br/>- Current status<br/>- Business rules"]
        StatusTool["✅ Status Check Tool<br/>- Analyze current state<br/>- Apply business logic<br/>- Determine actions"]
        StatusOutput["📤 OUTPUT<br/>Decision Type:<br/>- Workflow trigger<br/>- Database update<br/>- Task creation"]
        
        StatusInput --> StatusTool
        StatusTool --> StatusOutput
    end

    %% Data Flow Connections
    Email --> CA
    Attachments --> CA
    
    CA -.->|1. Process docs| ExtInput
    ExtOutput -.->|Text pool| CA
    
    CA -.->|2. Get thread| ThreadInput
    ThreadOutput -.->|Context| CA
    
    CA -.->|3. Extract refs| RefInput
    RefOutput -.->|Umbrella| CA
    
    CA -.->|4. Fetch models| FetchInput
    FetchOutput -.->|Model data| CA
    
    CA -.->|5. Check status| StatusInput
    
    %% Final Actions
    subgraph Actions["🎯 Action Layer"]
        Workflow["📋 Workflow Triggers<br/>- PO Acknowledgment (status='ISSUED')<br/>- Shipment Processing (status='AWAITING_SHIPMENT')<br/>- Invoice Matching (invoice exists)"]
        Database["💾 Database Updates<br/>- Model: specific entity<br/>- Field: attribute to update<br/>- Value: new value<br/>- Customer mapping: specific rules"]
        Task["📝 Task Creation<br/>- Full context preserved<br/>- Manual review required<br/>- Complex/unknown scenarios"]
    end
    
    StatusOutput --> Workflow
    StatusOutput --> Database
    StatusOutput --> Task

    %% Feedback to database
    Workflow -.->|Update status| DB[(Database)]
    Database -.->|Apply changes| DB
    Task -.->|Queue for review| DB

    %% Styling for clarity
    style CA fill:#e1bee7,stroke:#6a1b9a,stroke-width:3px
    style ExtTool fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    style ThreadTool fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    style RefTool fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    style FetchTool fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    style StatusTool fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    style RefOutput fill:#bbdefb,stroke:#1976d2,stroke-width:3px
    style Workflow fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    style Database fill:#fff9c4,stroke:#f57c00,stroke-width:2px
    style Task fill:#ffebee,stroke:#c62828,stroke-width:2px

    classDef inputStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:1px,color:#000
    classDef outputStyle fill:#e3f2fd,stroke:#1565c0,stroke-width:1px,color:#000
    
    class ExtInput,ThreadInput,RefInput,FetchInput,StatusInput inputStyle
    class ExtOutput,ThreadOutput,RefOutput,FetchOutput,StatusOutput outputStyle
```

## System Architecture Overview

### Three-Pillar Foundation

The agent-based email categorization system is built on three fundamental pillars:

1. **The Context Agent**: Central orchestrator that manages the flow of information and decision-making
2. **The Tools**: Specialized functions that extract, enrich, validate, and decide on actions
3. **Tool Outputs as Inputs**: Each tool's output becomes available as input to other tools, creating a rich, accumulating context

### Tool Input/Output Quick Reference

| Tool | Input | Output | Purpose |
|------|-------|--------|---------|
| **Extraction Tool** | • document (raw file)<br/>• document_type | • document_id<br/>• extracted_text<br/>• metadata (type, method, confidence) | Convert any document format to searchable text |
| **Thread Enrichment** | • email_id | • thread_id<br/>• all thread emails<br/>• combined thread_context | Gather full conversation history |
| **Reference Extraction** | • combined text from all sources | • email_id<br/>• references array (type, number, confidence, source) | Extract and validate PO/Invoice numbers |
| **Fetch Context** | • reference_numbers<br/>• number_type | • exists (boolean)<br/>• model_name<br/>• full object_json | Retrieve database records |
| **Status Check** | • model data<br/>• current status<br/>• business rules | • action type (workflow/update/task)<br/>• specific parameters | Determine appropriate actions |

## Processing Phases Detailed

### Phase 1: Document Extraction

Processes all input documents using appropriate extraction methods:

**Input Schema:**
```python
{
    "document": raw_file,  # PDF, CSV, image, etc.
    "document_type": str    # Type identifier for processing
}
```

**Processing Methods:**
- PDFs and images: Mistral OCR or alternative OCR engines
- CSVs: Structured data parsing or text extraction from unstructured formats
- Emails: Body text and metadata extraction

**Output Schema:**
```python
{
    "document_id": str,
    "extracted_text": str,
    "metadata": {
        "document_type": str,
        "extraction_method": str,
        "confidence": float
    }
}
```

### Phase 2: Thread Enrichment

Retrieves full conversation context using the email ID:

**Input Schema:**
```python
{
    "email_id": str  # Unique identifier of current email
}
```

**Processing Steps:**
1. Fetch thread ID associated with the email
2. Retrieve all emails in the thread
3. Extract text from each email and its attachments

**Output Schema:**
```python
{
    "thread_id": str,
    "emails": [
        {
            "email_id": str,
            "extracted_text": str,
            "attachments": List[str]
        }
    ],
    "thread_context": str  # Combined context from all emails
}
```

### Phase 3: Reference Extraction (The Umbrella)

Extracts and validates reference numbers from all available text:

**Input:**
- Combined text from email body, attachments, and thread context

**Processing:**
- Pattern matching for PO numbers
- Pattern matching for invoice numbers
- Validation of extracted numbers

**Output Schema (The "Umbrella"):**
```python
[
    {
        "email_id": str,
        "references": [
            {
                "number_type": "PO" | "INVOICE",
                "number": str,
                "confidence": float,
                "source": str  # Where it was found
            }
        ]
    }
]
```

> **Note:** This structure is called the "Umbrella" because it encompasses all reference numbers found across the email and its attachments.

### Phase 4: Database Context Fetching

Retrieves existing database records for extracted references:

**Input Schema:**
```python
{
    "reference_numbers": List[str],
    "number_type": "PO" | "INVOICE"
}
```

**Database Queries:**
- Purchase Order (PO) models
- Shipment models
- Invoice models

**Output Schema:**
```python
{
    "exists": bool,
    "model_name": "PurchaseOrder" | "Shipment" | "Invoice",
    "object_json": {
        # Full model data as JSON
    }
}
```

### Phase 5: Status Checking and Decision Making

Analyzes database objects and determines appropriate actions:

**Processing:**
1. Check current status from the model
2. Apply business rules
3. Determine available actions

**Action Categories:**

#### 1. Workflow Selection
Triggers specific workflows based on current state:
- PO status = "ISSUED" → Trigger acknowledgment workflow
- Status = "AWAITING_SHIPMENT" → Trigger shipment workflow
- Invoice exists → Trigger invoice matching workflow

#### 2. Database Updates
Direct field updates for specific models:
```python
{
    "model": str,            # Entity to update
    "field": str,            # Attribute to modify
    "value": Any,            # New value
    "customer_mapping": str  # Customer-specific rules
}
```

#### 3. Task Creation
For unhandled scenarios requiring manual review:
- No existing workflow matches
- Manual intervention required
- Complex/unknown scenarios
- Full context preserved for human review

## Key Architectural Benefits

1. **Clear Data Flow**: Each tool has well-defined inputs and outputs, making the system predictable and debuggable

2. **The Umbrella Pattern**: Centralizes all reference numbers in a single structure for efficient processing

3. **Accumulating Context**: Each phase adds to the context pool, enabling smarter decisions

4. **Flexible Actions**: Three distinct output paths (workflows, updates, tasks) handle all scenarios

5. **Customer Adaptability**: Field mappings and business rules can be customized per customer without code changes

6. **Graceful Degradation**: When the system cannot determine the appropriate action, it creates a task for manual review rather than failing silently

## Input Types Supported

The system accepts comprehensive input types:

- **Email Text**: Raw email body content with headers and metadata
- **PDF Documents**: Purchase orders, invoices, shipping documents
- **CSV Files**: Both templated (structured) and random (unstructured) formats
- **Images**: Screenshots, scanned documents, photos of receipts/invoices
- **Other Formats**: Word documents, Excel spreadsheets, and more

## Implementation Notes

This architecture transforms email processing from a linear pipeline into an intelligent, context-aware system that:
- Adapts its behavior based on accumulated information from multiple sources
- Maintains full traceability of decisions
- Provides multiple fallback mechanisms
- Scales with customer-specific requirements without code changes